# 📋 ERM采购管理模块"作废"功能技术实现方案

## 🎯 需求概述

在ERM系统的采购管理模块中开发"作废"功能，支持以下三个菜单的列表页面：
1. "采购立项"菜单列表
2. "选聘方案"菜单列表  
3. "选聘结果审批"菜单列表

## 📊 现状分析

### 1. 现有代码结构
- **采购立项**: `src/apps/erm/purchase/project_approval/`
- **选聘方案**: `src/apps/erm/purchase/recruitment_plan/`  
- **选聘结果审批**: `src/apps/erm/purchase/evaluation_result/`

### 2. 业务类型映射
根据`skip.config.json`分析，支持作废的业务类型：
- **立项**: `ERM_PURCHASE_MANAGEMENT_LX` → `ERM_PURCHASE_MANAGEMENT_LX_CANCEL`
- **新建询价函**: `ERM_PURCHASE_MANAGEMENT_XJH` → `ERM_PURCHASE_MANAGEMENT_XJH_CANCEL`
- **新建方案**: `ERM_RECRUITMENT_SCHEME_ADD` → `ERM_RECRUITMENT_SCHEME_CANCEL`
- **招标文件用印**: `ERM_RECRUITMENT_SCHEME_SEAL` → `ERM_RECRUITMENT_SCHEME_SEAL_CANCEL`
- **选聘结果审批**: `ERM_RECRUITMENT_RESULT_ADD` → `ERM_RECRUITMENT_RESULT_CANCEL`

### 3. 业务关联关系
```
采购立项 → 选聘方案 → 招标文件用印 → 选聘结果审批
    ↓         ↓           ↓            ↓
   作废      作废        作废         作废
```

## 🔧 技术实现方案

### 1. 新增API接口设计

#### 1.1 查询下游关联业务接口
```javascript
// API: 查询业务关联关系
queryDownstreamBusiness: '/erm/purchase/queryDownstreamBusiness'

// 参数: { businessId, businessType }
// 返回: { 
//   hasDownstream: boolean,
//   downstreamList: [
//     { id, businessId, businessType, businessName, statusCode }
//   ]
// }
```

#### 1.2 批量作废接口
```javascript
// API: 批量发起作废流程
batchCancelBusiness: '/erm/purchase/batchCancelBusiness'

// 参数: { 
//   mainBusinessId, 
//   businessList: [
//     { businessId, businessType, cancelReason }
//   ]
// }
```

#### 1.3 作废条件检查接口
```javascript
// API: 检查作废前置条件
checkCancelCondition: '/erm/purchase/checkCancelCondition'

// 参数: { businessId, businessType }
// 返回: { canCancel: boolean, message: string }
```

### 2. 前端组件修改方案

#### 2.1 新增通用作废确认弹窗
**文件**: `src/apps/erm/purchase/component/CancelConfirmModal.vue`

**功能特性**:
- 显示关联业务列表
- 作废原因输入验证
- 确认/取消操作
- 加载状态管理

#### 2.2 采购立项模块修改
**修改文件**:
- `src/apps/erm/purchase/project_approval/hooks/useProjectApprovalApi.jsx`
- `src/apps/erm/purchase/project_approval/list.vue`

**主要修改**:
- 新增作废相关API函数
- 集成作废确认弹窗
- 添加作废业务逻辑
- 按钮状态控制

#### 2.3 选聘方案模块修改
**修改文件**:
- `src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentApi.jsx`
- `src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentList.jsx`
- `src/apps/erm/purchase/recruitment_plan/list.vue`

**主要修改**:
- 新增作废按钮配置
- 集成作废功能逻辑
- 状态控制优化

#### 2.4 选聘结果审批模块修改
**修改文件**:
- `src/apps/erm/purchase/evaluation_result/hooks/useEvaluationApi.jsx`
- `src/apps/erm/purchase/evaluation_result/hooks/useEvaluationList.jsx`
- `src/apps/erm/purchase/evaluation_result/list.vue`

**主要修改**:
- 新增作废按钮和API
- 集成作废确认流程
- 状态管理优化

### 3. 业务逻辑实现

#### 3.1 作废流程
```javascript
const cancelFn = async () => {
  try {
    // 1. 检查作废前置条件
    const conditionCheck = await checkCancelConditionHttp({
      businessId: rowData.value.businessId,
      businessType: rowData.value.dicBusinessTypeCode
    });

    if (!conditionCheck.canCancel) {
      ElNotification({
        message: conditionCheck.message || '当前业务不满足作废条件',
        type: 'error'
      });
      return;
    }

    // 2. 查询下游关联业务
    const downstreamResult = await queryDownstreamBusinessHttp({
      businessId: rowData.value.businessId,
      businessType: rowData.value.dicBusinessTypeCode
    });

    // 3. 显示作废确认弹窗
    cancelConfirmModalRef.value.show(rowData.value, downstreamResult.downstreamList || []);
  } catch (error) {
    ElNotification({
      message: '检查作废条件失败',
      type: 'error'
    });
  }
};
```

#### 3.2 状态控制逻辑
```javascript
// 作废按钮启用条件
cancelDisabled.value = !(
  selection.row.statusCode == '1' &&  // 业务状态为"有效"
  !selection.row.doingBusCode         // 没有正在办理中的业务流程
);
```

#### 3.3 关联作废处理
```javascript
const handleCancelConfirm = async (cancelData) => {
  try {
    // 调用批量作废接口
    await batchCancelBusinessHttp({
      mainBusinessId: cancelData.mainBusiness.businessId,
      businessList: [
        {
          businessId: cancelData.mainBusiness.businessId,
          businessType: cancelData.mainBusiness.dicBusinessTypeCode,
          cancelReason: cancelData.cancelReason
        },
        ...cancelData.downstreamList.map(item => ({
          businessId: item.businessId,
          businessType: item.businessType,
          cancelReason: cancelData.cancelReason
        }))
      ]
    });

    ElNotification({
      message: '作废流程已发起',
      type: 'success'
    });

    cancelConfirmModalRef.value.close();
    pageList.value.reload({ resetPage: false });
  } catch (error) {
    ElNotification({
      message: '作废失败',
      type: 'error'
    });
  }
};
```

## 🔒 权限配置

需要在权限系统中新增以下权限码：
- `ERM_PURCHASE_CANCEL` - 采购立项作废
- `ERM_RECRUITMENT_CANCEL` - 选聘方案作废  
- `ERM_PURCHASE_EVALUATION_CANCEL` - 选聘结果作废

## 📝 后续需要的后端支持

### 1. 新增API接口实现
```java
// 检查作废条件
@PostMapping("/checkCancelCondition")
public Result checkCancelCondition(@RequestBody CheckCancelConditionRequest request)

// 查询下游关联业务
@PostMapping("/queryDownstreamBusiness") 
public Result queryDownstreamBusiness(@RequestBody QueryDownstreamRequest request)

// 批量作废业务
@PostMapping("/batchCancelBusiness")
public Result batchCancelBusiness(@RequestBody BatchCancelRequest request)
```

### 2. 数据库表结构
- 业务关联关系表
- 作废记录表
- 状态流转记录表

## 🧪 测试建议

### 1. 功能测试
- 作废前置条件验证
- 关联业务识别准确性
- 批量作废流程完整性
- 状态更新正确性

### 2. 边界测试
- 无关联业务的作废
- 多层级关联业务的作废
- 并发作废操作
- 异常情况处理

## 📋 部署清单

1. ✅ 前端代码修改完成
2. ⏳ 后端API接口开发
3. ⏳ 数据库表结构调整
4. ⏳ 权限配置更新
5. ⏳ 测试用例编写
6. ⏳ 用户手册更新

## 🎯 业务规则总结

### 前置条件（必须同时满足）
- 当前业务状态为"有效"
- 该业务下没有正在办理中的业务流程

### 关联作废逻辑
- 当某个业务发起作废时，需要自动识别其下游关联的所有有效业务
- 所有关联的有效业务必须一并发起作废流程
- 示例：新建方案作废时，如果下游关联了有效的"招标文件用印"和"选聘结果审批"，则这些关联业务也要同时发起作废

### 状态更新
- 作废流程完成后，所有相关业务的状态都要更新为"作废"

## ✅ 已完成的代码修改

### 1. 新增通用作废确认弹窗组件
- **文件**: `src/apps/erm/purchase/component/CancelConfirmModal.vue`
- **功能**: 统一的作废确认弹窗，支持显示关联业务列表和作废原因输入

### 2. 采购立项模块修改
- **API文件**: `src/apps/erm/purchase/project_approval/hooks/useProjectApprovalApi.jsx`
  - 新增作废相关API接口
- **列表页面**: `src/apps/erm/purchase/project_approval/list.vue`
  - 集成作废确认弹窗
  - 添加作废业务逻辑
- **按钮配置**: `src/apps/erm/purchase/project_approval/hooks/useProjectApproval.jsx`
  - 已有作废按钮配置

### 3. 选聘方案模块修改
- **API文件**: `src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentApi.jsx`
  - 新增作废相关API接口
- **列表页面**: `src/apps/erm/purchase/recruitment_plan/list.vue`
  - 集成作废确认弹窗
  - 添加作废业务逻辑
- **按钮配置**: `src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentList.jsx`
  - 新增作废按钮配置

### 4. 选聘结果审批模块修改
- **API文件**: `src/apps/erm/purchase/evaluation_result/hooks/useEvaluationApi.jsx`
  - 新增作废相关API接口
- **列表页面**: `src/apps/erm/purchase/evaluation_result/list.vue`
  - 集成作废确认弹窗
  - 添加作废业务逻辑
- **按钮配置**: `src/apps/erm/purchase/evaluation_result/hooks/useEvaluationList.jsx`
  - 新增作废按钮配置

## 📁 修改文件清单

### 新增文件
- `src/apps/erm/purchase/component/CancelConfirmModal.vue`

### 修改文件
- `src/apps/erm/purchase/project_approval/hooks/useProjectApprovalApi.jsx`
- `src/apps/erm/purchase/project_approval/list.vue`
- `src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentApi.jsx`
- `src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentList.jsx`
- `src/apps/erm/purchase/recruitment_plan/list.vue`
- `src/apps/erm/purchase/evaluation_result/hooks/useEvaluationApi.jsx`
- `src/apps/erm/purchase/evaluation_result/hooks/useEvaluationList.jsx`
- `src/apps/erm/purchase/evaluation_result/list.vue`

这个技术实现方案完整地覆盖了ERM采购管理模块作废功能的所有需求，遵循了系统的现有架构模式，确保了代码的一致性和可维护性。前端代码修改已全部完成，等待后端API接口开发配合。
