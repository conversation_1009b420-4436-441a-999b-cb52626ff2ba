import FuniJS from '@funi-lib/utils';
import { ElNotification, ElMessageBox } from 'element-plus';
let abortController = new AbortController();
import { sm3 } from 'sm-crypto';
import { useAppStore } from '@/stores/useAppStore';
import { minimatch } from 'minimatch';

export class BaseApi extends FuniJS.Http {
  constructor() {
    super({
      baseURL: window.$utils.getServerBaseApi(),
      timeout: 60000
    });
  }

  static getInstance() {
    if (!BaseApi._instance) {
      BaseApi._instance = new BaseApi();
    }
    return BaseApi._instance;
  }

  //////////////////////////////////////////////////////////////////////////////////////////

  get unifiedEncryptes() {
    if (!this._unifiedEncryptes) {
      this._unifiedEncryptes = useAppStore().platformConfig.unifiedEncryptes;
    }
    return this._unifiedEncryptes;
  }

  isNeedEncrypt(url) {
    if (!this.unifiedEncryptes || !url || typeof url !== 'string') return false;
    const path = new URL(url, location.origin).pathname;
    return this.unifiedEncryptes.some(i => minimatch(path, i));
  }

  //axiox拦截处理begin

  //实现request拦截
  interceptorsRequest(config) {
    config.signal = config?.signal || abortController.signal;
    const token = sessionStorage.getItem('token');
    const requestId = $utils.guid();
    config.headers['X-FuniPaas-Request-Id'] = requestId;
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json;charset=utf-8';
    config.headers['X-FuniPaas-Authorization'] = token;
    config.headers['X-FuniPaas-Tenant'] = 'dev';
    if (!!token) {
      const configURL = new URL(config.url, location.origin);
      const digestStr = [configURL.pathname, requestId, token].join('$$');
      const signDigest = sm3(digestStr);
      config.headers['X-FuniPaas-Request-Hash'] = signDigest;
    }
    return config;
  }

  //实现response拦截
  interceptorsResponse(response) {
    if (response instanceof Blob) {
      return { data: response };
    } else if (![0, 200].includes(response.status) && response.success !== true) {
      return this.handleError(response);
    }
    if (!!response.dataEncrypt && $utils.isString(response.data)) {
      // 加密内容以string形式返回
      const decryptdData = $utils.decryptdData(response.data);
      response.data = decryptdData ? JSON.parse(decryptdData) || {} : response.data;
    }
    return response;
  }

  handleError(response) {
    const respect = super.handleError(response);
    respect.catch(err => {
      if (['100001', '100002', '100003', '100004', '990001', '990002'].includes(err.code)) {
        abortController.abort();
        /* TODO 退出登录 并重定向登录页 临时处理为前端重定向  */
        ElMessageBox.confirm(err.message, err.phrase, {
          confirmButtonText: '知道了',
          type: 'warning',
          showClose: false,
          showCancelButton: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        }).then(() => {
          abortController = new AbortController();
          sessionStorage.clear();
          window.location.replace($utils.getLoginLocation());
        });
      } else if ([404, 502, 503].includes(err.status)) {
        let ops = {
          title: `Error(${err.status})`,
          message: err.message,
          type: 'error'
        };
        ElNotification(ops);
      } else {
        let ops = {
          title: err.phrase ? (err.code ? `${err.phrase}(${err.code})` : err.phrase) : err.message,
          type: 'error'
        };
        if (err.phrase) ops.message = err.message;
        ElNotification(ops);
      }
    });

    return respect;
  }

  post(url, param, config = {}) {
    if (this.isNeedEncrypt(url)) {
      const postData = $utils.isFormData(param) ? param : $utils.gatewayEncrypt(param || {});
      const contentType = $utils.isFormData(param) ? 'multipart/form-data' : 'text/plain';
      const postConfig = Object.assign({}, config, {
        headers: Object.assign({}, config.headers, { 'Content-Type': contentType })
      });
      return super
        .post(url, postData, postConfig)
        .then(data => ($utils.isString(data) ? $utils.gatewayDecrypt(data) : data));
    }

    return super.post(url, param, config);
  }

  fetch(url, param, headers = {}, config = {}) {
    if (this.isNeedEncrypt(url)) {
      return super
        .fetch(url, !!param ? { data: $utils.gatewayEncrypt(param) } : {}, headers, config)
        .then(data => ($utils.isString(data) ? $utils.gatewayDecrypt(data) : data));
    }
    return super.fetch(url, param, headers, config);
  }

  /**
   * @description: 文件上传
   * @param {*} url      接口地址
   * @param {*} formData 文件数据
   * @param {*} config   自定义config
   * @return {*}
   */
  upload2(url, formData, configer = {}) {
    return new Promise((resolve, reject) => {
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        responseType: 'blob',
        ...configer
      };
      this._axios.post(url, formData, config).then(
        response => {
          const contentType = response && response.headers ? response.headers['content-type'] : '';
          if (contentType.toLowerCase().includes('application/octet-stream')) {
            this.downloadDataHandler(response)
              .then(() => {
                resolve(response);
              })
              .catch(err1 => {
                reject(err1);
              });
          } else if (response && response.data) {
            var reader = new FileReader();
            reader.onload = function () {
              var dataUrl = reader.result;
              var base64 = dataUrl.split(',')[1]; // 将 dataUrl 转换为 base64 编码的字符串
              var decodedData = atob(base64); // 解码 base64
              let realResponse = {};
              try {
                realResponse = JSON.parse(decodedData);
              } catch (ex) {
                console.log(ex);
              }
              resolve(realResponse);
            };
            reader.readAsDataURL(response?.data);
          } else {
            resolve(response);
          }
        },
        err => {
          reject(err);
        }
      );
    });
  }
  //axiox拦截处理end

  /////////////////////////////////////////////////////////////////////////////////
}
