/*
 * @Author: co<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-28 09:53:59
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-01-26 15:48:05
 * @FilePath: /funi-paas-cs-web-cli/src/apis/app/paas.js
 * @Description: userManage 用户管理
 */

const apiUrl = {
  /** 查询当前用户信息 */
  findCurrentUser: '/csccs/user/findCurrentUser',
  /** 查询当前用户信息 */
  getUserInfo: '/csccs/userCenter/getUserInfo',
  /** 根据系统编码查询系统 */
  findSysByCode: '/csccs/sys/findSysByCode',
  /** 获取菜单、页面、权限数据 */
  getSystemMenus: '/csccs/userCenter/getSystemMenus',
  // 日志录制开始
  logRecordStart: '/csccs/logRecord/logRecordStart',
  // 日志录制结束
  logRecordEnd: '/csccs/logRecord/logRecordEnd',
  /** 查询平台配置 */
  findPlatformConfig: '/csccs/platformConfig/findPlatformConfig'
};

/** 查询当前用户信息 */
export function findCurrentUser() {
  return $http.post(apiUrl.findCurrentUser);
}

/** 查询用户信息 */
export function getUserInfo() {
  return $http.post(apiUrl.getUserInfo).then(res => res || {});
}

/**
 * 根据系统编码查询系统
 * @param {string} sysCode 系统编码
 * @returns
 */
export function findSysByCode(sysCode) {
  return $http.fetch(apiUrl.findSysByCode, {
    sysCode
  });
}

/**
 * 获取菜单、页面、权限数据
 * @param {string} sysCode 系统编码
 * @returns {object}
 */
export function getSystemMenus(sysCode) {
  return $http.post(apiUrl.getSystemMenus, {
    code: sysCode
  });
}

/** 日志录制开始 */
export function logRecordStart() {
  return $http.post(apiUrl.logRecordStart);
}

/** 日志录制结束 */
export function logRecordEnd(data) {
  return $http.post(apiUrl.logRecordEnd, data);
}

/** 查询平台配置 */
export function findPlatformConfig() {
  return $http.post(apiUrl.findPlatformConfig).then(res => {
    return JSON.parse((res || {}).platformConfig || '{}');
  });
}
