<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
    <funi-dialog v-model="dialogVisible" size="middle" title="请输入项目详情说明" @close="cancelClick">
      <funi-form @get-form="setForm" label-position="top" :schema="schema" :rules="rules" :col="1" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref, inject } from 'vue';
import { userProjectColumns, useBtnsConfig } from './../hooks/project.jsx';
import { queryBudgetProjectListHttp, deleteHttp, updateDetailHttp } from './../hooks/api.js';
import { getProjectInfo } from '@/apps/erm/budget/hooks/getInfo.js';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
const router = useRouter();
const rowData = ref(void 0);
const listPage = ref();
const form = ref();
const setForm = e => {
  form.value = e;
};
const dialogVisible = ref(false);
const lodaData = async (pages, parmas) => {
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryBudgetProjectListHttp({
    ...pages,
    ...parmas
  });
  rowData.value = void 0;
  return data;
};
//更新项目详情
const updateDetail = async () => {
  dialogVisible.value = true;
  let resData = await getProjectInfo(rowData.value?.id);
  form.value.setValues({ projectDesc: resData?.projectDesc });
};

//取消
const cancelClick = () => {
  dialogVisible.value = false;
};
//确定
const confirmFunc = async () => {
  let { isValid } = await form.value.validate();
  if (!isValid) return;
  await updateDetailHttp({ budgetProjectId: rowData.value?.id, projectDesc: form.value.getValues().projectDesc });
  ElNotification({
    title: '修改成功',
    type: 'success'
  });
  dialogVisible.value = false;
};

const schema = computed(() => {
  return [
    {
      label: '项目详情说明（推进进度）',
      component: 'el-input',
      props: {
        maxlength: 200,
        type: 'textarea'
      },
      prop: 'projectDesc'
    }
  ];
});

const rules = computed(() => {
  return {
    projectDesc: [{ required: true, message: '必填', trigger: 'blur' }] // 计划名称
  };
});

onMounted(() => {});
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig(addFunc, editFunc, delFunc, updateDetail, !rowData.value),
        // searchConfig: useSearchConfig(),
        lodaData: lodaData,
        reloadOnActive: true,
        fixedButtons: true,
        checkOnRowClick: true,
        columns: userProjectColumns({ seeDateils }),
        on: {
          rowClick: selection => {
            rowData.value = selection.row;
          }
        }
      }
    }
  ];
});

const seeDateils = obj => {
  router.push({
    name: 'ermBudgetProjectInfo',
    query: {
      title: obj.projectName,
      bizName: '详情',
      type: 'info',
      id: obj.id,
      tab: ['预算项目管理', obj.projectName, '详情'].join('-')
    }
  });
};

const editFunc = () => {
  router.push({
    name: 'ermBudgetProjectAdd',
    query: {
      title: rowData.value?.projectName,
      bizName: 'edit',
      type: 'edit',
      id: rowData.value?.id,
      tab: ['预算项目管理', rowData.value?.projectName, '编辑'].join('-')
    }
  });
};

const delFunc = async () => {
  await deleteHttp({
    budgetProjectId: rowData.value?.id
  });
  listPage.value.reload({ resetPage: false });
  rowData.value = void 0;
};

const addFunc = () => {
  router.push({
    name: 'ermBudgetProjectAdd',
    query: {
      title: '新建预算项目管理',
      bizName: '新建',
      type: 'add',
      tab: '预算项目管理-新建'
    }
  });
};
</script>
<style scoped>
:deep(.el-textarea__inner) {
  height: 100px !important;
}
</style>
