<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-07-24 14:27:35
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-11 17:37:01
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/project/info.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <funi-detail :bizName="bizName" :auditButtons="buttons" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
    <!-- <SubmitSuccess ref="submitSuccess" /> -->
  </div>
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from './../commponent/project/baseInfo.vue';
import ProjectCollect from './../commponent/project/projectCollect.vue';
import sharingDetail from '@/apps/erm/cost/component/cost_sharing/sharingDetail.vue';
const route = useRoute();
const bizName = ref(route.query.bizName);
const type = ref(route.query.type);
const id = ref(route.query.id);
const detail = ref({});
const buttons = ref([]);
const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    hideStatusBar: false,
    serialName: '项目编号',
    hideStatusName: true,
    no: detail.value?.budgetProjectSn
  };
  return obj;
});

const steps = computed(() => {
  return [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit: !['audit', 'info'].includes(type.value)
      },
      on: {
        updateInfo: v => {
          detail.value = v;
        }
      }
    },
    {
      title: '预算项目统计',
      preservable: true,
      type: ProjectCollect,
      props: {
        id: id.value
        // data: detail.value
      }
    }
  ];
  // return arr;
});
</script>
