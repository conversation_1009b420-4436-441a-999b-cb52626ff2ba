<template>
  <div>
    <funi-detail :bizName="bizName"
      :auditButtons="buttons"
      :steps="steps"
      :detailHeadOption="detailHeadOption || {}" />
    <SubmitSuccess ref="submitSuccess" />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onBeforeMount } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from './../commponent/project/baseInfo.vue';
const route = useRoute();
const bizName = ref(route.query.bizName);
const type = ref(route.query.type);
const id = ref(route.query.id);
const detail = ref({});

const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    hideStatusBar: false,
    serialName: '项目编号',
    hideStatusName: true,
    no: detail.value?.budgetProjectSn
  };
  return obj;
});

console.log(111)
const steps = computed(() => {
  let arr = [
    {
      title: '基本信息',
      preservable: false,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit: !['audit', 'info'].includes(route.query.type)
      },
      on: {
        updateID: v => {
          id.value = v;
        },
        updateInfo: v => {
          detail.value = v;
        }
      }
    }
  ];
  return arr;
});
</script>
