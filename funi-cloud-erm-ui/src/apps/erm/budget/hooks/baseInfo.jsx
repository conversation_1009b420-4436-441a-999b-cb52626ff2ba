/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-01 14:14:48
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-01 17:16:53
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/hooks/baseInfo.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import ErmTreeSelect from '@/apps/erm/component/ermTree/index.vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import { dicCode } from '@/apps/erm/config/config.jsx';
import MuliSelect from '@/apps/erm/budget/commponent/supervision/select.vue';
import { validPhone } from '@/apps/erm/config/validate.js';

export const useSchema = params => {
  const otherSchema = !params.isEdit
    ? [
        {
          label: '创建时间',
          prop: 'createTime'
        },
        {
          label: '关联项目',
          component: ({ value }) => (
            <div>
              {(value || []).map(item => {
                return (
                  <el-button
                    link
                    type="primary"
                    onClick={() => {
                      params.goPage(item);
                    }}
                  >
                    {item.projectName}
                  </el-button>
                );
              })}
            </div>
          ),
          prop: 'projectManagementList'
        }
      ]
    : [];
  return [
    {
      label: '项目编号',
      component: null,
      prop: 'budgetProjectSn'
    },
    {
      label: '项目名称',
      component: params.isEdit ? 'el-input' : null,
      prop: 'projectName',
      props: {
        placeholder: '请输入项目名称',
        maxlength: 200
      }
    },
    {
      label: '项目归属公司',
      component: params.isEdit
        ? () => (
            <ErmTreeSelect
              propsMap={{
                value: 'id',
                label: 'companyName',
                children: 'childrens'
              }}
              isDetp={false}
              isWatchAll={true}
              onChange={id => {
                params.setCompanyId(id);
                params?.resetDeptId();
              }}
            ></ErmTreeSelect>
          )
        : null,
      prop: params.isEdit ? 'companyId' : 'companyName',
      props: {
        style: { width: '100%' },
        placeholder: '请输选择项目归属公司'
      }
    },
    {
      label: '项目归属部门',
      component: params.isEdit
        ? () => (
            <ErmTreeSelect
              propsMap={{
                value: 'id',
                label: 'deptName',
                children: 'childrenDept'
              }}
              isDetp={true}
              isWatchAll={true}
              params={{
                id: params.getCompanyId()
              }}
            ></ErmTreeSelect>
          )
        : null,
      prop: params.isEdit ? 'deptId' : 'deptName',
      props: {
        style: { width: '100%' },
        placeholder: '请选择项目归属部门'
      }
    },

    {
      label: '负责人',
      component: params.isEdit
        ? () => (
            <InfiniteSelect
              api={ermGlobalApi.queryEmployeeList}
              defaultProps={{
                keyWord: 'keyword',
                name: 'employeeName',
                id: 'id'
              }}
            ></InfiniteSelect>
          )
        : null,
      prop: params.isEdit ? 'employeeObj' : 'employeeName'
    },
    {
      label: '预算项目类型',
      component: params.isEdit ? ({ formModel }) => <MuliSelect></MuliSelect> : null,
      prop: params.isEdit ? 'dicBudgetTagCode' : 'dicBudgetTagName',
      props: {
        style: { width: '100%' },
        code: dicCode.budgetTag,
        defaultProps: {
          name: 'name',
          id: 'code'
        }
      }
    },
    {
      label: '客户名称',
      component: params.isEdit
        ? () => (
            <InfiniteSelect
              api="erm/customerList/queryCustomerList"
              defaultProps={{
                keyWord: 'keyword',
                name: 'customerName',
                id: 'id'
              }}
              onChange={obj => params.changeCustomer(obj)}
            ></InfiniteSelect>
          )
        : null,
      prop: params.isEdit ? 'customerObj' : 'customerName',
      props: {
        style: { width: '100%' }
      }
    },
    {
      label: '联系人',
      component: params.isEdit
        ? () => (
            <ErmSelect
              options={params.contactPersonOption.value}
              onChange={e => params.contactPersonChange(e)}
            ></ErmSelect>
          )
        : null,
      prop: params.isEdit ? 'contactPersonId' : 'contactPerson',
      props: {
        style: { width: '100%' }
      }
    },
    {
      label: '联系人手机号',
      component: null,
      prop: 'contactPhone',
      props: {
        style: { width: '100%' }
      }
    },
    ...otherSchema,
    {
      label: '项目详细说明',
      component: params.isEdit
        ? 'el-input'
        : ({ formModel }) => {
            return (
              <span
                style={{
                  'white-space': 'pre-wrap'
                }}
              >
                {formModel.projectDesc || '--'}
              </span>
            );
          },
      prop: 'projectDesc',
      colProps: params.isEdit
        ? {
            span: 24
          }
        : null,
      props: {
        placeholder: '请输入项目详细说明',
        autosize: { minRows: 2, maxRows: 4 },
        type: 'textarea',
        maxlength: 200
      }
    }
  ];
};

export const useRule = ({ isEdit }) => {
  // let validPhone = (rule, value, callback) => {
  //   let reg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
  //   if (!reg.test(value)) {
  //     callback(new Error('请输入正确的手机号'));
  //   } else {
  //     callback();
  //   }
  // };

  let c = isEdit ? 'companyId' : 'companyName';
  let d = isEdit ? 'deptId' : 'deptName';
  return {
    projectName: [{ required: true, message: '必填', trigger: 'change' }],
    [c]: [{ required: true, message: '必填', trigger: 'change' }],
    [d]: [{ required: true, message: '必填', trigger: 'change' }],
    customerObj: [{ required: true, message: '必填', trigger: 'change' }],
    contactPersonId: [{ required: true, message: '必填', trigger: 'change' }],
    contactPerson: [{ required: true, message: '必填', trigger: 'change' }],
    contactPhone: [{ required: true, trigger: 'change', validator: validPhone }]
  };
};
