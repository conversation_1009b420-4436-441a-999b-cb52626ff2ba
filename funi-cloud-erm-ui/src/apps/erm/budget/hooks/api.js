/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-07 10:07:08
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-05 19:45:53
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/hooks/api.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
const apiUrl = {
  info: '/erm/budgetProject/info',
  queryBudgetProjectList: '/erm/budgetProjectList/queryBudgetProjectList',

  new: '/erm/budgetProject/new',
  delete: '/erm/budgetProject/delete',
  queryCustomerListByCurrentEmployee: '/erm/customerList/queryCustomerListByCurrentEmployee',
  queryBudgetPlanList: '/erm/budgetPlanList/queryBudgetPlanList', // 获取预算计划列表
  getBudgetPlanInfo: '/erm/budgetPlan/info', // 获取预算计划详情
  deleteBudgetPlan: '/erm/budgetPlan/delete', // 删除预算计划
  veriBudgetPlanName: '/erm/budgetPlan/verification', // 校验预算计划名称是否重复
  queryProjectForBudgetPlans: '/erm/projectManagementList/queryProjectForBudgetPlans', // 获取项目管理列表
  budgetPlanNew: '/erm/budgetPlan/new',
  queryBudgetProjectInfoListExport: '/erm/budgetProjectInfoList/queryBudgetProjectInfoListExport', //关联项目导出
  queryBudgetProjectInfoList: '/erm/budgetProjectInfoList/queryBudgetProjectInfoList', //关联项目
  getBudgetProjectList: '/erm/budgetProjectInfo/queryBudgetProjectList', // 项目预算添加预算项目列表查询
  changePlanYear: '/erm/budgetPlan/changePlanYear', //修改年份计划实时计算实际金额（签单、确收、回款）
  updateDetail: '/erm/budgetProject/updateDetail',
  queryProjectOrContractList: '/erm/budgetProjectInfo/queryProjectOrContractList',
  budgetProjectReminder: '/erm/reminderManagement/budgetProjectReminder'
};

/**
 * @description: 预算项目管理
 */

// 获取预算项目列表
export const queryBudgetProjectListHttp = params => {
  return $http.post(apiUrl.queryBudgetProjectList, params);
};

// 新建
export const newHttp = params => {
  return $http.post(apiUrl.new, params);
};

// 详情
export const infoHttp = params => {
  return $http.fetch(apiUrl.info, params);
};
// 详情
export const updateDetailHttp = params => {
  return $http.post(apiUrl.updateDetail, params);
};

// 删除
export const deleteHttp = params => {
  return $http.fetch(apiUrl.delete, params);
};

/**
 * @description: 项目预算
 */

// 获取预算计划列表
export const queryBudgetPlanListHttp = params => {
  return $http.post(apiUrl.queryBudgetPlanList, params);
};

// 获取预算计划详情
export const getBudgetPlanInfoHttp = params => {
  return $http.post(apiUrl.getBudgetPlanInfo, params);
};

// 删除预算计划
export const deleteBudgetPlanHttp = params => {
  return $http.fetch(apiUrl.deleteBudgetPlan, params);
};

// 校验预算计划名称是否重复
export const veriBudgetPlanNameHttp = params => {
  return $http.fetch(apiUrl.veriBudgetPlanName, params);
};

// 获取项目管理列表
export const queryProjectForBudgetPlansHttp = params => {
  return $http.post(apiUrl.queryProjectForBudgetPlans, params);
};

// 新增
export const budgetPlanNewHttp = params => {
  return $http.post(apiUrl.budgetPlanNew, params);
};
// 新增
export const getBudgetProjectList = params => {
  return $http.post(apiUrl.getBudgetProjectList, params);
};

// 详情
export const budgetPlanInfoHttp = params => {
  return $http.fetch(apiUrl.budgetPlanInfo, params);
};
// 关联项目导出
export const queryBudgetProjectInfoListExportHttp = async params => {
  let resData = await $http.post(apiUrl.queryBudgetProjectInfoListExport, params, {
    responseType: 'blob'
  });
  let downloadElement = document.createElement('a');
  let href = window.URL.createObjectURL(resData); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = '经营项目明细查询列表.xls'; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
};

export const queryBudgetProjectInfoListHttp = params => {
  return $http.post(apiUrl.queryBudgetProjectInfoList, params);
};

export const changePlanYearHttp = params => {
  return $http.post(apiUrl.changePlanYear, params);
};



export const queryProjectOrContractListHttp = params => {
  return $http.post(apiUrl.queryProjectOrContractList, params);
}


export const budgetProjectReminderHttp = params => {
  return $http.post(apiUrl.budgetProjectReminder, params);
}