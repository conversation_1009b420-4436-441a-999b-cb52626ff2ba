import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';

/**
 * 财务报告
 *
 */
const userColumns = ({ seeDateils = () => {} }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left',
      label: '选择'
    },
    {
      label: '标题',
      prop: 'title',
      width: '300px',
      fixed: 'left',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => seeDateils(row)}
            auth={'ERM_FINANCE_REPORT_INFO'}
            text={row.title}
          />
        );
      }
    },

    {
      label: '创建人',
      prop: 'creator'
    },
    {
      label: '创建时间',
      prop: 'createTime'
    }
  ];
};

/**
 *
 *  列表按钮配置
 *
 */
const useBtnsConfig = ({
  addFunc = () => {},
  editFunc = () => {},
  delFunc = () => {},
  copyFunc = () => {},
  isEnable = false
}) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_FINANCE_REPORT_ADD" onClick={addFunc} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_FINANCE_REPORT_EDIT" onClick={editFunc} type="primary" disabled={isEnable}>
          修改
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_FINANCE_REPORT_COPY" onClick={copyFunc} type="primary" disabled={isEnable}>
          复制
        </el-button>
      )
    },
    {
      component: () => (
        <el-popconfirm
          title="确定删除选择数据？"
          width="220"
          onConfirm={() => {
            delFunc();
          }}
        >
          {{
            reference: () => (
              <el-button v-auth="ERM_FINANCE_REPORT_DEL" type="primary" disabled={isEnable}>
                删除
              </el-button>
            )
          }}
        </el-popconfirm>
      )
    }
  ];
};

export { userColumns, useBtnsConfig };
