const apiUrl = {
  new: '/erm/financialReport/new',
  delete: '/erm/financialReport/delete',
  info: '/erm/financialReport/info',
  queryFinancialReportList: '/erm/financialReportList/queryFinancialReportList',
  downloadFileTemplate: '/erm/financialReport/downloadFileTemplate'
};

//列表查询
export const queryFinancialReportListHttp = params => {
  return $http.post(apiUrl.queryFinancialReportList, params);
};
//新增
export const newHttp = params => {
  return $http.post(apiUrl.new, params);
};
//删除
export const deleteHttp = params => {
  return $http.fetch(apiUrl.delete, params);
};
//详情
export const infoHttp = params => {
  return $http.fetch(apiUrl.info, params);
};
export const downloadFileTemplateHttp = async (params = {}) => {
  let resData = await $http.post(
    apiUrl.downloadFileTemplate,
    { ...params },
    {
      responseType: 'blob'
    }
  );
  let downloadElement = document.createElement('a');
  let href = window.URL.createObjectURL(resData); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = '财务报告导入(模板).xlsx'; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
};
