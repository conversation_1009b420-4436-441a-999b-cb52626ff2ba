/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-11 16:55:50
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-11 17:27:21
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/hooks/getInfo.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { infoHttp } from './api.js';
/**
 * @description 获取详情接口
 * **/

export const getProjectInfo = async id => {
  let { budgetProjectVo, projectManagementList } = await infoHttp({ budgetProjectId: id });
  budgetProjectVo.employeeObj = {
    id: budgetProjectVo.employeeId,
    name: budgetProjectVo.employeeName
  };
  budgetProjectVo.projectManagementList = projectManagementList;
  return budgetProjectVo;
};

export const getProjectInfoList = async id => {
  let { budgetProjectStatisticList } = await infoHttp({ budgetProjectId: id });
  // return budgetProjectStatisticList;
  return {
    list: budgetProjectStatisticList,
    total: budgetProjectStatisticList.length
  };
};
