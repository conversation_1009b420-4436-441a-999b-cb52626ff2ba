import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';

/**
 * 督办兼管理列表配置
 *
 */
const useColumns = seeDetail => {
  return [
    {
      type: 'selection',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '催办事项',
      prop: 'reminderTask',
      width: '300px',
      fixed: 'left',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => seeDetail(row)}
            auth={'ERM_URGENT_INFO'}
            text={row.reminderTask}
          />
        );
      }
    },
    {
      label: '催办内容',
      prop: 'reminderContent'
    },
    {
      label: '催办类型',
      prop: 'dicReminderTypeName'
    },
    {
      label: '催办对象',
      prop: 'reminderRecipientName'
    },
    {
      label: '催办时间',
      prop: 'reminderTimeStr'
    },
    {
      label: '催办人',
      prop: 'reminderInitiatorName'
    }
  ];
};

/**
 *
 *  列表按钮配置
 *
 */
const useBtnsConfig = ({ delFunc = () => {}, isEnable = false }) => {
  return [
    {
      component: () => (
        <el-popconfirm
          title="删除后将无法找回，您确定要删除吗？"
          width="220"
          onConfirm={() => {
            delFunc();
          }}
        >
          {{
            reference: () => (
              <el-button v-auth="ERM_URGENT_DEL" type="primary" disabled={isEnable}>
                删除
              </el-button>
            )
          }}
        </el-popconfirm>
      )
    }
  ];
};

export { useColumns, useBtnsConfig };
