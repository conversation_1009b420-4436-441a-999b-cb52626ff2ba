export const apiUrl = {
  querySupervisionContentList: '/erm/SupervisionContentList/querySupervisionContentList',
  supervisionContentNew: '/erm/SupervisionContent/new',
  SupervisionContentDelete: '/erm/SupervisionContent/delete',
  SupervisionContentInfo: '/erm/SupervisionContent/info',
  SupervisionContentFinish: '/erm/SupervisionContent/finish',
  SupervisionReplyNew: '/erm/SupervisionReply/new',
  queryDeptList: '/erm/deptList/queryFLYMDeptList',
  creatBus: 'erm/SupervisionContent/creatBus',
  chooseReminderPersonList: '/erm/SupervisionContentList/chooseReminderPersonList',
  budgetProjectReminder: '/erm/reminderManagement/budgetProjectReminder'
};

//列表查询
export const querySupervisionContentListHttp = params => {
  return $http.post(apiUrl.querySupervisionContentList, params);
};
//新增
export const supervisionContentNewHttp = params => {
  return $http.post(apiUrl.supervisionContentNew, params);
};
//删除
export const SupervisionContentDeleteHttp = params => {
  return $http.fetch(apiUrl.SupervisionContentDelete, params);
};
//详情
export const SupervisionContentInfoHttp = params => {
  return $http.fetch(apiUrl.SupervisionContentInfo, params);
};
//完成
export const SupervisionContentFinishHttp = params => {
  return $http.fetch(apiUrl.SupervisionContentFinish, params);
};
//督办件评论接口
export const SupervisionReplyNewHttp = params => {
  return $http.post(apiUrl.SupervisionReplyNew, params);
};

export const creatBusHttp = params => {
  return $http.post(apiUrl.creatBus, params);
};
export const chooseReminderPersonListHttp = params => {
  return $http.post(apiUrl.chooseReminderPersonList, params);
};
export const budgetProjectReminderHttp = params => {
  return $http.post(apiUrl.budgetProjectReminder, params);
};
