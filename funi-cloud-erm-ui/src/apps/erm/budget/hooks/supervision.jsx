import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import ermHooks from '@/apps/erm/hooks/index.js';

/**
 * 督办兼管理列表配置
 *
 */
const userColumns = ({ seeDetails, examFunc, delFunc, editFunc, seeBusinessInfo, urgent }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    // {
    //   label: '督办件编号',
    //   prop: 'supervisionSn',
    //   width: '300px',
    //   fixed: 'left',
    //   render: ({ row, index }) => {
    //     return (
    //       <HyperlinkInfo
    //         row={row}
    //         index={index}
    //         func={() => seeDateils(row)}
    //         auth={'ERM_SUPERVISION_INFO'}
    //         text={row.supervisionSn}
    //       />
    //     );
    //   }
    // },

    {
      label: '督办件事项',
      prop: 'supervisionContext',
      width: '300px',
      fixed: 'left',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => seeDetails(row)}
            auth={'ERM_SUPERVISION_INFO'}
            text={row.supervisionContext}
          />
        );
      }
    },
    {
      label: '分管领导',
      prop: 'leaderName'
    },
    {
      label: '最新回复',
      prop: 'latestReplyContext',
      showOverflowTooltip: false,

      render: ({ row }) => {
        return (
          <el-popover
            effect={'dark'}
            placement={'top'}
            width={'500'}
            disabled={!row.latestReplyContext || row.latestReplyContext?.length <= 20}
            trigger={'hover'}
          >
            {{
              reference: () => {
                return !!row.latestReplyContext ? (
                  <div style={{ cursor: 'pointer', width: '300px', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {row.latestReplyContext}
                  </div>
                ) : (
                  <span>{'--'}</span>
                );
              },
              default: () => {
                return <span style={{ fontSize: '16px' }}>{row.latestReplyContext}</span>;
              }
            }}
          </el-popover>
        );
      },
      width: '300px'
    },
    {
      label: '预计完成时间',
      prop: 'predictFinishTime'
    },
    {
      label: '最新回复时间',
      prop: 'latestReplyTime'
    },
    {
      label: '分类',
      prop: 'dicSupervisionTypeName'
    },
    {
      label: '牵头承办部门',
      prop: 'leadDeptName'
    },
    {
      label: '状态',
      prop: 'dicStatusName'
    },
    {
      label: '流程节点',
      prop: 'businessNode'
    },
    {
      label: '协办部门',
      prop: 'assistDeptName'
    },
    {
      label: '完成状态',
      prop: 'dicSupervisionStatusName'
    },
    {
      label: '负责人',
      prop: 'supervisionEmployeeList',
      render: ({ row }) => {
        let list = row.supervisionEmployeeList.map(item => item.employeeName);
        return list.toString();
      },
      width: '200px'
    },
    {
      label: '会议纪要编号',
      prop: 'meetingNotesSn'
    },
    {
      label: '操作',
      prop: 'operationBtn',
      align: 'center',
      fixed: 'right',
      render: ({ row, index }) => {
        let operationBtn = {
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => examFunc(row)}
              auth={'ERM_SUPERVISION_AUDIT'}
              text={'审核'}
            ></Hyperlink>
          ),
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => editFunc(row)}
              auth={'ERM_SUPERVISION_BUZ_EDIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          DEL: (
            <el-popconfirm
              title="删除后将无法找回，您确定要删除吗？"
              width="220"
              onConfirm={() => {
                delFunc(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_SUPERVISION_BUZ_DELE'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={() => seeBusinessInfo(row)}
              auth={'ERM_SUPERVISION_BUZ_INFO'}
              text={'业务详情'}
            ></Hyperlink>
          ),
          URGENT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => urgent(row)}
              auth={'ERM_SUPERVISION_URGENT'}
              text={'催办'}
            ></Hyperlink>
          )
        };
        let btnList = [];
        btnList = $utils.clone(row.buttonList, true);
        if (row.dicDataStatusCode == '2' && !btnList.includes('EDIT') && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO');
        }
        if (row.dicSupervisionStatusCode == '0' && row.dicStatusCode == '1') btnList.push('URGENT');

        return (
          <div style="display:flex;justify-content: space-around;align-items: center;width: 100px">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

/**
 *
 *  列表按钮配置
 *
 */
const useBtnsConfig = ({
  addFunc = () => {},
  editFunc = () => {},
  delFunc = () => {},
  completeFn = () => {},
  update = () => {},
  isEnable = false
}) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_SUPERVISION_DELAYEXTNESION" onClick={update} type="primary" disabled={isEnable}>
          督办件延期申请
        </el-button>
      )
    },

    {
      component: () => (
        <el-button v-auth="ERM_SUPERVISION_ADD" onClick={addFunc} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_SUPERVISION_EDIT" onClick={editFunc} type="primary" disabled={isEnable}>
          编辑
        </el-button>
      )
    },
    {
      component: () => (
        <el-popconfirm
          title="删除后将无法找回，您确定要删除吗？"
          width="220"
          onConfirm={() => {
            delFunc();
          }}
        >
          {{
            reference: () => (
              <el-button v-auth="ERM_SUPERVISION_DEL" type="primary" disabled={isEnable}>
                删除
              </el-button>
            )
          }}
        </el-popconfirm>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_SUPERVISION_COMPLETE" onClick={completeFn} type="primary" disabled={isEnable}>
          完成
        </el-button>
      )
    }
  ];
};

const useSearchConfig = () => {
  const style = {
    width: '100%'
  };
  return {
    border: false,

    schema: [
      {
        label: '员工姓名',
        component: 'el-input',
        prop: 'keyword',
        props: {
          placeholder: '请输入员工姓名'
        }
      }
    ]
  };
};

const useUrgentColumns = () => {
  return [
    {
      type: 'selection',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '员工姓名',
      prop: 'employeeName'
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    }
  ];
};

export { userColumns, useBtnsConfig, useSearchConfig, useUrgentColumns };
