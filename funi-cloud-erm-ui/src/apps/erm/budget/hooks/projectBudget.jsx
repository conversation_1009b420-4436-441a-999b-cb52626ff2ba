/*
 * @Author: co<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-24 15:00:15
 * @LastEditors: “linzhi” “<EMAIL>”
 * @LastEditTime: 2023-10-13 09:50:08
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/hooks/projectBudget.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import { erm_intl } from '@/apps/erm/hooks/intl';
export const userProjectColumns = ({ seeDetails }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '计划名称',
      prop: 'planName',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={seeDetails}
            auth={'ERM_PROJECTPUDGET_INFO'}
            text={row.planName}
          />
        );
      }
    },
    {
      label: '计划年份',
      prop: 'planYear'
    },
    {
      label: '计划签单',
      prop: 'planOrder',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.planOrder);
      }
    },
    {
      label: '实际签单',
      prop: 'actualOrder',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.actualOrder);
      }
    },
    {
      label: '计划回款',
      prop: 'planCollection',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.planCollection);
      }
    },
    {
      label: '实际回款(万元)',
      prop: 'actualCollection',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.actualCollection);
      }
    },
    {
      label: '计划确收(万元)',
      prop: 'planReceipt',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.planReceipt);
      }
    },
    {
      label: '调整后确收(万元)',
      prop: 'adjustedReceipt',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.adjustedReceipt);
      }
    },
    {
      label: '实际确收(万元)',
      prop: 'actualReceipt',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.actualReceipt);
      }
    },
    {
      label: '创建时间',
      prop: 'createTime'
    }
  ];
};

/**
 * @description 按钮配置
 * @param {function} addFunc
 * @param {function} editFunc
 * @param {function} delFunc
 * **/
export const useBtnsConfig = (addFunc = () => {}, editFunc = () => {}, delFunc = () => {}, isDisabled = false) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_PROJECTPUDGET_ADD" onClick={addFunc} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_PROJECTPUDGET_EDIT" onClick={editFunc} type="primary" disabled={isDisabled}>
          编辑
        </el-button>
      )
    },
    {
      component: () => (
        <el-popconfirm
          title="确定删除选择数据？"
          width="220"
          onConfirm={() => {
            delFunc();
          }}
        >
          {{
            reference: () => (
              <el-button v-auth="ERM_PROJECTPUDGET_DEL" type="primary" disabled={isDisabled}>
                删除
              </el-button>
            )
          }}
        </el-popconfirm>
      )
    }
  ];
};

// 搜索配置
export const useSearchConfig = () => {};
