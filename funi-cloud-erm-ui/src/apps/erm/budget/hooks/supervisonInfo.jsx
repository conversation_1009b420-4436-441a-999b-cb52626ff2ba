import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import Select from '@/apps/erm/budget/commponent/supervision/select.vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { apiUrl } from '@/apps/erm/budget/hooks/supervisionApi.jsx';
import { dicCode } from '@/apps/erm/config/config.jsx';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
const useSchema = ({ isEdit = false, operation, editType, type }) => {
  const changeColor = (val, code) => {
    let now = $utils.Date(new Date());
    let start = $utils.Date(val);
    let diff = Math.floor(now.diff(start, 'day', true));
    if (code === '1') return;
    if (diff >= 0) {
      return {
        color: '#f73131'
      };
    } else if (diff < 0 && diff > -10) {
      return {
        color: '#ff66009c'
      };
    } else if (diff < -10) {
      return {
        color: '#606266'
      };
    }
  };

  return [
    {
      label: '督办事项',
      component:
        editType == 'normal' && operation != 'change'
          ? 'el-input'
          : ({ formModel }) => {
              return (
                <span
                  style={{
                    'white-space': 'pre-wrap',
                    color: 'var(--el-color-primary)'
                  }}
                >
                  {formModel.supervisionContext || '--'}
                </span>
              );
            },
      prop: 'supervisionContext',
      props: {
        placeholder: '请输入督办事项',
        maxlength: 200,
        clearable: true,
        type: 'textarea'
      },
      colProps: {
        span: isEdit ? 16 : 24
      }
    },
    {
      label: '创建时间',
      component: null,
      prop: 'createTime',
      hidden: () => {
        if (isEdit || editType == 'buz' || type == 'audit') return false;
        else return true;
      }
    },
    {
      label: '会议时间',
      component:
        editType == 'normal' && operation != 'change'
          ? () => (
              <ElDatePicker
                modelValue={'meetingTime'}
                placeholder={'请选择会议时间'}
                format="YYYY-MM-DD"
                valueFormat="x"
                type={'date'}
              />
            )
          : null,
      prop: 'meetingTime',
      props: {
        style: { width: '100%' }
      }
    },
    {
      label: '会议纪要编号',
      component: editType == 'normal' && operation != 'change' ? 'el-input' : null,
      prop: 'meetingNotesSn',
      props: {
        placeholder: '请输入会议纪要编号',
        maxlength: 200,
        clearable: true
      }
    },
    {
      label: '分管领导',
      component:
        editType == 'normal' && operation != 'change'
          ? ({ formModel }) => (
              <Select
                api={ermGlobalApi.queryEmployeeList}
                // modelValue={formModel.leaderId}
                multiple={false}
                options={formModel.leaderOptions}
                defaultProps={{
                  keyWord: 'employeeName',
                  name: 'employeeName',
                  id: 'id'
                }}
              ></Select>
            )
          : null,
      prop: editType == 'normal' && operation != 'change' ? 'leaderId' : 'leaderName',
      props: {
        placeholder: '请输入分管领导',
        maxlength: 200,
        clearable: true
      }
    },
    {
      label: '负责人',
      component:
        editType == 'normal' && operation != 'change'
          ? ({ formModel }) => (
              <Select
                api={ermGlobalApi.queryEmployeeList}
                // modelValue={formModel.leaderId}
                options={formModel.options}
                multiple={true}
                defaultProps={{
                  keyWord: 'employeeName',
                  name: 'employeeName',
                  id: 'id'
                }}
              ></Select>
            )
          : null,
      prop: editType == 'normal' && operation != 'change' ? 'employeeIds' : 'employeeNames',
      props: {
        placeholder: '请输入客户名称',
        maxlength: 200,
        style: {
          width: '100%'
        }
      }
    },
    {
      label: '完成状态',
      component: null,
      prop: 'dicSupervisionStatusName',
      hidden: () => {
        if (isEdit || editType == 'buz' || type == 'audit') {
          return true;
        } else {
          return false;
        }
      }
    },

    {
      label: '预计完成时间',
      component:
        editType == 'buz' || type == 'add' || isEdit
          ? () => (
              <ElDatePicker
                modelValue={'predictFinishTime'}
                placeholder={'请选择预计完成时间'}
                format="YYYY-MM-DD"
                valueFormat="x"
                type={'date'}
              />
            )
          : ({ formModel }) => (
              <span style={changeColor(formModel.predictFinishTime, formModel.dicSupervisionStatusCode)}>
                {formModel.predictFinishTime}
              </span>
            ),
      prop: 'predictFinishTime',
      props: {
        style: { width: '100%' }
      }
      // hidden: () => {
      //   if (isEdit) {
      //     return true;
      //   } else {
      //     return false;
      //   }
      // }
    },
    {
      label: '牵头承办部门',
      component:
        editType == 'normal' && operation != 'change'
          ? ({ formModel }) => (
              <Select
                api={apiUrl.queryDeptList}
                options={formModel.leadDeptOption}
                multiple={false}
                defaultProps={{
                  keyWord: 'deptName',
                  name: 'deptName',
                  id: 'id'
                }}
              ></Select>
            )
          : null,
      prop: editType == 'normal' && operation != 'change' ? 'leadDeptId' : 'leadDeptName'
    },
    {
      label: '协办部门',
      component:
        editType == 'normal' && operation != 'change'
          ? ({ formModel }) => (
              <Select
                api={apiUrl.queryDeptList}
                // modelValue={formModel.leaderId}
                options={formModel.assistDeptOption}
                multiple={false}
                defaultProps={{
                  keyWord: 'deptName',
                  name: 'deptName',
                  id: 'id'
                }}
              ></Select>
            )
          : null,
      prop: editType == 'normal' && operation != 'change' ? 'assistDeptId' : 'assistDeptName'
    },
    {
      label: '分类',
      component: editType == 'normal' && operation != 'change' ? () => <ErmSelect></ErmSelect> : null,
      prop: editType == 'normal' && operation != 'change' ? 'dicSupervisionTypeCode' : 'dicSupervisionTypeName',
      props: {
        code: dicCode.supervision_type_code,
        style: { width: '100%' }
      }
    },

    {
      label: '回复',
      component: null,
      prop: 'replyLength',
      hidden: () => {
        if (isEdit || editType == 'buz' || type == 'audit') {
          return true;
        } else {
          return false;
        }
      }
    }
    // {
    //   label: '督办件编号',
    //   component: null,
    //   prop: 'supervisionSn'
    // },

    // {
    //   label: '发布时间',
    //   component: null,
    //   prop: 'publishTime',
    //   hidden: () => {
    //     if (isEdit) {
    //       return true;
    //     } else {
    //       return false;
    //     }
    //   }
    // },
    // {
    //   label: '发布人',
    //   component: null,
    //   prop: 'publisherName',
    //   hidden: () => {
    //     if (isEdit) {
    //       return true;
    //     } else {
    //       return false;
    //     }
    //   }
    // }
  ];
};

const useRule = ({ isEdit = false }) => {
  let validEmployeeIds = (_, value, callback) => {
    if ($utils.isNil(value) || !Array.isArray(value) || value?.length <= 0) {
      callback(new Error('必填'));
    } else {
      callback();
    }
  };

  return isEdit
    ? {
        leaderId: [{ required: true, message: '必填', trigger: 'change' }],
        employeeIds: [{ required: true, validator: validEmployeeIds, trigger: 'change' }],
        // predictFinishTime: [{ required: true, message: '必填', trigger: 'change' }],
        supervisionContext: [{ required: true, message: '必填', trigger: 'change' }],
        dicSupervisionTypeCode: [{ required: true, message: '必填', trigger: 'change' }],
        leadDeptId: [{ required: true, message: '必填', trigger: 'change' }]
      }
    : null;
};

export { useSchema, useRule };
