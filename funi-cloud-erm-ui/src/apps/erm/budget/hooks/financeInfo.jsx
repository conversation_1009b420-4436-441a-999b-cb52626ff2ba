import MoneyInput from '@/apps/erm/component/inputMoneyNumber/index.vue';
const useSchema = ({ isEdit = false }) => {
  return [
    {
      label: '创建人',
      component: null,
      prop: 'creator',
      hidden: isEdit
    },
    {
      label: '创建时间',
      component: null,
      prop: 'createTime',
      hidden: isEdit
    },
    {
      label: '标题',
      component: isEdit ? 'el-input' : null,
      prop: 'title',
      props: {
        placeholder: '请输入',
        maxlength: 200,
        clearable: true
      }
    },
    {
      label: '月份',
      component: isEdit
        ? () => (
          <ElDatePicker
            modelValue={'month'}
            placeholder={'请选择'}
            format="YYYY-MM"
            valueFormat="YYYY-MM"
            type={'month'}
          />
        )
        : null,
      prop: 'month',
      props: {
        style: { width: '100%' }
      }
    }
  ];
};

const useRule = ({ isEdit = false }) => {
  return isEdit
    ? {
      title: [{ required: true, message: '必填', trigger: 'change' }],
      month: [{ required: true, message: '必填', trigger: 'change' }]
    }
    : null;
};
const useBudgetColumns = ({ isEdit = false, iptChange = () => { } }) => {
  return [
    {
      label: '',
      prop: 'type',
      width: 180
    },
    {
      label: '当月完成',
      prop: 'currentMonthCompleted',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.currentMonthCompleted}
            onChange={e => {
              iptChange(index, 'budgetList', 'currentMonthCompleted', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.currentMonthCompleted
        );
      }
    },
    {
      label: '累计完成',
      prop: 'accumulatedComplet',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.accumulatedComplet}
            onChange={e => {
              iptChange(index, 'budgetList', 'accumulatedComplet', e);
            }}
            maxlength={50}
            placeholder="请输入"
          />
        ) : (
          row.accumulatedComplet
        );
      }
    },
    {
      label: '年度目标',
      prop: 'annualTarget',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.annualTarget}
            onChange={e => {
              iptChange(index, 'budgetList', 'annualTarget', e);
            }}
            maxlength={50}
            placeholder="请输入"
          />
        ) : (
          row.annualTarget
        );
      }
    }
  ];
};

const useCompareColumns = ({ isEdit = false, iptChange = () => { } }) => {
  return [
    {
      label: '项目名称',
      prop: 'projectName',
      width: 200
    },
    {
      label: '当月累计',
      prop: 'accumulatedInJanuary',
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.accumulatedInJanuary}
            onChange={e => {
              iptChange(index, 'compareList', 'accumulatedInJanuary', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.accumulatedInJanuary
        );
      }
    },
    {
      label: '去年同期',
      prop: 'synchronismLastYear',
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.synchronismLastYear}
            onChange={e => {
              iptChange(index, 'compareList', 'synchronismLastYear', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.synchronismLastYear
        );
      }
    },
    {
      label: '同比增减额',
      prop: 'incrDecrease',
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecrease}
            onChange={e => {
              iptChange(index, 'compareList', 'incrDecrease', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecrease
        );
      }
    },
    {
      label: '同比增减率(%)',
      prop: 'incrDecreaseRate',
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecreaseRate}
            onChange={e => {
              iptChange(index, 'compareList', 'incrDecreaseRate', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecreaseRate
        );
      }
    },
    {
      label: '差异说明',
      prop: 'desc',
      align: 'left',
      minWidth: 300,
      render: ({ row, index }) => {
        return isEdit ? (
          <el-input
            modelValue={row.desc}
            onInput={e => {
              iptChange(index, 'compareList', 'desc', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.desc
        );
      }
    }
  ];
};
const useCostInputListColumns = ({ isEdit = false, iptChange = () => { } }) => {
  const edit = row => isEdit && row.projectName !== '成本投入合计';
  return [
    {
      label: '项目名称',
      prop: 'projectName',
      width: 200,
      render: ({ row, index }) => {
        if ([3, 4, 5, 6].includes(index)) {
          return <div style="text-indent:40px">{row.projectName}</div>;
        } else {
          return row.projectName;
        }
      }
    },
    {
      label: '当月累计',
      prop: 'accumulatedInJanuary',
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.accumulatedInJanuary}
            onChange={e => {
              iptChange(index, 'costInputList', 'accumulatedInJanuary', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.accumulatedInJanuary
        );
      }
    },
    {
      label: '去年同期',
      prop: 'synchronismLastYear',
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.synchronismLastYear}
            onChange={e => {
              iptChange(index, 'costInputList', 'synchronismLastYear', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.synchronismLastYear
        );
      }
    },
    {
      label: '同比增减额',
      prop: 'incrDecrease',
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecrease}
            onChange={e => {
              iptChange(index, 'costInputList', 'incrDecrease', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecrease
        );
      }
    },
    {
      label: '同比增减率(%)',
      prop: 'incrDecreaseRate',
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecreaseRate}
            onChange={e => {
              iptChange(index, 'costInputList', 'incrDecreaseRate', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecreaseRate
        );
      }
    },
    {
      label: '差异分析',
      prop: 'desc',
      align: 'left',
      minWidth: 300,
      render: ({ row, index }) => {
        return edit(row) ? (
          <el-input
            modelValue={row.desc}
            onInput={e => {
              iptChange(index, 'costInputList', 'desc', e);
            }}
            placeholder="请输入"
          />
        ) : (
          '--'
        );
      }
    }
  ];
};

const useSigningColumns = ({ isEdit = false, iptChange = () => { } }) => {
  const edit = row => isEdit && row.dept !== '合计';
  return [
    {
      label: '部门',
      prop: 'dept',
      width: 200,
      render: ({ row, index }) => {
        if ([4, 7, 8].includes(index)) {
          return <div style="text-indent:40px">{row.dept}</div>;
        } else {
          return row.dept;
        }
      }
    },
    {
      label: '实际签单',
      prop: 'actualSigning',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.actualSigning}
            onChange={e => {
              iptChange(index, 'signingList', 'actualSigning', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.actualSigning
        );
      }
    },
    {
      label: '计划签单',
      prop: 'planSigning',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.planSigning}
            onChange={e => {
              iptChange(index, 'signingList', 'planSigning', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.planSigning
        );
      }
    },
    {
      label: '完成进度(%)',
      prop: 'completionProgress',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.completionProgress}
            onChange={e => {
              iptChange(index, 'signingList', 'completionProgress', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.completionProgress
        );
      }
    },
    {
      label: '去年同期',
      prop: 'synchronismLastYear',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.synchronismLastYear}
            onChange={e => {
              iptChange(index, 'signingList', 'synchronismLastYear', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.synchronismLastYear
        );
      }
    },
    {
      label: '增减额',
      prop: 'incrDecrease',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecrease}
            onChange={e => {
              iptChange(index, 'signingList', 'incrDecrease', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecrease
        );
      }
    },
    {
      label: '增减率(%)',
      prop: 'incrDecreaseRate',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecreaseRate}
            onChange={e => {
              iptChange(index, 'signingList', 'incrDecreaseRate', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecreaseRate
        );
      }
    }
  ];
};
const useReceiptColumns = ({ isEdit = false, iptChange = () => { } }) => {
  const edit = row => isEdit && row.dept !== '合计';

  return [
    {
      label: '部门',
      prop: 'dept',
      width: 200,
      render: ({ row, index }) => {
        if ([4, 7, 8].includes(index)) {
          return <div style="text-indent:40px">{row.dept}</div>;
        } else {
          return row.dept;
        }
      }
    },
    {
      label: '实际回款',
      prop: 'actualCollection',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.actualCollection}
            onChange={e => {
              iptChange(index, 'receiptList', 'actualCollection', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.actualCollection
        );
      }
    },
    {
      label: '计划回款',
      prop: 'planCollection',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.planCollection}
            onChange={e => {
              iptChange(index, 'receiptList', 'planCollection', e);
            }}
            maxlength={50}
            placeholder="请输入"
          />
        ) : (
          row.planCollection
        );
      }
    },
    {
      label: '完成进度(%)',
      prop: 'completionProgress',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.completionProgress}
            onChange={e => {
              iptChange(index, 'receiptList', 'completionProgress', e);
            }}
            maxlength={50}
            placeholder="请输入"
          />
        ) : (
          row.completionProgress
        );
      }
    },
    {
      label: '去年同期',
      prop: 'synchronismLastYear',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.synchronismLastYear}
            onChange={e => {
              iptChange(index, 'receiptList', 'synchronismLastYear', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.synchronismLastYear
        );
      }
    },
    {
      label: '增减额',
      prop: 'incrDecrease',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecrease}
            onChange={e => {
              iptChange(index, 'receiptList', 'incrDecrease', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecrease
        );
      }
    },
    {
      label: '增减率(%)',
      prop: 'incrDecreaseRate',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecreaseRate}
            onChange={e => {
              iptChange(index, 'receiptList', 'incrDecreaseRate', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecreaseRate
        );
      }
    }
  ];
};
const useIncomeColumns = ({ isEdit = false, iptChange = () => { } }) => {
  const edit = row => isEdit && row.dept !== '合计';
  return [
    {
      label: '部门',
      prop: 'dept',
      width: 200,
      render: ({ row, index }) => {
        if ([4, 7, 8].includes(index)) {
          return <div style="text-indent:40px">{row.dept}</div>;
        } else {
          return row.dept;
        }
      }
    },
    {
      label: '实际确收',
      prop: 'actualIncomme',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.actualIncomme}
            onChange={e => {
              iptChange(index, 'incomeList', 'actualIncomme', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.actualIncomme
        );
      }
    },
    {
      label: '计划确收',
      prop: 'planIncome',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.planIncome}
            onChange={e => {
              iptChange(index, 'incomeList', 'planIncome', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.planIncome
        );
      }
    },
    {
      label: '完成进度(%)',
      prop: 'completionProgress',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.completionProgress}
            onChange={e => {
              iptChange(index, 'incomeList', 'completionProgress', e);
            }}
            maxlength={50}
            placeholder="请输入"
          />
        ) : (
          row.completionProgress
        );
      }
    },
    {
      label: '去年同期',
      prop: 'synchronismLastYear',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.synchronismLastYear}
            onChange={e => {
              iptChange(index, 'incomeList', 'synchronismLastYear', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.synchronismLastYear
        );
      }
    },
    {
      label: '增减额',
      prop: 'incrDecrease',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecrease}
            onChange={e => {
              iptChange(index, 'incomeList', 'incrDecrease', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecrease
        );
      }
    },
    {
      label: '增减率(%)',
      prop: 'incrDecreaseRate',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return edit(row) ? (
          <MoneyInput
            controls={false}
            modelValue={row.incrDecreaseRate}
            onChange={e => {
              iptChange(index, 'incomeList', 'incrDecreaseRate', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.incrDecreaseRate
        );
      }
    }
  ];
};
const useProfitsColumns = ({ isEdit = false, iptChange = () => { } }) => {
  return [
    {
      label: '公司',
      prop: 'profit',
      width: 100
    },
    {
      label: '智慧锦城',
      prop: 'zhjc',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.zhjc}
            onChange={e => {
              iptChange(index, 'profitsList', 'zhjc', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.zhjc
        );
      }
    },
    {
      label: '房联云码',
      prop: 'funi',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.funi}
            onChange={e => {
              iptChange(index, 'profitsList', 'funi', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.funi
        );
      }
    },
    {
      label: '成都电子',
      prop: 'cddz',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.cddz}
            onChange={e => {
              iptChange(index, 'profitsList', 'cddz', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.cddz
        );
      }
    },
    {
      label: '南充电子',
      prop: 'ncdz',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.ncdz}
            onChange={e => {
              iptChange(index, 'profitsList', 'ncdz', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.ncdz
        );
      }
    },
    {
      label: '房联天用',
      prop: 'flty',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.flty}
            onChange={e => {
              iptChange(index, 'profitsList', 'flty', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.flty
        );
      }
    },
    {
      label: '精坐标',
      prop: 'jzb',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.jzb}
            onChange={e => {
              iptChange(index, 'profitsList', 'jzb', e);
            }}
            maxlength={50}
            placeholder="请输入"
          />
        ) : (
          row.jzb
        );
      }
    },
    {
      label: '合并抵消',
      prop: 'mergeOffset',
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <MoneyInput
            controls={false}
            modelValue={row.mergeOffset}
            onChange={e => {
              iptChange(index, 'profitsList', 'mergeOffset', e);
            }}
            placeholder="请输入"
          />
        ) : (
          row.mergeOffset
        );
      }
    }
  ];
};

const useAccountReceivableColumns = () => {
  return [
    {
      label: '部门',
      prop: 'deptName'
    },
    {
      label: '项目',
      prop: 'projectName'
    },
    {
      label: '甲方',
      prop: 'partyA'
    },
    {
      label: '年初余额',
      prop: 'receiptAmount',
      align: 'right'
    },
    {
      label: '本期新增',
      prop: 'currentIssueAdd',
      align: 'right'
    },
    {
      label: '本期收回',
      prop: 'receivableAmount',
      align: 'right'
    },
    {
      label: '期末余额',
      prop: 'overdueAmount',
      align: 'right'
    },
    {
      label: '逾期天数',
      prop: 'overdueDays'
    },
    {
      label: '备注',
      prop: 'notes'
    }
  ];
};
const getBudgetFields = () => {
  return {
    type: '',
    currentMonthCompleted: 0,
    accumulatedComplet: 0,
    annualTarget: 0
  };
};
const getCompareFields = () => {
  return {
    projectName: '',
    accumulatedInJanuary: 0,
    synchronismLastYear: 0,
    incrDecrease: 0,
    incrDecreaseRate: 0,
    desc: ''
  };
};
const getSigningFields = () => {
  return {
    dept: '',
    actualSigning: 0,
    planSigning: 0,
    completionProgress: 0,
    synchronismLastYear: 0,
    incrDecrease: 0,
    incrDecreaseRate: 0
  };
};
const getReceiptFields = () => {
  return {
    dept: '',
    actualCollection: 0,
    planCollection: 0,
    completionProgress: 0,
    synchronismLastYear: 0,
    incrDecrease: 0,
    incrDecreaseRate: 0
  };
};
const getIncomeFields = () => {
  return {
    dept: '',
    actualIncomme: 0,
    planIncome: 0,
    completionProgress: 0,
    synchronismLastYear: 0,
    incrDecrease: 0,
    incrDecreaseRate: 0
  };
};
const getProfitsFields = () => {
  return {
    profit: '',
    zhjc: 0,
    funi: 0,
    cddz: 0,
    ncdz: 0,
    flty: 0,
    jzb: 0,
    mergeOffset: 0
  };
};
export {
  useSchema,
  useRule,
  useBudgetColumns,
  useCompareColumns,
  useCostInputListColumns,
  useSigningColumns,
  useReceiptColumns,
  useIncomeColumns,
  useProfitsColumns,
  useAccountReceivableColumns,
  getBudgetFields,
  getCompareFields,
  getSigningFields,
  getReceiptFields,
  getIncomeFields,
  getProfitsFields
};
