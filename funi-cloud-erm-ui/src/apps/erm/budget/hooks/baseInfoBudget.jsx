/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-01 14:14:48
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-07 19:30:27
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/hooks/baseInfoBudget.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import Verifics from '@/apps/erm/component/verifica/index.vue';
import InputMoneyNumber from '@/apps/erm/component/inputMoneyNumber/index.vue';
import ermHooks from '@/apps/erm/hooks/index.js';
import { budgetProjectReminderHttp } from '@/apps/erm/budget/hooks/api';
import { ElNotification } from 'element-plus';
import { ref } from 'vue';
let y = new Date().getFullYear() - 25;
let yearList = [];
for (let i = 0; i < 50; i++) {
  yearList.push(y + i);
}

// 催办
const loading = ref(false);
const setUrge = async row => {
  loading.value = true;
  const taskSn = row.columnType == 'manageProject' ? row.projectSn : row.contractSn;
  const dicReminderTypeCode = row.columnType == 'manageProject' ? '1' : '2';
  const reminderContent = row.textarea;
  await budgetProjectReminderHttp({
    taskSn,
    dicReminderTypeCode,
    reminderContent
  }).finally(() => {
    loading.value = false;
  });
  ElNotification({
    title: '催办成功',
    type: 'success'
  });
};

// 表单配置
export const useSchema = params => {
  return [
    {
      label: '计划名称',
      component: params.isEdit ? 'el-input' : null,
      props: {
        maxlength: 50
      },
      prop: 'planName'
    },
    {
      label: '年份',
      component: params.isEdit
        ? () => {
            return (
              <el-select
                onChange={e => {
                  params?.yearChange(e);
                }}
              >
                {yearList.map(item => (
                  <el-option label={item} value={item} />
                ))}
              </el-select>
            );
          }
        : null,
      prop: 'planYear',
      props: {
        placeholder: '请选择年份',
        style: {
          width: '100%'
        }
      }
    },

    {
      label: '计划利润(万元)',
      component: () => <InputMoneyNumber />,
      prop: 'planProfit',
      props: {
        moneyCapitalShow: false,
        isEdit: params.isEdit,
        unit: '万元'
      }
    },
    // {
    //   label: '计划签单(万元)',
    //   component: () => <InputMoneyNumber />,
    //   prop: 'planOrder',
    //   props: {
    //     isEdit: false,
    //     moneyCapitalShow: false,
    //     unit: '万元'
    //   }
    // },
    // {
    //   label: '计划回款(万元)',
    //   component: () => <InputMoneyNumber />,
    //   prop: 'planCollection',
    //   props: {
    //     isEdit: false,
    //     moneyCapitalShow: false,
    //     unit: '万元'
    //   }
    // },
    // {
    //   label: '调整后确收(万元)',
    //   component: () => <InputMoneyNumber />,
    //   prop: 'adjustedReceipt',
    //   props: {
    //     isEdit: false,
    //     moneyCapitalShow: false,
    //     unit: '万元'
    //   }
    // },
    // {
    //   label: '实际签单(万元)',
    //   component: () => <InputMoneyNumber />,
    //   prop: 'actualOrder',
    //   props: {
    //     isEdit: false,
    //     moneyCapitalShow: false,
    //     unit: '万元'
    //   }
    // },
    // {
    //   label: '实际回款(万元)',
    //   component: () => <InputMoneyNumber />,
    //   prop: 'actualCollection',
    //   props: {
    //     isEdit: false,
    //     moneyCapitalShow: false,
    //     unit: '万元'
    //   }
    // },
    // {
    //   label: '实际确收(万元)',
    //   component: () => <InputMoneyNumber />,
    //   prop: 'actualReceipt',
    //   props: {
    //     isEdit: false,
    //     moneyCapitalShow: false,
    //     unit: '万元'
    //   }
    // },
    // {
    //   label: '计划确收(万元)',
    //   component: () => <InputMoneyNumber />,
    //   prop: 'planReceipt',
    //   props: {
    //     isEdit: false,
    //     moneyCapitalShow: false,
    //     unit: '万元'
    //   }
    // },
    // {
    //   label: '确收调整(万元)',
    //   component: () => <InputMoneyNumber />,
    //   prop: 'adjustedQuota',
    //   props: {
    //     isEdit: false,
    //     moneyCapitalShow: false,
    //     unit: '万元'
    //   }
    // },
    {
      label: '备注',
      component: params.isEdit
        ? 'el-input'
        : ({ formModel }) => {
            return (
              <span
                style={{
                  'white-space': 'pre-wrap'
                }}
              >
                {formModel.remark || '--'}
              </span>
            );
          },
      prop: 'remark',
      colProps: {
        span: 24
      },
      props: {
        placeholder: '请输入项目详细说明',
        autosize: { minRows: 3, maxRows: 6 },
        type: 'textarea',
        maxlength: 200
      }
    }
  ];
};

// 验证计划名称
// 表单验证
export const useRules = () => {
  return {
    planName: [{ required: true, message: '必填', trigger: 'blur' }], // 计划名称
    planYear: [{ required: true, message: '必填', trigger: 'change' }], // 计划年份
    planProfit: [{ required: true, message: '必填', trigger: 'change' }] // 计划利润
  };
};

export const pColumns = ({
  isEdit,
  iptChange = () => {},
  contactRef = {},
  onDblclick = {},
  textRef,
  isFullscreen,
  operation,
  tableData
}) => {
  const reset_v = (key, el) => {
    contactRef[key] = el;
  };
  // 金额验证
  let validateMoney_table = (value, callback) => {
    if (!isEdit) {
      callback();
      return;
    }
    if (value === '' || value === null || value === void 0) {
      callback('必填');
    } else {
      callback();
    }
  };

  let validateMoney_number = (value, callback) => {
    if ($utils.isNil(value)) {
      callback();
    } else if (/^[0-9]\d*$/.test(value)) {
      callback();
    } else {
      callback('请填写正确的序号');
    }
  };

  let hasProjectOrContract = tableData.filter(item => ['contract', 'manageProject'].includes(item.columnType))?.length;

  return [
    ...(isEdit
      ? [
          {
            type: 'selection',
            width: 10,
            selectable: (row, index) => {
              if (
                isEdit &&
                (row.columnType === 'total' ||
                  row.columnType === 'other' ||
                  row.columnType === 'allTotal' ||
                  row.columnType === 'deptTotal')
              ) {
                return false;
              } else {
                return true;
              }
            }
          }
        ]
      : []),
    ...(!isEdit
      ? [
          {
            label: ' ',
            prop: '',
            showOverflowTooltip: false,
            width: 50,

            render: ({ row, index }) => {
              if (row.existChild) {
                return (
                  <div class={`upOrDown ${row.columnType}`}>
                    <funi-icon
                      icon={
                        !row.openStatus
                          ? row.columnType === 'manageProject'
                            ? 'icon-park-outline:down'
                            : 'charm:chevrons-down'
                          : row.columnType === 'manageProject'
                          ? 'icon-park-outline:up'
                          : 'charm:chevrons-up'
                      }
                      onClick={() => {
                        operation(row, index);
                      }}
                    ></funi-icon>
                  </div>
                );
              } else {
                return <span></span>;
              }
            }
          }
        ]
      : []),
    {
      label: '副总级部门',
      prop: 'deputyDeptName',
      className: 'noHight',
      showOverflowTooltip: false,
      minWidth: isFullscreen.value ? '200px' : '100px',
      render: ({ row, index }) => {
        return (
          <el-tooltip popper-options={{ boundariesElement: 'body' }} placement="top" content={row.deputyDeptName}>
            <div
              style={{
                position: 'relative',
                cursor: 'pointer',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              {row.deputyDeptName}
            </div>
          </el-tooltip>
        );
      }
    },
    {
      label: '部门',
      prop: 'deptName',
      className: 'noHight',
      showOverflowTooltip: false,
      minWidth: isFullscreen.value ? '200px' : '100px',
      render: ({ row, index }) => {
        return (
          <el-tooltip
            disabled={!!!row.deptName.trim()}
            popper-options={{ boundariesElement: 'body' }}
            placement="top"
            content={row.deptName}
          >
            <div
              style={{
                position: 'relative',
                cursor: 'pointer',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              {row.deptName}
            </div>
          </el-tooltip>
        );
      }
    },
    {
      label: '序号',
      prop: 'projectPriority',
      width: 60,
      render: ({ row, index }) => {
        let c = <el-input placeholder="序号" />;
        return isEdit &&
          !row.dicBudgetTagName &&
          row.columnType !== 'total' &&
          row.columnType !== 'allTotal' &&
          row.columnType !== 'other' &&
          row.columnType !== 'deptTotal' ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_projectPriority`, el);
            }}
            style={{
              display: 'inline-block',
              height: '100%',
              width: '100%'
            }}
            value={row.projectPriority}
            c={c}
            key={`${row.uuId}_projectPriority`}
            rule={[
              {
                validator: validateMoney_number
              }
            ]}
            onChange={e => {
              iptChange(index, 'projectPriority', e);
            }}
          ></Verifics>
        ) : (
          <span
            id={`projectPriority_${index}`}
            ref={e => {
              textRef.value[`projectPriority_${index}`] = e;
            }}
            style={{
              display: 'inline-block',
              height: '100%',
              minHeight: '23px',
              width: '100%'
            }}
            onDblclick={() => onDblclick(row, 'projectPriority', index)}
          >
            {row.columnType !== 'other' ? (row.dicBudgetTagName ? row.dicBudgetTagName : row.projectPriority) : ''}
          </span>
        );
      }
    },
    {
      label: '预算项目/经营项目/合同名称',
      prop: 'budgetProjectName',
      width: 230,
      render: ({ row, index }) => {
        return (
          <span
            id={`budgetProjectName_${index}`}
            ref={e => {
              textRef.value[`budgetProjectName_${index}`] = e;
            }}
            style={{
              display: 'inline-block',
              height: '100%',
              width: '100%'
            }}
            onDblclick={() =>
              row.columnType !== 'total' &&
              row.columnType !== 'allTotal' &&
              row.columnType !== 'other' &&
              onDblclick(row, 'budgetProjectName', index)
            }
          >
            {row.budgetProjectName}
          </span>
        );
      }
    },

    {
      label: '确收',
      prop: 'planReceiptInfo',
      align: 'center',
      children: [
        {
          label: '年初计划',
          prop: 'planReceipt',
          width: 100,
          align: 'right',
          slots: {
            header: 'c'
          },
          render: ({ row, index }) => {
            let c = <InputMoneyNumber placeholder="年初计划" isEdit={isEdit} />;
            return isEdit &&
              row.columnType !== 'total' &&
              row.columnType !== 'allTotal' &&
              row.columnType !== 'deptTotal' ? (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_planReceipt`, el);
                }}
                value={row.planReceipt}
                c={c}
                key={`${row.uuId}_planReceipt`}
                rule={[
                  {
                    validator: (value, callback) => validateMoney_table(value, callback)
                  }
                ]}
                onChange={e => {
                  iptChange(index, 'planReceipt', e);
                }}
              ></Verifics>
            ) : (
              <span
                id={`planReceipt_${index}`}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                ref={e => {
                  textRef.value[`planReceipt_${index}`] = e;
                }}
                onDblclick={() => onDblclick(row, 'planReceipt', index)}
              >
                {ermHooks.erm_intl(row.planReceipt)}
              </span>
            ); //详情
          }
        },
        {
          label: '确收调整',
          prop: 'adjustedQuota',
          width: 100,
          align: 'right',
          slots: {
            header: 'd'
          },
          render: ({ row, index }) => {
            let c = <InputMoneyNumber placeholder="确收调整" isEdit={isEdit && row.columnType !== 'total'} />;
            return isEdit &&
              row.columnType !== 'total' &&
              row.columnType !== 'allTotal' &&
              row.columnType !== 'deptTotal' ? (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_adjustedQuota`, el);
                }}
                value={row.adjustedQuota}
                c={c}
                key={`${row.uuId}_adjustedQuota`}
                rule={[
                  {
                    validator: (value, callback) => validateMoney_table(value, callback)
                  }
                ]}
                onChange={e => {
                  iptChange(index, 'adjustedQuota', e);
                }}
              ></Verifics>
            ) : (
              <span
                id={`adjustedQuota_${index}`}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                ref={e => {
                  textRef.value[`adjustedQuota_${index}`] = e;
                }}
                onDblclick={() => onDblclick(row, 'adjustedQuota', index)}
              >
                {ermHooks.erm_intl(row.adjustedQuota)}
              </span>
            );
          }
        },
        {
          label: '全年预计',
          prop: 'adjustedReceipt',
          align: 'right',
          width: 110,
          render: ({ row, index }) => {
            if (isEdit) {
              return <span>{ermHooks.erm_intl(row.adjustedReceipt)}</span>;
            } else if (['contract', 'manageProject'].includes(row.columnType)) {
              return <span> {ermHooks.erm_intl(row.adjustedReceipt)}</span>;
            } else {
              return <InputMoneyNumber isEdit={false} modelValue={row.adjustedReceipt} />;
            }
          }
        },
        {
          label: '实际完成',
          prop: 'actualReceipt',
          width: 100,
          align: 'right',
          render: ({ row, index }) => {
            if (isEdit) {
              return ermHooks.erm_intl(row.actualReceipt);
            } else {
              return <InputMoneyNumber isEdit={false} modelValue={row.actualReceipt} />;
            }
          }
        }
      ]
    },
    {
      label: '签单',
      prop: 'planOrderInfo',
      align: 'center',
      children: [
        {
          label: '年初计划',
          prop: 'planOrder',
          width: 100,
          align: 'right',
          slots: {
            header: 'a'
          },
          render: ({ row, index }) => {
            let c = <InputMoneyNumber placeholder="年初计划" isEdit={isEdit} />;
            return isEdit &&
              row.columnType !== 'total' &&
              row.columnType !== 'allTotal' &&
              row.columnType !== 'deptTotal' ? (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_planOrder`, el);
                }}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                value={row.planOrder}
                c={c}
                key={`${row.uuId}_planOrder`}
                rule={[
                  {
                    validator: validateMoney_table
                  }
                ]}
                onChange={e => {
                  iptChange(index, 'planOrder', e);
                }}
              ></Verifics>
            ) : (
              <span
                id={`planOrder_${index}`}
                ref={e => {
                  textRef.value[`planOrder_${index}`] = e;
                }}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                onDblclick={() => onDblclick(row, 'planOrder', index)}
              >
                {ermHooks.erm_intl(row.planOrder)}
              </span>
            );
          }
        },
        {
          label: '预计调整',
          prop: 'adjustOrder',
          width: 100,
          align: 'right',
          render: ({ row, index }) => {
            let c = <InputMoneyNumber placeholder="预计调整" isEdit={isEdit} />;
            return isEdit &&
              row.columnType !== 'total' &&
              row.columnType !== 'allTotal' &&
              row.columnType !== 'deptTotal' ? (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_adjustOrder`, el);
                }}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                value={row.adjustOrder}
                c={c}
                key={`${row.uuId}_adjustOrder`}
                rule={[
                  {
                    validator: validateMoney_table
                  }
                ]}
                onChange={e => {
                  iptChange(index, 'adjustOrder', e);
                }}
              ></Verifics>
            ) : (
              <span
                id={`adjustOrder_${index}`}
                ref={e => {
                  textRef.value[`adjustOrder_${index}`] = e;
                }}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                onDblclick={() => onDblclick(row, 'adjustOrder', index)}
              >
                {ermHooks.erm_intl(row.adjustOrder)}
              </span>
            );
          }
        },
        {
          label: '全年预计',
          prop: 'adjustAfterOrder',
          align: 'right',
          width: 100,
          render: ({ row, index }) => {
            if (isEdit) {
              return ermHooks.erm_intl(row.adjustAfterOrder);
            } else {
              return <InputMoneyNumber isEdit={false} modelValue={row.adjustAfterOrder} />;
            }
          }
        },
        {
          label: '实际完成',
          prop: 'actualOrder',
          width: 100,
          align: 'right',
          render: ({ row, index }) => {
            if (isEdit) {
              return ermHooks.erm_intl(row.actualOrder);
            } else {
              return <InputMoneyNumber isEdit={false} modelValue={row.actualOrder} />;
            }
          }
        }
      ]
    },
    {
      label: '回款',
      prop: 'planCollectionInfo',
      align: 'center',
      children: [
        {
          label: '全年计划',
          prop: 'planCollection',
          width: 100,
          align: 'right',
          slots: {
            header: 'b'
          },
          render: ({ row, index }) => {
            let c = <InputMoneyNumber placeholder="全年计划" isEdit={isEdit} />;
            return isEdit &&
              row.columnType !== 'total' &&
              row.columnType !== 'allTotal' &&
              row.columnType !== 'deptTotal' ? (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_planCollection`, el);
                }}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                value={row.planCollection}
                c={c}
                key={`${row.uuId}_planCollection`}
                rule={[
                  {
                    validator: validateMoney_table
                  }
                ]}
                onChange={e => {
                  iptChange(index, 'planCollection', e);
                }}
              ></Verifics>
            ) : (
              <span
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                id={`planCollection_${index}`}
                ref={e => {
                  textRef.value[`planCollection_${index}`] = e;
                }}
                onDblclick={() => onDblclick(row, 'planCollection', index)}
              >
                {ermHooks.erm_intl(row.planCollection)}
              </span>
            );
          }
        },
        {
          label: '实际完成',
          prop: 'actualCollection',
          align: 'right',
          width: 100,
          render: ({ row, index }) => {
            if (isEdit) {
              return ermHooks.erm_intl(row.actualCollection);
            } else {
              return <InputMoneyNumber isEdit={false} modelValue={row.actualCollection} />;
            }
          }
        }
      ]
    },
    {
      label: '外采',
      prop: 'planCollectionInfo',
      align: 'center',
      children: [
        {
          label: '年初计划',
          prop: 'unplanPurchaseCost',
          width: 120,
          align: 'right',
          slots: {
            header: 'e'
          },
          render: ({ row, index }) => {
            let c = <InputMoneyNumber placeholder="年初计划" isEdit={isEdit} />;
            return isEdit &&
              row.columnType !== 'total' &&
              row.columnType !== 'allTotal' &&
              row.columnType !== 'deptTotal' ? (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_unplanPurchaseCost`, el);
                }}
                value={row.unplanPurchaseCost}
                c={c}
                key={`${row.uuId}_unplanPurchaseCost`}
                rule={[
                  {
                    validator: (value, callback) => validateMoney_table(value, callback)
                  }
                ]}
                onChange={e => {
                  iptChange(index, 'unplanPurchaseCost', e);
                }}
              ></Verifics>
            ) : (
              <span
                id={`unplanPurchaseCost_${index}`}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                ref={e => {
                  textRef.value[`unplanPurchaseCost_${index}`] = e;
                }}
                onDblclick={() => onDblclick(row, 'unplanPurchaseCost', index)}
              >
                {ermHooks.erm_intl(row.unplanPurchaseCost)}
              </span>
            );
          }
        },
        {
          label: '全年预计',
          prop: 'planPurchaseEstimate',
          width: 120,
          align: 'right',
          slots: {
            header: 'e'
          },
          render: ({ row, index }) => {
            let c = <InputMoneyNumber placeholder="全年预计" isEdit={isEdit} />;
            return isEdit &&
              row.columnType !== 'total' &&
              row.columnType !== 'allTotal' &&
              row.columnType !== 'deptTotal' ? (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_planPurchaseEstimate`, el);
                }}
                value={row.planPurchaseEstimate}
                c={c}
                key={`${row.uuId}_planPurchaseEstimate`}
                rule={[
                  {
                    validator: (value, callback) => validateMoney_table(value, callback)
                  }
                ]}
                onChange={e => {
                  iptChange(index, 'planPurchaseEstimate', e);
                }}
              ></Verifics>
            ) : (
              <span
                id={`planPurchaseEstimate_${index}`}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                ref={e => {
                  textRef.value[`planPurchaseEstimate_${index}`] = e;
                }}
                onDblclick={() => onDblclick(row, 'planPurchaseEstimate', index)}
              >
                {ermHooks.erm_intl(row.planPurchaseEstimate)}
              </span>
            );
          }
        }
      ]
    },
    {
      label: '成本',
      prop: 'expectPerformanceInfo',
      align: 'center',
      children: [
        {
          label: '预计绩效',
          prop: 'expectPerformance',
          width: 100,
          align: 'right',
          slots: {
            header: 'f'
          },
          render: ({ row, index }) => {
            let c = <InputMoneyNumber placeholder="预计绩效" isEdit={isEdit} />;
            return isEdit &&
              row.columnType !== 'total' &&
              row.columnType !== 'allTotal' &&
              row.columnType !== 'deptTotal' ? (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_expectPerformance`, el);
                }}
                value={row.expectPerformance}
                c={c}
                key={`${row.uuId}_expectPerformance`}
                rule={[
                  {
                    validator: (value, callback) => validateMoney_table(value, callback)
                  }
                ]}
                onChange={e => {
                  iptChange(index, 'expectPerformance', e);
                }}
              ></Verifics>
            ) : (
              <span
                id={`expectPerformance_${index}`}
                ref={e => {
                  textRef.value[`expectPerformance_${index}`] = e;
                }}
                style={{
                  display: 'inline-block',
                  height: '100%',
                  width: '100%'
                }}
                onDblclick={() => onDblclick(row, 'expectPerformance', index)}
              >
                {ermHooks.erm_intl(row.expectPerformance)}
              </span>
            ); //详情
          }
        },
        {
          label: '实际投入',
          prop: 'actualCost',
          width: 100,
          align: 'right',
          render: ({ row, index }) => {
            if (isEdit) {
              return ermHooks.erm_intl(row.actualCost);
            } else if (['contract'].includes(row.columnType)) {
              return <span> {ermHooks.erm_intl(row.actualCost)}</span>;
            } else {
              return <InputMoneyNumber isEdit={false} modelValue={row.actualCost} />;
            }
          }
        }
      ]
    },

    ...(hasProjectOrContract && !isEdit
      ? [
          {
            label: '催办',
            prop: 'operation',
            width: 55,
            fixed: 'right',
            align: 'center',
            render: ({ row, index }) => {
              if (!isEdit && ['contract', 'manageProject'].includes(row.columnType)) {
                return (
                  <div class="urge">
                    <el-popover placement="left" trigger="click" width={300}>
                      {{
                        reference: <funi-icon icon="heroicons-solid:bell"></funi-icon>,
                        default: (
                          <div>
                            <el-radio-group
                              v-model={row.urgeType}
                              onChange={e => {
                                let keyName = row.columnType === 'manageProject' ? 'budgetProjectName' : 'contractSn';
                                row.textarea = `你负责的“${row[keyName]}”${
                                  e == '1'
                                    ? '下有未关联的收款单，请及时关联'
                                    : '下有未发起或未完成流程的确收，请及时操作'
                                }`;
                              }}
                            >
                              <el-radio label="1" size="small">
                                回款
                              </el-radio>
                              <el-radio label="2" size="small">
                                确收
                              </el-radio>
                            </el-radio-group>
                            <el-input
                              v-model={row.textarea}
                              style="width: 100%"
                              placeholder="请输入"
                              show-word-limit
                              type="textarea"
                            />
                            <div style="margin-top:8px;text-align:right">
                              <el-button
                                loading={loading.value}
                                type="primary"
                                size="small"
                                onClick={() => {
                                  setUrge(row);
                                }}
                              >
                                确定
                              </el-button>
                            </div>
                          </div>
                        )
                      }}
                    </el-popover>
                  </div>
                );
              } else {
                return <span> </span>;
              }
            }
          }
        ]
      : [])
  ];
};

export const totalColumns = () => {
  return [
    {
      label: '确收',
      prop: 'planReceiptInfo',
      align: 'center',
      children: [
        {
          label: '年初计划',
          prop: 'planReceipt',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.planReceipt)}</span>;
          }
        },
        {
          label: '确收调整',
          prop: 'adjustedQuota',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.adjustedQuota)}</span>;
          }
        },
        {
          label: '全年预计',
          prop: 'adjustedReceipt',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.adjustedReceipt)}</span>;
          }
        },
        {
          label: '实际完成',
          prop: 'actualReceipt',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.actualReceipt)}</span>;
          }
        }
      ]
    },
    {
      label: '签单',
      prop: 'planOrderInfo',
      align: 'center',
      children: [
        {
          label: '年初计划',
          prop: 'planOrder',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.planOrder)}</span>;
          }
        },
        {
          label: '预计调整',
          prop: 'adjustOrder',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.adjustOrder)}</span>;
          }
        },
        {
          label: '全年预计',
          prop: 'adjustAfterOrder',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.adjustAfterOrder)}</span>;
          }
        },
        {
          label: '实际完成',
          prop: 'actualOrder',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.actualOrder)}</span>;
          }
        }
      ]
    },
    {
      label: '回款',
      prop: 'planCollectionInfo',
      align: 'center',
      children: [
        {
          label: '全年计划',
          prop: 'planCollection',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.planCollection)}</span>;
          }
        },
        {
          label: '实际完成',
          prop: 'actualCollection',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.actualCollection)}</span>;
          }
        }
      ]
    },
    {
      label: '外采',
      prop: 'planCollectionInfo',
      align: 'center',
      children: [
        {
          label: '年初计划',
          prop: 'unplanPurchaseCost',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.unplanPurchaseCost)}</span>;
          }
        },
        {
          label: '全年预计',
          prop: 'planPurchaseEstimate',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.planPurchaseEstimate)}</span>;
          }
        }
      ]
    },
    {
      label: '成本',
      prop: 'expectPerformanceInfo',
      align: 'center',
      children: [
        {
          label: '预计绩效',
          prop: 'expectPerformance',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.expectPerformance)}</span>;
          }
        },
        {
          label: '实际投入',
          prop: 'actualCost',
          render: ({ row, index }) => {
            return <span> {ermHooks.erm_intl(row.actualCost)}</span>;
          }
        }
      ]
    }
  ];
};
