/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-07-24 15:00:15
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-14 10:16:57
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/hooks/project.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';

export const userProjectColumns = ({ seeDateils }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '项目编号',
      prop: 'budgetProjectSn',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={seeDateils}
            auth={'ERM_PUDGETPROJECT_INFO'}
            text={row.budgetProjectSn}
          />
        );
      }
    },
    {
      label: '项目名称',
      prop: 'projectName'
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '创建时间',
      prop: 'createTime'
    },
    {
      label: '负责人',
      prop: 'employeeName'
    }
  ];
};

/**
 * @description 按钮配置
 * @param {function} addFunc
 * @param {function} editFun
 * @param {function} delFun
 * **/
export const useBtnsConfig = (
  addFunc = () => {},
  editFun = () => {},
  delFun = () => {},
  updateDetail = () => {},
  isDisabled = false
) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_PUDGETPROJECT_ADD" onClick={addFunc} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button disabled={isDisabled} v-auth="ERM_PUDGETPROJECT_EDIT" onClick={editFun} type="primary">
          编辑
        </el-button>
      )
    },
    {
      component: () => (
        <el-popconfirm
          title="确定删除选择数据？"
          width="220"
          onConfirm={() => {
            delFun();
          }}
        >
          {{
            reference: () => (
              <el-button disabled={isDisabled} v-auth="ERM_PUDGETPROJECT_DEL" type="primary">
                删除
              </el-button>
            )
          }}
        </el-popconfirm>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_PROJECTPUDGET_UPDATE" onClick={updateDetail} type="primary" disabled={isDisabled}>
          更新项目详情
        </el-button>
      )
    }
  ];
};
