<template>
  <div class="page-list">
    <funi-list-page-v2 ref="pageList" :cardTab="useCardTab" :teleported="false" />
    <CommentDialog
      v-model="showDialog"
      @refreshList="refreshList"
      :supervisionId="supervisionId"
      :conditions="conditions"
      :type="type"
    />
    <AuditDialog v-model="dialogVisible" @getData="getReason" />
    <urgentTargetList v-model="visible" ref="urgentDialog" @close="close" @confirm="confirm" />
  </div>
</template>
<script lang="jsx" setup>
import { computed, nextTick, ref, unref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { userColumns, useBtnsConfig } from '@/apps/erm/budget/hooks/supervision.jsx';
import AuditDialog from '@/apps/erm/component/auditDialog/index.vue';
import {
  querySupervisionContentListHttp,
  SupervisionContentDeleteHttp,
  SupervisionContentFinishHttp
} from '@/apps/erm/budget/hooks/supervisionApi.jsx';
import { creatBusHttp } from '@/apps/erm/budget/hooks/supervisionApi.jsx';
import CommentDialog from '@/apps/erm/budget/commponent/supervision/commentDialog.vue';
import { ElNotification } from 'element-plus';
import { submitBusiness } from '@/apps/erm/config/business.js';
import urgentTargetList from '@/apps/erm/budget/commponent/supervision/urgentTargetList.vue';

const router = useRouter();
const dialogVisible = ref(false);
const route = useRoute();
const pageList = ref(null);
const urgentDialog = ref(null);
let rowData = ref(void 0);
let showDialog = ref(false);
let supervisionId = ref(void 0);
let visible = ref(false);
let params = ref(void 0);
const reason = ref(void 0);
const type = ref();
const isEnable = ref(void 0);
let routeId = ref(route.query?.routeId || void 0);

let conditions = ref(void 0);

const lodaData = async (params, queryParams) => {
  conditions.value = { ...params, ...queryParams };
  rowData.value = void 0;
  let resData = await querySupervisionContentListHttp({ ...params, ...queryParams });
  return resData;
};

//查看详情
const seeDetails = row => {
  showDialog.value = true;
  supervisionId.value = row.businessNode || row.dicStatusCode == '0' ? row.lastId : row.id;
  type.value = 'info';
  // router.push({
  //   name: 'ermSupervisionInfo',
  //   query: {
  //     bizName: '详情',
  //     title: '督办件管理-详情',
  //     type: 'info',
  //     id: row.id,
  //     tab: ['督办件管理', '详情'].join('-')
  //   }
  // })
};

const urgent = async row => {
  visible.value = true;
  await nextTick();
  urgentDialog.value?.ref.getRow(row);
};

const close = () => {
  visible.value = false;
};

const confirm = () => {
  visible.value = false;
  pageList.value.reload({ resetPage: false });
};

watch(
  routeId,
  newVal => {
    if (newVal) {
      setTimeout(() => {
        seeDetails?.({ id: newVal });
      }, 500);
    }
  },
  {
    immediate: true
  }
);

const refreshList = () => {
  pageList.value.reload({ resetPage: false });
};

//新增
const addFunc = () => {
  router.push({
    name: 'ermSupervisionAdd',
    query: {
      title: '督办件管理-新增',
      bizName: '新建',
      type: 'add',
      tab: '督办件管理-新增'
    }
  });
};

const update = async () => {
  let datas = await creatBusHttp({ id: rowData.value.id, dicBusinessTypeCode: 'ERM_SUPERVISION_CONTENT_CHANGE' });
  if (!datas) return Promise.reject({});
  dialogVisible.value = true;
};

//获取审批原因
const getReason = async e => {
  reason.value = e?.reason;
  if (reason.value) {
    router.push({
      name: 'ermSupervisionAdd',
      query: {
        title: '督办件-延期申请',
        bizName: '审核',
        type: 'audit',
        operation: 'change',
        id: rowData.value.id,
        businessName: `督办件-延期申请`,
        tab: '督办件-延期申请',
        code: 'ERM_SUPERVISION_CONTENT_CHANGE',
        reason: reason.value,
        editType: 'buz'
      }
    });
  } else {
    ElNotification({
      title: '提示',
      message: '请输入审核原因',
      type: 'warning'
    });
  }
};

//编辑
const editFunc = row => {
  // if (row.dicDataStatusCode == '2') {
  //   ElNotification({
  //     title: '提示',
  //     message: '',
  //     type: 'warning'
  //   });
  //   return
  // }
  router.push({
    name: 'ermSupervisionAdd',
    query: {
      title: '督办件管理-编辑',
      bizName: '编辑',
      type: 'edit',
      id: row.id ? row.id : rowData.value.id,
      tab: ['督办件管理', '编辑'].join('-'),
      editType: row.id ? 'buz' : 'normal',
      businessId: row.businessId
    }
  });
};

//删除
const delFunc = async row => {
  if (row) {
    await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
    ElNotification({
      title: '删除成功',
      type: 'success'
    });
  } else {
    await SupervisionContentDeleteHttp({ supervisionId: rowData.value.id });
  }
  pageList.value.reload({ resetPage: false });
};
//完成
const completeFn = async () => {
  await SupervisionContentFinishHttp({ supervisionId: rowData.value.id });
  pageList.value.reload({ resetPage: false });
};

const rowClassName = ({ row }) => {
  let now = $utils.Date(new Date());
  let start = $utils.Date(row?.predictFinishTime);
  let diff = Math.floor(now.diff(start, 'day', true));
  if (row.dicSupervisionStatusCode === '1') return;
  if (diff >= 0) {
    return 'error';
  } else if (diff < 0 && diff > -10) {
    return 'warn';
  } else {
    return;
  }
};

const examFunc = row => {
  router.push({
    name: 'ermSupervisionInfo',
    query: {
      bizName: '审核',
      title: '督办件管理-审核',
      type: 'audit',
      id: row.id,
      tab: ['督办件管理', '审核'].join('-'),
      businessId: row.businessId
    }
  });
};

const seeBusinessInfo = row => {
  router.push({
    name: 'ermSupervisionInfo',
    query: {
      bizName: '审核',
      title: '督办件管理-详情',
      type: 'audit',
      id: row.id,
      tab: ['督办件管理', '详情'].join('-'),
      businessId: row.businessId
    }
  });
};

// 表单配置
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        lodaData: lodaData,
        btns: useBtnsConfig({
          addFunc,
          editFunc,
          delFunc,
          completeFn,
          update,
          isEnable: !rowData.value || isEnable.value
        }),
        checkOnRowClick: true,
        reloadOnActive: true,
        fixedButtons: true,
        rowClassName: rowClassName,
        stripe: false,
        columns: userColumns({ seeDetails, examFunc, editFunc, delFunc, seeBusinessInfo, urgent }),
        on: {
          rowClick: selection => {
            rowData.value = selection.row;
            if (selection.row.dicSupervisionStatusCode === '1' || selection.row?.dicStatusCode === '0')
              isEnable.value = true;
            else isEnable.value = false;
          },
          cellClick: (_r, _c) => {
            if (_c.property === 'latestReplyContext' && !!_r.latestReplyContext) {
              showDialog.value = true;
              supervisionId.value = _r.id;
              type.value = 'commentList';
            }
          }
          // rowClassName: _r => rowClassName(_r)
        }
      }
    }
  ];
});
</script>
<style lang="scss" scoped>
:deep(.el-table__row.warn) {
  background: #ffece0 !important;
}
:deep(.el-table__row.error) {
  background: #fa8d8d !important;
}
</style>
