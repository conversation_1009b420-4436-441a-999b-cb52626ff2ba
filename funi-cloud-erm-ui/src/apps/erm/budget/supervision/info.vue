<template>
  <div>
    <funi-detail
      :steps="steps"
      :auditButtons="buttons"
      :showWorkflow="true"
      :bizName="bizName"
      @auditEvent="auditEvent"
      :detailHeadOption="detailHeadOption || {}"
      :businessId="['info', 'audit'].includes(type) && businessId ? businessId : void 0"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from '@/apps/erm/budget/commponent/supervision/baseInfo.vue';
import { auditEndFunc } from '@/apps/erm/config/business.js';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
const multiTab = useMultiTab();
const route = useRoute();
const buttons = ref([]);
const id = route.query.id || '';
const bizName = route.query.bizName;
const type = route.query.type || '';
const businessId = ref('');
const infoData = ref('');
const detailHeadOption = computed(() => {
  return {
    title: route.query.title,
    no: infoData.value?.businessId,
    statusName: '节点',
    status: infoData.value?.businessNode
  };
});
const steps = computed(() => {
  return [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        isEdit: false,
        id,
        type
      },
      on: {
        updateInfo: data => {
          businessId.value = data.businessId;
          infoData.value = data;
        }
      }
    }
  ];
});
const auditEvent = () => {
  auditEndFunc(() => {
    multiTab.closeCurrentPage();
  });
};
</script>
