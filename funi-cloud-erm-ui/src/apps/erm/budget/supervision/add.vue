<template>
  <div>
    <funi-detail :bizName="bizName" :steps="steps" :showWorkflow="true" :detailHeadOption="detailHeadOption || {}" />
    <SubmitSuccess ref="submitSuccess" />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from '@/apps/erm/budget/commponent/supervision/baseInfo.vue';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
import { submitBusiness } from '@/apps/erm/config/business.js';
const route = useRoute();
const id = route.query.id || '';
const submitSuccess = ref();
const operation = route.query.operation || '';
const businessName = route.query.businessName || '';
const reason = route.query.reason || '';
const editType = route.query.editType || 'normal';
const type = route.query.type || 'normal';
const businessId = route.query.businessId || '';
const infoData = ref('');

const detailHeadOption = computed(() => {
  return {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点'
  };
});

const steps = computed(() => {
  return [
    {
      title: '基本信息',
      preservable: false,
      type: BaseInfo,
      props: {
        isEdit: true,
        id,
        operation,
        businessName,
        reason,
        editType,
        type,
        businessId
      },
      on: {
        updateInfo: data => {
          infoData.value = data;
        }
      }
    }
  ];
});
</script>
<style lang="scss" scoped></style>
