<template>
  <funi-detail :bizName="bizName" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from './../commponent/finance/baseInfo.vue';
const route = useRoute();
const bizName = ref(route.query.bizName);
const id = ref(route.query.id);
const type = ref(route.query.type);
const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    hideStatusBar: true,
    serialName: '',
    hideStatusName: true,
    no: ''
  };
  return obj;
});

const steps = computed(() => {
  let arr = [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit: true,
        type: type.value
      },
      on: {
        updateID: value => {
          id.value = value;
        }
      }
    }
  ];
  return arr;
});
</script>
