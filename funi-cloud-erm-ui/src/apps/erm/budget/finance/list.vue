<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-06 10:58:17
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-06 11:15:04
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/finance/list.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
  <FinanceDialog ref="financeDialog"></FinanceDialog>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref } from 'vue';
import { userColumns, useBtnsConfig } from './../hooks/finance.jsx';
import { queryFinancialReportListHttp, deleteHttp } from './../hooks/financeApi.js';
import { useRouter } from 'vue-router';
import FinanceDialog from './../commponent/financeDialog/index.vue';
const router = useRouter();
const rowData = ref(void 0);
const listPage = ref();

const lodaData = async (pages, parmas) => {
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryFinancialReportListHttp({
    ...pages,
    ...parmas
  });
  rowData.value = void 0;
  return data;
};
const financeDialog = ref();
onMounted(() => {});
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig({ addFunc, editFunc, delFunc, copyFunc, isEnable: !rowData.value }),
        lodaData: lodaData,
        reloadOnActive: true,
        fixedButtons: true,
        checkOnRowClick: true,
        columns: userColumns({ seeDateils }),
        on: {
          rowClick: selection => {
            rowData.value = selection.row;
          }
        }
      }
    }
  ];
});

const seeDateils = row => {
  // console.log(financeDialog.value);
  financeDialog.value.showPPT(row);
  // router.push({
  //   name: 'ermFinanceReportInfo',
  //   query: {
  //     title: row.title,
  //     bizName: '详情',
  //     type: 'info',
  //     id: row.id,
  //     tab: ['财务报告', row.title, '详情'].join('-')
  //   }
  // });
};

const editFunc = () => {
  router.push({
    name: 'ermFinanceReportAdd',
    query: {
      title: rowData.value?.title,
      bizName: 'edit',
      type: 'edit',
      id: rowData.value?.id,
      tab: ['财务报告', rowData.value?.title, '编辑'].join('-')
    }
  });
};

const delFunc = async () => {
  await deleteHttp({
    financialReportId: rowData.value?.id
  });
  listPage.value.reload({ resetPage: false });
  rowData.value = void 0;
};

const addFunc = () => {
  router.push({
    name: 'ermFinanceReportAdd',
    query: {
      title: '新建财务报告',
      bizName: '新建',
      type: 'add',
      tab: '财务报告-新建'
    }
  });
};
const copyFunc = () => {
  router.push({
    name: 'ermFinanceReportAdd',
    query: {
      title: rowData.value?.title,
      bizName: 'edit',
      type: 'copy',
      id: rowData.value?.id,
      tab: ['财务报告', rowData.value?.title, '复制'].join('-')
    }
  });
};
</script>
