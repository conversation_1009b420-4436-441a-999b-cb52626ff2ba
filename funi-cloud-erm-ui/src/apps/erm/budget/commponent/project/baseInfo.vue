<!--
 * @Author: co<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-26 10:25:23
 * @LastEditors: “linzhi” “<EMAIL>”
 * @LastEditTime: 2023-10-10 14:08:53
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/commponent/project/baseInfo.vue7777
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <GroupTitle title="基本信息"></GroupTitle>
    <funi-form :schema="schema" @get-form="setForm" :rules="rules" :col="2" :border="false"></funi-form>
    <SubmitSuccess ref="su"></SubmitSuccess>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onMounted, inject, nextTick } from 'vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { useSchema, useRule } from './../../hooks/baseInfo.jsx';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import { newHttp } from './../../hooks/api.js';
import { ElNotification } from 'element-plus';
import { getProjectInfo } from './../../hooks/getInfo.js';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
import { customerInfoHttp } from '@/apps/erm/supplier/hooks/supplierApi.jsx';
import { useRouter } from 'vue-router';
const router = useRouter();
const from = ref();
const loadingStatus = inject('loadingStatus');
const emit = defineEmits(['updateID', 'updateInfo']);
const companyId = ref();
const su = ref();
const contactPersonOption = ref([]);
const setForm = e => {
  from.value = e;
};
const props = defineProps({
  ...baseInfoProps
});

// onMounted
onMounted(() => {
  if (props.id)
    getProjectInfo(props.id, loadingStatus).then(res => {
      from.value.setValues({
        ...res,
        dicBudgetTagCode: res.dicBudgetTagCodeList,
        customerObj: {
          id: res.customerId,
          name: res.customerName
        }
      });
      getInitOption(res.customerId);
      setCompanyId(res.companyId);
      emit('updateInfo', res);
    });
});
const goPage = obj => {
  router.push({
    name: 'erm_projectManage_bulletin_two',
    query: {
      title: '看板详情',
      bizName: '详情',
      type: 'info',
      id: obj.id,
      tab: '看板详情'
    }
  });
};

const contactPersonChange = e => {
  let cur = contactPersonOption.value.find(item => item.code === e);
  from.value.setValues({
    contactPhone: cur.tel,
    contactPerson: cur.name
  });
};

const getInitOption = async id => {
  let { customerContactPersonVoList } = await customerInfoHttp({ customerId: id });
  contactPersonOption.value = customerContactPersonVoList.map(item => {
    return {
      name: item.name,
      code: item.id,
      tel: item.telephone
    };
  });
  let { contactPersonId, contactPerson, contactPhone } = from.value.getValues();
  from.value.setValues({
    contactPersonId: contactPersonId,
    contactPerson: contactPerson,
    contactPhone: contactPhone
  });
};

const changeCustomer = async id => {
  let { customerContactPersonVoList } = await customerInfoHttp({ customerId: id });
  contactPersonOption.value = customerContactPersonVoList.map(item => {
    return {
      name: item.name,
      code: item.id,
      tel: item.telephone
    };
  });
  from.value.setValues({
    contactPersonId: contactPersonOption.value[0].code,
    contactPerson: contactPersonOption.value[0].name,
    contactPhone: contactPersonOption.value[0].tel
  });
};

/**
 * @description 表单配置
 * **/
const schema = computed(() => {
  let { isEdit } = props;

  return useSchema({
    isEdit,
    resetDeptId,
    setCompanyId,
    getCompanyId,
    goPage,
    contactPersonOption,
    changeCustomer,
    contactPersonChange
  });
});

const resetDeptId = () => {
  from.value.setValues({
    deptId: void 0
  });
};
const setCompanyId = e => {
  companyId.value = e;
};
const getCompanyId = e => companyId.value;

const rules = computed(() => {
  return useRule({ isEdit: props.isEdit });
});

/**
 * @description 数据保存函数
 * **/
const saveDate = async type => {
  let data = await getData(type);
  if (!data) return;
  Reflect.deleteProperty(data, 'employeeObj');
  loadingStatus.value.status = true;
  let resolveData = await newHttp(data).finally(() => {
    loadingStatus.value.status = true;
  });
  emit('updateID', resolveData.id);
  from.value.setValues({
    budgetProjectSn: resolveData.sn
  });
  await nextTick();
  if (!props.id) return Promise.reject();
  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  } else {
    su.value.show();
  }
  return Promise.resolve({});
};

/**
 * @description 表单数据处理
 * **/
const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await from.value.validate();
  if (type == 'ts' || isValid) {
    let fromData = from.value.getValues();
    if (fromData?.employeeObj?.id) {
      fromData.employeeId = fromData?.employeeObj?.id;
      fromData.employeeName = fromData?.employeeObj?.name;
    }
    if (fromData?.customerObj.id) {
      fromData.customerId = fromData?.customerObj?.id;
      fromData.customerName = fromData?.customerObj?.name;
    }

    let obj = {
      id: props.id || '',
      ...fromData,
      dicBudgetTagCode: fromData?.dicBudgetTagCode?.toString()
    };
    companyId.value = obj.companyId;
    return obj;
  } else {
    return false;
  }
};

/**
 * @description 下一步 提交数据
 * **/
const submit = () => {
  return saveDate();
};
const ts = () => {
  return saveDate('ts');
};

defineExpose({
  submit,
  ts
});
</script>
