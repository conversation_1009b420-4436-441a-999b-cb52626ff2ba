<template>
  <div>
    <FuniCurdV2
      ref="curdRef"
      :lodaData="lodaData"
      :span-method="arraySpanMethod"
      row-key="id"
      :columns="columns"
      :pagination="false"
    />
  </div>
</template>
<script lang="jsx" setup>
import { computed, ref } from 'vue';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import { getProjectInfoList } from './../../hooks/getInfo.js';

const props = defineProps({
  ...baseInfoProps
  // data: {
  //   type: Object,
  //   default: () => {}
  // }
});

// const dataList = ref([]);

// watch(
//   () => props.data,
//   async newValue => {
//     console.log(newValue, '123123123');
//     dataList.value = newValue?.budgetProjectStatisticList;
//   }
// );

const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (rowIndex === 0) {
    if (columnIndex === 0) {
      return [1, 2];
    } else if (columnIndex === 1) {
      return [0, 0];
    }
  }
};

const lodaData = async () => {
  let data = await getProjectInfoList(props.id);
  return data;
};

// onMounted(() => {
//   getList();
// });

const getList = () => {
  getProjectInfo(props.id).then(res => {
    dataList.value = res.budgetProjectStatisticList.map(item => {
      return {
        id: $utils.guid(),
        ...item
      };
    });
  });
};

const columns = computed(() => {
  return [
    {
      label: '序号',
      type: 'index',
      align: 'center',
      index: index => {
        if (index === 0) {
          return '合计';
        } else {
          return index;
        }
      }
    },
    {
      label: '年度',
      prop: 'issue',
      align: 'center'
    },
    {
      label: '计划签单',
      prop: 'planOrder',
      align: 'right'
    },
    {
      label: '签单调整',
      prop: 'adjustOrder',
      align: 'right'
    },
    {
      label: '计划回款',
      prop: 'planReceipt',
      align: 'right'
    },
    {
      label: '计划确收',
      prop: 'planIncome',
      align: 'right'
    },
    {
      label: '确收调整',
      prop: 'adjustIncome',
      align: 'right'
    }
  ];
});
</script>
<style lang="scss" scoped></style>
