<template>
  <div>
    <funi-dialog v-model="budgetListData.dialogVisible" size="large" :destroy-on-close="true" title="添加预算项目">
      <div>
        <funi-list-page ref="pageList" class="list_page" :cardTab="useCardTab" :teleported="false" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, reactive, nextTick } from 'vue';
import { getBudgetProjectList } from '@/apps/erm/budget/hooks/api.js';
import { ElNotification } from 'element-plus';

const budgetListData = reactive({
  dialogVisible: false
});
const selectData = ref();
const pageList = ref();
let year = ref();
let snList = ref();
const emits = defineEmits(['updateData']);

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: columnsProject.value,
        lodaData: lodaData,
        useSearchV2: false,
        isShowSearch: true,
        searchConfig: searchConfig.value,
        colNumber: 5,
        fixedButtons: true,
        rowClassName: ({ row }) => {
          if (snList.value.indexOf(row.id) > -1) {
            return 'disabled-row';
          }
          return '';
        },
        on: {
          selectionChange: selection => {
            console.log(selection);
            selectData.value = selection;
          }
        }
      }
    }
  ];
});

// 表格配置
const columnsProject = computed(() => {
  return [
    {
      type: 'selection',
      width: '55px',
      fixed: 'left',
      selectable: (row, index) => {
        return snList.value.indexOf(row.id) < 0;
      }
    },
    {
      label: '预算项目编号',
      prop: 'budgetProjectSn'
    },
    {
      label: '预算项目名称',
      prop: 'projectName'
    },
    {
      label: '归属公司',
      prop: 'companyShortName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '负责人',
      prop: 'employeeName'
    }
  ];
});

//搜索配置
const searchConfig = computed(() => {
  let obj = {
    inline: true,
    schema: [
      {
        label: '预算项目名称',
        component: 'el-input',
        prop: 'budgetProjectName',
        colProps: {
          span: 8
        },
        props: {
          placeholder: '预算项目名称',
          clearable: true
        }
      },
      {
        label: '预算项目归属公司',
        component: 'el-input',
        prop: 'companyName',
        colProps: {
          span: 8
        },
        props: {
          placeholder: '预算项目归属公司',
          clearable: true
        }
      },
      {
        label: '预算项目归属部门',
        component: 'el-input',
        prop: 'deptName',
        colProps: {
          span: 8
        },
        props: {
          placeholder: '预算项目归属部门',
          clearable: true
        }
      },
      {
        label: '预算项目负责人',
        component: 'el-input',
        prop: 'employeeName',
        colProps: {
          span: 8
        },
        props: {
          placeholder: '预算项目负责人',
          clearable: true
        }
      }
    ]
  };
  return obj;
});

//确定
const confirmFunc = () => {
  if (!selectData.value) {
    ElNotification({ message: '请选择预算项目！', type: 'warning' });
    return;
  }
  let chooseData = selectData.value.map(item => {
    console.log(item, 'item');
    return {
      budgetProjectId: item.id,
      budgetProjectName: item.projectName,
      deptName: item.deptName,
      deptId: item.deptId,
      actualOrder: item.actualOrderAmount ?? 0,
      actualCollection: item.actualReceiptAmount ?? 0,
      actualReceipt: item.actualIncomeAmount ?? 0,
      planOrder: item.planOrder ?? 0,
      planCollection: item.planCollection ?? 0,
      planReceipt: item.planReceipt ?? 0,
      adjustedQuota: item.adjustedQuota ?? 0,
      adjustedReceipt: item.adjustedReceipt ?? 0,
      actualCost: item.actualCost ?? 0,
      companySort: item.companySort,
      deputyDeptName: item.deputyDeptName,
      dicBudgetTagCode: item.dicBudgetTagCode,
      dicBudgetTagName: item.dicBudgetTagName
    };
  });
  emits('updateData', chooseData);
  selectData.value = void 0;
  budgetListData.dialogVisible = false;
};
//取消
const cancelClick = () => {
  budgetListData.dialogVisible = false;
};

const show = async (tData, y) => {
  await nextTick();
  budgetListData.dialogVisible = true;
  year.value = y;
  snList.value = tData.map(item => item.budgetProjectId);
};

//加载列表数据
const lodaData = async (page, params) => {
  let resData = await getBudgetProjectList({
    year: year.value,
    ...page,
    ...params
  });
  console.log(resData);
  return resData;
};

defineExpose({
  show
});
</script>
<style scoped>
@import url('@/apps/erm/config/table_disabled.css');
</style>
