<template>
  <div class="base_info">
    <GroupTitle title="基本信息" />
    <funi-form @get-form="setForm" :schema="schema" :rules="rules" :col="3" />
    <funi-curd :rowSelection="false" :columns="tColumns" :data="tData" :pagination="false"></funi-curd>
    <GroupTitle title="关联预算项目" :isSymbol="props.isEdit ? true : false">
      <div v-if="props.isEdit">
        <el-button type="primary" @click="addFunc">添加预算项目</el-button>
        <el-button type="primary" @click="delFunc">删除</el-button>
      </div>
      <div v-else class="flex">
        <div class="month">
          <span>截止统计月份:</span>
          <el-date-picker
            v-model="month"
            :disabledDate="disabledDate"
            popper-class="popperClass"
            :teleported="false"
            value-format="YYYY-MM-DD"
            type="month"
            placeholder="请选择截止统计月份"
          />
        </div>
        <el-input v-model="keyword" style="width: 280px" placeholder="请输入部门名称/预算项目名称" />&emsp;
        <el-button type="primary" @click="() => getBudgetPlanInfo()">查询</el-button>
        <el-popover placement="top" :width="400" trigger="click">
          <div style="display: flex; gap: 10px; align-items: center">
            <el-checkbox-group v-model="checkList">
              <el-checkbox label="1" value="1"> 预算项目 </el-checkbox>
              <el-checkbox label="2" value="2"> 经营性项目 </el-checkbox>
              <el-checkbox label="3" value="3"> 收款合同 </el-checkbox>
            </el-checkbox-group>
            <el-button type="primary" size="small" @click="exportFunc">确定</el-button>
          </div>
          <template #reference>
            <el-button type="primary">导出</el-button>
          </template>
        </el-popover>

        <el-button type="primary" @click="toggleFullscreen">全屏</el-button>
      </div>
    </GroupTitle>
    <div id="fullscreen">
      <div :class="isShowBar ? 'isShowBar tableMerge' : 'tableMerge'">
        <div id="erm_popover_box" ref="ermPopoverBox">
          <div v-if="isInfo" class="popover_content_info">
            <span class="title">项目详情说明:</span>
            <div class="text">{{ projectDesc }}</div>
          </div>
          <div v-else class="popover_content">
            <InputMoneyNumber :precision="precision" v-model="nowNumber" />
            <el-button type="primary" @click="nowNumberChange">确定</el-button>
          </div>

          <div data-popper-arrow id="erm-arrow"></div>
        </div>
        <funi-curd
          rowKey="uuId"
          ref="curdRef"
          class="fixed-nth-last-child"
          @get-curd="
            e => {
              funi_curd = e;
            }
          "
          :data="tableState.tableData"
          :columns="columns"
          :pagination="false"
          :useTools="!isEdit ? useTools : false"
          :max-height="height - 50"
          :loading="loading"
          :span-method="mergeCells"
          :row-class-name="setRowClassName"
          :header-row-style="headerRowStyle"
          :cell-class-name="cellClassName"
          @select-all="getSelectData"
          @select="getSelectData"
          :stripe="false"
          border
        >
          <!-- <template #a>
            <div class="musterW"><span v-if="props.isEdit">*</span>年初计划</div>
          </template>

          <template #b>
            <div class="musterW"><span v-if="props.isEdit">*</span>计划回款</div>
          </template>

          <template #c>
            <div class="musterW"><span v-if="props.isEdit">*</span>计划确收</div>
          </template>

          <template #d>
            <div class="musterW"><span v-if="props.isEdit">*</span>确收调整</div>
          </template>

          <template #e>
            <div class="musterW"><span v-if="props.isEdit">*</span>计划外采成本</div>
          </template>

          <template #f>
            <div class="musterW"><span v-if="props.isEdit">*</span>预计绩效</div>
          </template> -->
        </funi-curd>
      </div>
    </div>
    <ChooseBudget ref="chooseModal" @updateData="updateData" />
    <SubmitSuccess ref="su" />

    <funi-dialog title="催办" v-model="dialogModel" size="small">
      <div>
        <el-radio-group v-model="radio">
          <el-radio value="1">回款</el-radio>
          <el-radio value="2">确收</el-radio>
        </el-radio-group>
      </div>
    </funi-dialog>
  </div>
</template>

<script lang="jsx" setup>
import { reactive, computed, nextTick, ref, onMounted, inject, watch, unref } from 'vue';
import { ElNotification, usePopper } from 'element-plus';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import InputMoneyNumber from '@/apps/erm/component/inputMoneyNumber/index.vue';
import { onClickOutside } from '@vueuse/core';
import screenfull from 'screenfull';
import { useSchema, useRules, pColumns, totalColumns } from '@/apps/erm/budget/hooks/baseInfoBudget.jsx';
import {
  getBudgetPlanInfoHttp,
  budgetPlanNewHttp,
  queryBudgetProjectInfoListExportHttp,
  queryBudgetProjectInfoListHttp,
  changePlanYearHttp,
  queryProjectOrContractListHttp
} from '@/apps/erm/budget/hooks/api.js';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import ChooseBudget from '@/apps/erm/budget/commponent/project_budget/modal/chooseBudget.vue';
import { mergeRowMethod } from '@/apps/erm/hooks/merge.jsx';
import { getProjectInfo } from '@/apps/erm/budget/hooks/getInfo.js';
import { createPopper } from '@popperjs/core';
import { useWindowSize, useDebounceFn } from '@vueuse/core';
const precision = ref(2);

let windowSize = useWindowSize();
const props = defineProps({
  ...baseInfoProps,
  planYear: {
    type: String,
    default: '2024'
  },
  auth: {
    type: Boolean,
    default: false
  }
});

const tData = ref([
  {
    planReceipt: 0,
    adjustedQuota: 0,
    adjustedReceipt: 0,
    actualReceipt: 0,
    planOrder: 0,
    adjustOrder: 0,
    adjustAfterOrder: 0,
    actualOrder: 0,
    planCollection: 0,
    actualCollection: 0,
    unplanPurchaseCost: 0,
    planPurchaseEstimate: 0,
    expectPerformance: 0,
    actualCost: 0
  }
]);

const emit = defineEmits(['updateID', 'updateInfo']);
let curdRef = ref();
const textRef = ref({});
const nowTd = ref();
const ermPopoverBox = ref();
const isInfo = ref(false);
const nowNumber = ref();
const nowIndex = ref();
const nowNumProp = ref();
const currentRef = ref();
const projectDesc = ref('');
const visible = ref(false);
const checkList = ref(['1', '2', '3']);
const dialogModel = ref(false);
const radio = ref('1');

let height = ref(1000);
let isShowBar = ref(false);
const setForm = e => {
  form.value = e;
};
const disabledDate = time => {
  return String(time.getFullYear()) !== year.value;
};
const isFullscreen = ref(screenfull.isFullscreen);
const useTools = ref(true);

const nowNumberChange = async () => {
  loadingStatus.value.status = true;
  iptChange(nowIndex.value, nowNumProp.value, nowNumber.value);
  let data = await getData();
  await budgetPlanNewHttp(data)
    .then(() => {
      loadingStatus.value.status = false;
      ermPopoverBox.value.style.display = 'none';
    })
    .catch(async () => {
      loadingStatus.value.status = false;
      ElNotification({
        title: '提示',
        message: '操作失败',
        type: 'error'
      });
      await getBudgetPlanInfo();
    });
};
const tColumns = computed(() => {
  return totalColumns();
});

const onDblclick = async (row, prop, index) => {
  if (!props.auth) return;
  if (row.lvl) return;
  if (row.columnType === 'total' || row.columnType === 'allTotal') return;
  if (prop === 'budgetProjectName') {
    isInfo.value = true;
    let resData = await getProjectInfo(row.budgetProjectId);
    projectDesc.value = resData?.projectDesc;
  } else {
    isInfo.value = false;
  }
  if (prop === 'projectPriority') {
    if (row.columnType == 'other' || row.dicBudgetTagCode) return;
    precision.value = 0;
  }
  await nextTick();
  nowTd.value = textRef.value[`${prop}_${index}`];
  let parent = textRef.value[`${prop}_${index}`].parentNode.parentNode;
  parent.style.backgroundColor = 'var(--el-color-primary-light-5)';
  textRef.value[`${prop}_${index}`].style.userSelect = 'none';
  currentRef.value = textRef.value[`${prop}_${index}`];
  nowNumber.value = row[prop];
  nowNumProp.value = prop;
  nowIndex.value = index;

  ermPopoverBox.value.style.display = 'flex';
  createPopper(nowTd.value, ermPopoverBox.value, {
    placement: 'bottom',
    reset: true,
    modifiers: [
      {
        name: 'offset',
        options: {
          offset: [0, 10]
        }
      }
    ]
  });
};
onClickOutside(
  currentRef,
  () => {
    currentRef.value.parentNode.parentNode.style.backgroundColor = '';
  },
  {
    ignore: ['#erm_popover_box']
  }
);

const toggleFullscreen = () => {
  let ele = document.getElementById('fullscreen');
  useTools.value = false;
  screenfull.request(ele);
};

onMounted(() => {
  screenfull.on('change', () => {
    if (screenfull.isFullscreen) isFullscreen.value = true;
    else isFullscreen.value = false;
  });
});

watch(
  () => isFullscreen.value,
  async newVal => {
    if (!newVal) {
      useTools.value = true;
      isShowBar.value = false;
    } else {
      isShowBar.value = true;
      height.value = windowSize.height.value;
      await nextTick();
      curdRef.value.reload({ resetPage: false });
    }
  }
);

// 表单验证规则
const rules = computed(() => {
  return useRules({ isEdit: props.isEdit });
});
const loadingStatus = inject('loadingStatus'); // 加载动画
const loading = ref(false);
const chooseModal = ref();
const contactRef = {};
const funi_curd = ref();
const year = ref();
const su = ref();
const keyword = ref();
const month = ref($utils.Date().local().format('YYYY-MM-DD'));
const tableMap = ref(new Map());
const selectData = ref();
const tableState = reactive({
  tableData: [],
  postTableData: [],
  selectAddData: []
});
const form = ref();
const schema = computed(() => {
  const { isEdit } = props;
  return useSchema({
    isEdit,
    yearChange
  });
});

const yearChange = async e => {
  loadingStatus.value.status = true;
  year.value = e;
  let budgetProjectInfoList = [];
  let data = tableState.postTableData.filter(item => {
    return item.isAdd !== true;
  });
  data.forEach(item => {
    budgetProjectInfoList.push({
      budgetProjectId: item.budgetProjectId,
      budgetProjectName: item.budgetProjectName,
      deptId: item.deptId,
      deptName: item.deptName,
      planOrder: item.planOrder || 0,
      actualOrder: item.actualOrder || 0,
      planCollection: item.planCollection || 0,
      actualCollection: item.actualCollection || 0,
      planReceipt: item.planReceipt || 0,
      adjustedQuota: item.adjustedQuota || 0,
      adjustedReceipt: item.adjustedReceipt || 0,
      actualReceipt: item.actualReceipt || 0,
      actualCost: item.actualCost || 0,
      adjustOrder: item.adjustOrder || 0,
      adjustAfterOrder: item.adjustAfterOrder || 0,
      planPurchaseEstimate: item.planPurchaseEstimate || 0
    });
  });
  await changePlanYearHttp({ budgetProjectInfoList, year: e })
    .then(res => {
      tableState.tableData = [];
      tableMap.value = new Map();
      handlerFilterData(res.list, false);
    })
    .finally(() => {
      loadingStatus.value.status = false;
    });
};

/**
 * 导出
 */
const exportFunc = () => {
  queryBudgetProjectInfoListExportHttp({
    planId: props.id,
    year: year.value,
    keyword: keyword.value,
    statisticMonth: month.value,
    exportDataType: checkList.value
  });
};

const initDocEvent = () => {
  document.addEventListener('click', e => {
    if (
      nowTd.value &&
      ermPopoverBox.value &&
      !e.composedPath().includes(nowTd.value) &&
      !e.composedPath().includes(ermPopoverBox.value)
    ) {
      nowTd.value = void 0;
      ermPopoverBox.value.style.display = 'none';
    }
  });
};

const iptChange = async (index, key, e) => {
  //计划确收
  if (key === 'planReceipt') {
    tableState.tableData[index]['adjustedReceipt'] = Number(e) + Number(tableState.tableData[index]['adjustedQuota']);
  }
  //确收调增/减(万)
  if (key === 'adjustedQuota') {
    tableState.tableData[index]['adjustedReceipt'] = Number(e) + Number(tableState.tableData[index]['planReceipt']);
  }

  if (key === 'adjustOrder') {
    tableState.tableData[index]['adjustAfterOrder'] = Number(e) + Number(tableState.tableData[index]['planOrder']);
  }
  if (key === 'planOrder') {
    tableState.tableData[index]['adjustAfterOrder'] = Number(e) + Number(tableState.tableData[index]['adjustOrder']);
  }

  if (!tableMap.value.get(`${tableState.tableData[index]['deputyDeptName']}${tableState.tableData[index]['deptId']}`)) {
    handlerFilterData(e, false);
  } else {
    tableState.tableData[index][key] = e;
    let groupData = tableMap.value
      .get(`${tableState.tableData[index]['deputyDeptName']}${tableState.tableData[index]['deptId']}`)
      .filter(item => item.budgetProjectName !== '一级部门小计');

    let total = groupData.reduce(
      (pre, cur) => {
        if (key !== 'projectPriority') {
          pre[key] += Number(cur[key]);
          return pre;
        }
      },
      {
        planOrder: 0,
        actualOrder: 0,
        planCollection: 0,
        actualCollection: 0,
        planReceipt: 0,
        adjustedQuota: 0,
        adjustedReceipt: 0,
        actualReceipt: 0,
        unplanPurchaseCost: 0,
        expectPerformance: 0,
        actualCost: 0,
        adjustOrder: 0,
        adjustAfterOrder: 0,
        planPurchaseEstimate: 0
      }
    );

    tableMap.value.get(`${tableState.tableData[index]['deputyDeptName']}${tableState.tableData[index]['deptId']}`)[
      groupData.length
    ][key] = total?.[key];

    let deptIdx = tableState.tableData.findIndex(item => {
      if (item.deputyDeptName === tableState.tableData[index]['deputyDeptName']) return true;
    });

    let currentDeptAry = tableState.tableData.filter(
      item => item.deputyDeptName === tableState.tableData[index]['deputyDeptName'] && item.columnType == 'total'
    );

    let deptTotal = currentDeptAry.reduce(
      (pre, cur) => {
        if (key !== 'projectPriority') {
          pre[key] += Number(cur[key]);
          return pre;
        }
      },
      {
        planOrder: 0,
        actualOrder: 0,
        planCollection: 0,
        actualCollection: 0,
        planReceipt: 0,
        adjustedQuota: 0,
        adjustedReceipt: 0,
        actualReceipt: 0,
        unplanPurchaseCost: 0,
        expectPerformance: 0,
        actualCost: 0,
        adjustOrder: 0,
        adjustAfterOrder: 0,
        planPurchaseEstimate: 0
      }
    );
    tableState.tableData[deptIdx][key] = deptTotal[key];
  }
};
//获取选中数据
const updateData = data => {
  selectData.value = [];
  let selectData1 = data.map(item => {
    return {
      ...item,
      isAdd: true
    };
  });
  handlerFilterData(selectData1, true);
};

//处理添加数据
const handlerFilterData = (data, needAddOther, isFilter = false) => {
  // console.log(data, needAddOther, 'needAddOther');
  let temp = [];
  if (isFilter) {
    tableState.tableData = data;
  } else {
    // let normalP = [];
    // let businessP = [];
    // let level1 = [];
    // let level2 = [];
    // let level3 = [];
    // let other = [];

    data.forEach(item => {
      if (item.dicBudgetTagCode) {
        let ary = item.dicBudgetTagCode.split(',');
        if (ary.includes('10000')) {
          item = { ...item, columnType: 'business' };
        }
        // if (ary.includes('9999')) {
        //   level1.push(item);
        // } else if (ary.includes('9998')) {
        //   level2.push(item);
        // } else if (ary.includes('9997')) {
        //   level3.push(item);
        // } else {
        //   businessP.push({ ...item, columnType: 'business' });
        // }
      }
      //  else {
      //   if (item.budgetProjectName == '其他') other.push(item);
      //   else normalP.push(item);
      // }
    });
    // data = [...normalP, ...level1, ...level2, ...level3, ...businessP, ...other];

    data.forEach?.((item, index) => {
      //处理合计为NAN情况  实际签单 实际确收 实际回款
      item['actualOrder'] = item.actualOrder ?? 0;
      item['actualCollection'] = item.actualCollection ?? 0;
      item['actualReceipt'] = item.actualReceipt ?? 0;
      item['expectPerformance'] = item.expectPerformance ?? 0;
      item['unplanPurchaseCost'] = item.unplanPurchaseCost ?? 0;
      item['actualCost'] = item.actualCost ?? 0;
      item['adjustOrder'] = item.adjustOrder ?? 0;
      item['adjustAfterOrder'] = item.adjustAfterOrder ?? 0;
      item['planPurchaseEstimate'] = item.planPurchaseEstimate ?? 0;

      if (needAddOther) {
        if (!tableMap.value.get(`${item.deputyDeptName}${item.deptId}`)) {
          tableMap.value.set(`${item.deputyDeptName}${item.deptId}`, [
            { ...item, columnType: item.dicBudgetTagCode?.split(',').includes('10000') ? 'business' : '' },
            {
              columnType: 'other',
              deptId: item.deptId,
              deputyDeptName: item.deputyDeptName,
              deptName: item.deptName,
              budgetProjectName: '其他',
              budgetProjectId: $utils.guid(),
              planOrder: 0,
              actualOrder: 0,
              planCollection: 0,
              actualCollection: 0,
              planReceipt: 0,
              adjustedQuota: 0,
              adjustedReceipt: 0,
              actualReceipt: 0,
              expectPerformance: 0,
              unplanPurchaseCost: 0,
              actualCost: 0,
              adjustOrder: 0,
              adjustAfterOrder: 0,
              planPurchaseEstimate: 0
            },
            {
              columnType: 'total',
              deptId: item.deptId,
              deputyDeptName: item.deputyDeptName,
              deptName: item.deptName,
              budgetProjectName: '一级部门小计',
              budgetProjectId: $utils.guid(),
              planOrder: 0,
              actualOrder: 0,
              planCollection: 0,
              actualCollection: 0,
              planReceipt: 0,
              adjustedQuota: 0,
              adjustedReceipt: 0,
              actualReceipt: 0,
              expectPerformance: 0,
              unplanPurchaseCost: 0,
              actualCost: 0,
              adjustOrder: 0,
              adjustAfterOrder: 0,
              planPurchaseEstimate: 0
            }
          ]);
        } else {
          tableMap.value.get(`${item.deputyDeptName}${item.deptId}`).unshift({ ...item, columnType: '' });
          //对map重新赋值
          let sortAry = Array.from(tableMap.value).sort((a, b) => a[0].localeCompare(b[0]));
          // let normalP = [];
          // let businessP = [];
          // let other = [];
          // let level1 = [];
          // let level2 = [];
          // let level3 = [];
          // sortAry[0][1].forEach(item => {
          //   if (item.dicBudgetTagCode) {
          //     let ary = item.dicBudgetTagCode.split(',');

          //     if (ary.includes('10000')) {
          //       item = { ...item, columnType: 'business' };
          //     }
          //     if (ary.includes('9999')) {
          //       level1.push(item);
          //     } else if (ary.includes('9998')) {
          //       level2.push(item);
          //     } else if (ary.includes('9997')) {
          //       level3.push(item);
          //     } else {
          //       businessP.push({ ...item, columnType: 'business' });
          //     }
          //   } else {
          //     if (item.columnType == 'other' || item.columnType == 'total') other.push(item);
          //     else normalP.push(item);
          //   }
          // });
          // sortAry[0][1] = [...normalP, ...level1, ...level2, ...level3, ...businessP, ...other];
          tableMap.value = new Map();
          sortAry.forEach(item => {
            tableMap.value.set(item[0], item[1]);
          });
        }
      } else {
        if (!tableMap.value.get(`${item.deputyDeptName}${item.deptId}`)) {
          tableMap.value.set(`${item.deputyDeptName}${item.deptId}`, [
            { ...item, columnType: item.dicBudgetTagCode?.split(',').includes('10000') ? 'business' : '' }
          ]);
        } else {
          if (item.budgetProjectName === '其他') {
            tableMap.value.get(`${item.deputyDeptName}${item.deptId}`).push(
              {
                ...item,
                deptName: tableMap.value.get(`${item.deputyDeptName}${item.deptId}`)[0].deptName,
                deputyDeptName: tableMap.value.get(`${item.deputyDeptName}${item.deptId}`)[0].deputyDeptName,
                projectPriority: '',
                columnType: 'other'
              },
              {
                columnType: 'total',
                deptId: item.deptId,
                deptName: tableMap.value.get(`${item.deputyDeptName}${item.deptId}`)[0].deptName,
                deputyDeptName: tableMap.value.get(`${item.deputyDeptName}${item.deptId}`)[0].deputyDeptName,
                budgetProjectName: '一级部门小计',
                budgetProjectId: $utils.guid(),
                planOrder: 0,
                actualOrder: 0,
                planCollection: 0,
                actualCollection: 0,
                planReceipt: 0,
                adjustedQuota: 0,
                adjustedReceipt: 0,
                actualReceipt: 0,
                expectPerformance: 0,
                unplanPurchaseCost: 0,
                actualCost: 0,
                adjustOrder: 0,
                adjustAfterOrder: 0,
                planPurchaseEstimate: 0
              }
            );
          } else {
            tableMap.value
              .get(`${item.deputyDeptName}${item.deptId}`)
              .push({ ...item, columnType: item.dicBudgetTagCode?.split(',').includes('10000') ? 'business' : '' });
          }
        }
      }
    });
    let deptAry = [];
    tableMap.value.forEach(val => {
      temp.push(...val);
    });

    temp.forEach(item => {
      if (!deptAry.includes(item.deputyDeptName)) deptAry.push(item.deputyDeptName);
    });

    for (let j = 0; j < deptAry.length; j++) {
      let lIdx = temp.findIndex(item => {
        if (item.deputyDeptName == deptAry[j]) return true;
      });
      temp.splice(lIdx, 0, {
        columnType: 'deptTotal',
        deptId: '',
        deputyDeptName: temp[lIdx].deputyDeptName,
        deptName: '',
        budgetProjectName: '副总级部门小计',
        budgetProjectId: $utils.guid(),
        planOrder: 0,
        actualOrder: 0,
        planCollection: 0,
        actualCollection: 0,
        planReceipt: 0,
        adjustedQuota: 0,
        adjustedReceipt: 0,
        actualReceipt: 0,
        expectPerformance: 0,
        unplanPurchaseCost: 0,
        actualCost: 0,
        adjustOrder: 0,
        adjustAfterOrder: 0,
        planPurchaseEstimate: 0
      });
    }
    temp.unshift({
      columnType: 'allTotal',
      deptId: $utils.guid(),
      deputyDeptName: '公司合计',
      deptName: ' ',
      budgetProjectName: ' ',
      budgetProjectId: $utils.guid(),
      planOrder: 0,
      actualOrder: 0,
      planCollection: 0,
      actualCollection: 0,
      planReceipt: 0,
      adjustedQuota: 0,
      adjustedReceipt: 0,
      actualReceipt: 0,
      unplanPurchaseCost: 0,
      expectPerformance: 0,
      actualCost: 0,
      adjustOrder: 0,
      adjustAfterOrder: 0,
      planPurchaseEstimate: 0
    });

    tableState.postTableData = temp.filter(
      item => item.columnType !== 'total' && item.columnType !== 'allTotal' && item.columnType !== 'deptTotal'
    );
    tableState.tableData = temp;
  }
};

watch(
  () => tableState.postTableData,
  async t => {
    if (t) {
      //计算表格total
      tableMap.value.forEach((v, k) => {
        let total = v
          .filter(item => item.columnType !== 'total' && item.columnType !== 'deptTotal')
          .reduce(
            (pre, cur) => {
              pre['planOrder'] += Number(cur['planOrder']);
              pre['actualOrder'] += Number(cur['actualOrder']);
              pre['planCollection'] += Number(cur['planCollection']);
              pre['actualCollection'] += Number(cur['actualCollection']);
              pre['planReceipt'] += Number(cur['planReceipt']);
              pre['adjustedQuota'] += Number(cur['adjustedQuota']);
              pre['adjustedReceipt'] += Number(cur['adjustedReceipt']);
              pre['actualReceipt'] += Number(cur['actualReceipt']);
              pre['actualCost'] += Number(cur['actualCost']);
              pre['unplanPurchaseCost'] += Number(cur['unplanPurchaseCost']);
              pre['expectPerformance'] += Number(cur['expectPerformance']);
              pre['adjustOrder'] += Number(cur['adjustOrder']);
              pre['adjustAfterOrder'] += Number(cur['adjustAfterOrder']);
              pre['planPurchaseEstimate'] += Number(cur['planPurchaseEstimate']);

              return pre;
            },
            {
              planOrder: 0,
              actualOrder: 0,
              planCollection: 0,
              actualCollection: 0,
              planReceipt: 0,
              adjustedQuota: 0,
              adjustedReceipt: 0,
              actualReceipt: 0,
              unplanPurchaseCost: 0,
              expectPerformance: 0,
              actualCost: 0,
              adjustOrder: 0,
              adjustAfterOrder: 0,
              planPurchaseEstimate: 0
            }
          );
        if (v && v.length > 0) {
          v[v.length - 1] = Object.assign(v[v.length - 1], total);
        }
      });
      //数据变化计算表单的total
      let total = t.reduce(
        (pre, cur) => {
          pre['planOrder'] += Number(cur['planOrder']);
          pre['actualOrder'] += Number(cur['actualOrder']);
          pre['planCollection'] += Number(cur['planCollection']);
          pre['actualCollection'] += Number(cur['actualCollection']);
          pre['planReceipt'] += Number(cur['planReceipt']);
          pre['adjustedQuota'] += Number(cur['adjustedQuota']);
          pre['adjustedReceipt'] += Number(cur['adjustedReceipt']);
          pre['actualReceipt'] += Number(cur['actualReceipt']);
          pre['expectPerformance'] += Number(cur['expectPerformance']);
          pre['unplanPurchaseCost'] += Number(cur['unplanPurchaseCost']);
          pre['actualCost'] += Number(cur['actualCost']);
          pre['adjustOrder'] += Number(cur['adjustOrder']);
          pre['adjustAfterOrder'] += Number(cur['adjustAfterOrder']);
          pre['planPurchaseEstimate'] += Number(cur['planPurchaseEstimate']);
          return pre;
        },
        {
          planOrder: 0,
          actualOrder: 0,
          planCollection: 0,
          actualCollection: 0,
          planReceipt: 0,
          adjustedQuota: 0,
          adjustedReceipt: 0,
          actualReceipt: 0,
          unplanPurchaseCost: 0,
          expectPerformance: 0,
          actualCost: 0,
          adjustOrder: 0,
          adjustAfterOrder: 0,
          planPurchaseEstimate: 0
        }
      );

      //副总级部门小计
      let deptAry = [];
      tableState.tableData.forEach(item => {
        if (!deptAry.includes(item.deputyDeptName)) deptAry.push(item.deputyDeptName);
      });
      for (let j = 0; j < deptAry.length; j++) {
        let ary = tableState.tableData.filter(item => item.deputyDeptName == deptAry[j] && item.columnType === 'total');
        let deptTotal = ary.reduce(
          (pre, cur) => {
            pre['planOrder'] += Number(cur['planOrder']);
            pre['actualOrder'] += Number(cur['actualOrder']);
            pre['planCollection'] += Number(cur['planCollection']);
            pre['actualCollection'] += Number(cur['actualCollection']);
            pre['planReceipt'] += Number(cur['planReceipt']);
            pre['adjustedQuota'] += Number(cur['adjustedQuota']);
            pre['adjustedReceipt'] += Number(cur['adjustedReceipt']);
            pre['actualReceipt'] += Number(cur['actualReceipt']);
            pre['actualCost'] += Number(cur['actualCost']);
            pre['unplanPurchaseCost'] += Number(cur['unplanPurchaseCost']);
            pre['expectPerformance'] += Number(cur['expectPerformance']);
            pre['adjustOrder'] += Number(cur['adjustOrder']);
            pre['adjustAfterOrder'] += Number(cur['adjustAfterOrder']);
            pre['planPurchaseEstimate'] += Number(cur['planPurchaseEstimate']);
            return pre;
          },
          {
            planOrder: 0,
            actualOrder: 0,
            planCollection: 0,
            actualCollection: 0,
            planReceipt: 0,
            adjustedQuota: 0,
            adjustedReceipt: 0,
            actualReceipt: 0,
            unplanPurchaseCost: 0,
            expectPerformance: 0,
            actualCost: 0,
            adjustOrder: 0,
            adjustAfterOrder: 0,
            planPurchaseEstimate: 0
          }
        );
        let lIdx = tableState.tableData.findIndex(item => {
          if (item.deputyDeptName == deptAry[j]) return true;
        });

        tableState.tableData[lIdx] = Object.assign(tableState.tableData[lIdx], deptTotal);
      }

      //公司合计
      tableState.tableData?.[0] && Object.assign(tableState.tableData?.[0], total);
      await nextTick();
      tData.value[0] = total;
      // form.value.setValues({
      //   ...total
      // });
    }
  },
  { immediate: true, deep: true }
);

//表格合并
const mergeCells = (...args) => {
  return mergeRowMethod(
    {
      fields: ['deptName', 'deputyDeptName'],
      filedKeys: ['deptId', 'deputyDeptName']
    },
    tableState.tableData,
    ...args
  );
};

//设置合计行样式
const setRowClassName = ({ row }) => {
  let rowNameChild = '';
  if (row.dicBudgetTagCode) {
    let ary = row.dicBudgetTagCode.split(',');
    if (ary.includes('9998') || ary.includes('9997') || ary.includes('9999')) {
      rowNameChild = 'warning_project ';
    } else {
      rowNameChild = 'business_project ';
    }
  }
  if (row.columnType === 'manageProject') {
    rowNameChild = 'manageProject ' + rowNameChild;
  } else if (row.columnType === 'contract') {
    rowNameChild = 'contract ' + rowNameChild;
  }
  if (selectData.value?.filter(item => item.budgetProjectId === row.budgetProjectId).length > 0) {
    return 'selected ' + rowNameChild;
  } else {
    if (row.budgetProjectName == '副总级部门小计') {
      return 'hight ' + rowNameChild;
    }
    if (row.columnType == 'allTotal') {
      return 'all_total ' + rowNameChild;
    }
    return rowNameChild;
  }
};

//处理合并后移入问题
const cellClassName = c => {
  if (c.row.columnType === 'allTotal') {
    return;
  }
  if (c.row.columnType === 'deptTotal') {
    // return 'hight';
  }
  if (c.column.label === '副总级部门' || c.column.label === '部门') {
    return 'normal';
  }
};
const columns = computed(() => {
  return pColumns({
    isEdit: props.isEdit,
    iptChange,
    contactRef,
    tableData: tableState.tableData,
    onDblclick,
    textRef,
    isFullscreen,
    operation,
    showDialog
  });
});

//添加预算项目
const addFunc = () => {
  chooseModal.value.show(tableState.tableData, year.value);
};

const showDialog = () => {
  dialogModel.value = true;
};

//删除预算项目
const delFunc = () => {
  let sData = $utils.clone(selectData.value, true);
  if (sData <= 0) return;
  selectData.value.forEach((item, index) => {
    let idx = tableState.tableData.findIndex(i => {
      if (
        i.budgetProjectId == item.budgetProjectId &&
        i.deptId == item.deptId &&
        i.deputyDeptName == item.deputyDeptName
      ) {
        return true;
      }
    });

    if (idx >= 0) {
      let mIdx = tableMap.value
        .get(`${tableState.tableData[idx]?.['deputyDeptName']}${tableState.tableData[idx]?.['deptId']}`)
        ?.findIndex(i => {
          if (
            i.budgetProjectId == item.budgetProjectId &&
            i.deptId == item.deptId &&
            i.deputyDeptName == item.deputyDeptName
          ) {
            return true;
          }
        });
      tableState.tableData.splice(idx, 1);
      tableMap.value.get(`${item['deputyDeptName']}${item['deptId']}`)?.splice(mIdx, 1);
      if (tableMap.value.get(`${item['deputyDeptName']}${item['deptId']}`).length <= 2) {
        let dIdx = tableState.tableData.findIndex(i => {
          if (i.deptId == item.deptId && i.deputyDeptName == item.deputyDeptName) return true;
        });
        tableState.tableData.splice(dIdx, 2);
        tableMap.value.delete(`${item['deputyDeptName']}${item['deptId']}`);
        if (tableState.tableData.filter(i => i.deputyDeptName == item.deputyDeptName).length == 1) {
          tableState.tableData = tableState.tableData.filter(i => i.deputyDeptName !== item.deputyDeptName);
        }
      }
    }
  });
  tableState.postTableData = tableState.tableData.filter(
    item => item.columnType !== 'total' && item.columnType !== 'allTotal' && item.columnType !== 'deptTotal'
  );

  curdRef.value.ref.clearSelection();
};

//选中
const getSelectData = e => {
  selectData.value = e;
};

// 获取数据
const getBudgetPlanInfo = async () => {
  tableState.tableData = [];
  tableMap.value = new Map();
  loadingStatus.value.status = true;
  const { budgetPlanVo, budgetProjectDetailVoList } = await getBudgetPlanInfoHttp({
    budgetPlanId: props.id,
    year: props.planYear,
    statisticMonth: month.value ?? $utils.Date().local().format('YYYY-MM-DD'),
    keyword: keyword.value
  }).finally(() => {
    loadingStatus.value.status = false;
  });
  // 配置表单数据
  form.value.setValues({
    ...budgetPlanVo
  });
  tData.value[0] = [budgetPlanVo];
  handlerFilterData(budgetProjectDetailVoList, false);
  year.value = budgetPlanVo.planYear;
};

const verificationTable = () => {
  if (tableState.postTableData.length == 0) {
    ElNotification({
      title: '提示',
      message: '请添加关联预算项目',
      type: 'warning'
    });
    return false;
  } else {
    return verificationTableForm(contactRef);
  }
};

const verificationTableForm = obj => {
  let v_l = [];
  for (let key in obj) {
    let v = obj[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};

const resize = async () => {
  height.value = windowSize.height.value;
  await nextTick();
  curdRef.value.reload({ resetPage: false });
};

const debouncedResize = useDebounceFn(resize, 300, { maxWait: 800 });

// 获取详情
onMounted(async () => {
  initDocEvent();
  if (props.isEdit && !props.id) {
    let yearStr = new Date().getFullYear();
    year.value = yearStr;
    form.value.setValues({ planYear: yearStr });
  }
  if (props.id) {
    await getBudgetPlanInfo();
  }
  window.addEventListener('resize', debouncedResize);
});

/**
 * @description 数据保存函数
 * **/
const saveDate = async type => {
  let data = await getData(type);
  if (!data) return;
  loadingStatus.value.status = true;
  let resolveData = await budgetPlanNewHttp(data).finally(() => {
    loadingStatus.value.status = true;
  });
  emit('updateID', resolveData.id);

  await nextTick();
  if (!props.id) return Promise.reject();
  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  } else {
    su.value.show();
  }
  return Promise.resolve({});
};

const operation = async (row, index) => {
  const { budgetProjectId, deptId, planId, openStatus, deptName, deputyDeptName, projectSn } = row;
  if (!openStatus) {
    loading.value = true;
    let { list } = await queryProjectOrContractListHttp({
      mark: !row.columnType || row.columnType == 'other' || row.columnType == 'business' ? 'XM' : 'HT',
      id: row.columnType === 'manageProject' ? projectSn : budgetProjectId,
      statisticMonth: month.value.vlaue ?? $utils.Date().local().format('YYYY-MM-DD'),
      deptName,
      deptId,
      isOtherItem: row.columnType === 'other' ? true : false,
      planId,
      deputyDeptName
    }).finally(() => {
      loading.value = false;
    });
    list
      .map(item => {
        let keyName = row.columnType === 'manageProject' ? 'contractSn' : 'budgetProjectName';
        return {
          ...$utils.clone(item, true),
          lvl: row.columnType === 'manageProject' ? '3' : '2',
          budgetProjectId,
          deputyDeptName,
          deptId,
          planId,
          urgeType: '1',
          textarea: `你负责的“${item[keyName]}”下有未关联的收款单，请及时关联`,
          columnType: row.columnType === 'manageProject' ? 'contract' : 'manageProject'
        };
      })
      .forEach((item, _index) => {
        tableState.tableData.splice(index + 1 + _index, 0, item);
      });
    row.openStatus = true;
  } else {
    tableState.tableData = tableState.tableData.filter(item => {
      let { budgetProjectId: bpi, deptId: di, planId: pi, lvl } = item;
      let l = row.columnType === 'manageProject' ? ['3'] : ['2', '3'];
      let flag = true;
      if (budgetProjectId === bpi && deptId === di && planId === pi && l.includes(lvl)) {
        flag = false;
      }
      return flag;
    });
    row.openStatus = false;
  }
};

/**
 * @description 表单数据处理
 * **/
const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await form.value.validate();
  if (type == 'ts' || (isValid && verificationTable())) {
    let obj = {
      id: props.id || '',
      ...form.value.getValues(),
      ...tData.value[0],
      budgetProjectInfoRequests: tableState.postTableData
    };
    return obj;
  } else {
    return false;
  }
};

/**
 * @description 下一步 提交数据
 * **/
const submit = () => {
  return saveDate();
};

defineExpose({
  submit
});
</script>

<style lang="scss" scoped>
:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

.base_info {
  // padding: 20px;
  box-sizing: border-box;
}

.tableMerge :deep(.funi-curd__table .el-table--border .el-table__cell) {
  border-right: var(--el-table-border) !important;
}

.tableMerge :deep(.el-table__body > td.el-table__cell.normal) {
  background-color: #e5f2ff !important;
}

.tableMerge :deep(.el-table .el-table__body tr.selected.hover-row) {
  background-color: #fff !important;
}

.tableMerge :deep(.el-table .el-table__body > td.el-table__cell.normal) {
  background-color: #fff !important;
}

:deep(.el-table tbody tr.selected:hover > td) {
  background-color: var(--el-color-primary-light-3) !important;
}

.tableMerge :deep(.el-table .el-table__body tr.el-table__row.hight) {
  background-color: var(--el-color-primary-light-9) !important;
}
.tableMerge :deep(.el-table .el-table__body tr.el-table__row:not(.all_total) .noHight) {
  background-color: #fff !important;
}
.tableMerge :deep(.el-table .el-table__body tr.el-table__row.all_total) {
  background-color: #f8f8f9 !important;
}
.tableMerge :deep(.el-table .el-table__body tr.el-table__row.all_total .el-table-column--selection) {
  background-color: #f8f8f9 !important;
}
.tableMerge :deep(.el-table .el-table__body tr.el-table__row.business_project) {
  background-color: rgb(111, 201, 111) !important;
}
.tableMerge :deep(.el-table .el-table__body tr.el-table__row.warning_project) {
  background-color: rgb(233, 157, 157) !important;
}

.tableMerge :deep(.el-table .el-table__body tr.el-table__row.hight.selected) {
  background-color: var(--el-color-primary-light-3) !important;
}

.tableMerge :deep(.el-table .el-table__body tr.el-table__row.selected) {
  background-color: var(--el-color-primary-light-5) !important;
}

.tableMerge :deep(.el-table .el-table__body td.el-table__cell.normal) {
  background-color: #fff !important;
}

.isShowBar :deep(.el-table__body-wrapper .el-scrollbar__bar.is-horizontal) {
  display: inline-block !important;
}
.isShowBar :deep(.el-table__body-wrapper .el-scrollbar__bar.is-vertical) {
  display: inline-block !important;
}
:deep(.fixed-nth-last-child) {
  margin-bottom: 40px;
}

:deep(.el-popper.dept_popover) {
  z-index: 2107;
  width: 150px;
  position: absolute;
  inset: auto auto 530px 349px;
}

.musterW {
  overflow: hidden;
  width: 100%;
  height: 26px;
  padding-left: 5px;
}

:deep(.el-table .cell) {
  white-space: pre-wrap !important;
  .upOrDown {
    display: flex;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--el-color-primary);
    cursor: pointer;
    transition: all 0.3s;
    &.manageProject {
      color: var(--el-color-danger);
    }
    &:hover {
      transform: scale(1.3);
      transition: all 0.3s;
    }
  }
}
:deep(.el-table .manageProject) {
  color: var(--el-color-danger);
}
:deep(.el-table .contract) {
  color: var(--el-color-success);
}

.musterW > span {
  color: red;
  display: inline-block;
  /* margin: 3px 3px 0 0; */
  position: relative;
  top: 4px;
  right: 3px;
}

#fullscreen {
  background-color: #fff;
}

.month {
  font-size: 14px;
  padding-right: 15px;
  color: var(--el-text-color-regular);

  span {
    margin-right: 8px;
  }
}

:deep(.el-month-table td.today .cell) {
  color: var(--el-datepicker-text-color) !important;
}

:deep(.urge) {
  font-size: 16px;
  cursor: pointer;
  color: var(--el-color-primary) !important;
}

.warning_project {
  background-color: red !important;
}
.business_project {
  background-color: green !important;
}

#erm_popover_box {
  width: max-content;
  height: max-content;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  z-index: 2000;
  display: none;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.22);
  justify-content: space-between;
  align-items: center;
  gap: 10px;

  .popover_content {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  .popover_content_info {
    width: 400px;
    font-size: 14px;
    color: #48515e;
    padding: 5px;
    height: 150px;
    overflow-y: scroll;
    .title {
      font-weight: bold;
      color: #48515e;
    }
    .text {
      white-space: pre-wrap;
    }
  }
}
</style>
