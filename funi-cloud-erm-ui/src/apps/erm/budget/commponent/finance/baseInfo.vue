<template>
  <div>
    <GroupTitle title="基本信息"></GroupTitle>
    <funi-form :schema="schema" @get-form="setForm" :rules="rules" :col="2" :border="false"></funi-form>
    <div class="upload-file">
      <el-upload
        ref="upload"
        v-model:file-list="fileList"
        :action="uploadUrl"
        :limit="1"
        :on-exceed="handleExceed"
        :http-request="request"
        accept=".xlsx"
      >
        <el-button type="primary">上传文件</el-button>
        <template #tip>
          <div class="el-upload__tip" style="margin-left: 98px; margin-top: -22px">
            <span style="margin-right: 100px">支持格式：.xlsx</span>

            <el-button ref="buttonRef" v-click-outside="onClickOutside" type="primary" link>下载模板</el-button>
          </div>
        </template>
      </el-upload>

      <el-popover
        ref="popoverRef"
        :virtual-ref="buttonRef"
        trigger="click"
        title="请选择下载的表格模板"
        virtual-triggering
        width="400"
      >
        <div>
          <el-checkbox-group v-model="checkList">
            <el-checkbox v-for="item in typeList" :label="item" :value="item" />
          </el-checkbox-group>
          <div style="text-align: right; margin: 0">
            <el-button size="small" type="primary" @click="downloadTemplate"> 下载 </el-button>
          </div>
        </div>
      </el-popover>
    </div>
    <GroupTitle title="主要指标预算完成情况"></GroupTitle>
    <funi-curd :rowSelection="false" :columns="budgetColumns" :data="baseData.budgetList" :pagination="false">
    </funi-curd>

    <GroupTitle title="主要指标同比情况分析"></GroupTitle>
    <funi-curd :rowSelection="false" :columns="compareColumns" :data="baseData.compareList" :pagination="false">
    </funi-curd>

    <GroupTitle title="成本投入同比情况分析"></GroupTitle>
    <funi-curd
      :rowSelection="false"
      :columns="costInputListColumns"
      :row-class-name="tableRowClassName"
      :data="baseData.costInputList"
      :pagination="false"
    >
    </funi-curd>

    <GroupTitle title="部门签单完成情况分析"></GroupTitle>
    <funi-curd
      :rowSelection="false"
      :columns="signingColumns"
      :row-class-name="tableRowClassName"
      :data="baseData.signingList"
      :pagination="false"
    >
    </funi-curd>
    <div style="padding: 0 8px 10px">
      <el-input
        type="textarea"
        :autosize="{
          minRows: 4
        }"
        v-model="baseData.signingDesc"
        :disabled="!props.isEdit"
        placeholder="请输入..."
      ></el-input>
    </div>
    <GroupTitle title="部门回款完成情况分析"></GroupTitle>
    <funi-curd
      :rowSelection="false"
      :columns="receiptColumns"
      :row-class-name="tableRowClassName"
      :data="baseData.receiptList"
      :pagination="false"
    >
    </funi-curd>
    <div style="padding: 0 8px 10px">
      <el-input
        type="textarea"
        :autosize="{
          minRows: 4
        }"
        v-model="baseData.receiptDesc"
        :disabled="!props.isEdit"
        placeholder="请输入..."
      ></el-input>
    </div>
    <GroupTitle title="部门收入完成情况分析"></GroupTitle>
    <funi-curd
      :rowSelection="false"
      :columns="incomeColumns"
      :row-class-name="tableRowClassName"
      :data="baseData.incomeList"
      :pagination="false"
    >
    </funi-curd>
    <div style="padding: 0 8px 10px">
      <el-input
        type="textarea"
        :autosize="{
          minRows: 4
        }"
        v-model="baseData.incomeDesc"
        :disabled="!props.isEdit"
        placeholder="请输入..."
      ></el-input>
    </div>
    <GroupTitle title="各公司利润贡献情况"></GroupTitle>
    <funi-curd :rowSelection="false" :columns="profitsColumns" :data="baseData.profitsList" :pagination="false">
    </funi-curd>
    <GroupTitle title="应收款项管理情况"></GroupTitle>

    <funi-curd
      :rowSelection="false"
      :columns="accountReceivableColumns"
      :data="baseData.accountReceivableList"
      v-if="baseData.accountReceivableList.length"
      :pagination="false"
    >
    </funi-curd>
    <div style="padding: 0 8px 10px">
      <el-input
        type="textarea"
        :autosize="{
          minRows: 4
        }"
        v-model="baseData.receivableFileDesc"
        :disabled="!props.isEdit"
        placeholder="请输入..."
      ></el-input>
    </div>
    <SubmitSuccess ref="su"></SubmitSuccess>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onMounted, inject, nextTick, reactive, unref } from 'vue';
import { genFileId } from 'element-plus';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { ClickOutside as vClickOutside } from 'element-plus';
import {
  useSchema,
  useRule,
  useBudgetColumns,
  useCompareColumns,
  useCostInputListColumns,
  useSigningColumns,
  useReceiptColumns,
  useIncomeColumns,
  useProfitsColumns,
  useAccountReceivableColumns,
  getBudgetFields,
  getCompareFields,
  getSigningFields,
  getReceiptFields,
  getIncomeFields,
  getProfitsFields
} from './../../hooks/financeInfo.jsx';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import { newHttp, infoHttp, downloadFileTemplateHttp } from '../../hooks/financeApi.js';
import { ElNotification } from 'element-plus';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
import { useRouter } from 'vue-router';
const router = useRouter();
const from = ref();
const loadingStatus = inject('loadingStatus');
const emit = defineEmits(['updateID', 'updateInfo']);
const su = ref();
const fileList = ref([]);
const once = ref(true);
const upload = ref();
const setForm = e => {
  from.value = e;
};

const buttonRef = ref();
const popoverRef = ref();
const checkList = ref([
  '主要指标预算完成情况',
  '主要指标同比情况',
  '成本投入同比情况',
  '部门签单完成情况',
  '部门回款完成情况',
  '部门收入完成情况',
  '各公司利润贡献',
  '应收款项管理情况'
]);
const typeList = ref([
  '主要指标预算完成情况',
  '主要指标同比情况',
  '成本投入同比情况',
  '部门签单完成情况',
  '部门回款完成情况',
  '部门收入完成情况',
  '各公司利润贡献',
  '应收款项管理情况'
]);

// 上传返回对应关系
const eunms = {
  mainBudgetCompleteList: {
    key: 'budgetList',
    name: '主要指标预算完成情况',
    keys: {
      type: 'entryName',
      currentMonthCompleted: 'currentMonthAmount',
      accumulatedComplet: 'accumulateAmount',
      annualTarget: 'yearTargetAmount'
    }
  },
  mainIndicatorCompareList: {
    key: 'compareList',
    name: '主要指标同比情况分析',
    keys: {
      projectName: 'entryName',
      accumulatedInJanuary: 'currentMonthAmount',
      synchronismLastYear: 'lastYearAmount',
      incrDecrease: 'increaseDecreaseAmount',
      incrDecreaseRate: 'increaseDecreaseRatio',
      desc: 'differenceDescription'
    }
  },
  costInputCompareList: {
    key: 'costInputList',
    name: '成本投入同比情况分析',
    keys: {
      projectName: 'entryName',
      accumulatedInJanuary: 'currentMonthAmount',
      synchronismLastYear: 'lastYearAmount',
      incrDecrease: 'increaseDecreaseAmount',
      incrDecreaseRate: 'increaseDecreaseRatio',
      desc: 'differenceDescription'
    },
    total: '成本投入合计'
  },
  deptSignCompleteList: {
    key: 'signingList',
    name: '部门签单完成情况分析',
    keys: {
      dept: 'deptName',
      actualSigning: 'actualSignAmount',
      planSigning: 'planSignAmount',
      completionProgress: 'completeRatio',
      synchronismLastYear: 'lastYearAmount',
      incrDecrease: 'increaseDecreaseAmount',
      incrDecreaseRate: 'increaseDecreaseRatio'
    },
    desc: 'signingDesc',
    total: '合计'
  },
  deptReceiptCompleteList: {
    key: 'receiptList',
    name: '部门回款完成情况分析',
    keys: {
      dept: 'deptName',
      actualCollection: 'actualReceiptAmount',
      planCollection: 'planReceiptAmount',
      completionProgress: 'completeRatio',
      synchronismLastYear: 'lastYearAmount',
      incrDecrease: 'increaseDecreaseAmount',
      incrDecreaseRate: 'increaseDecreaseRatio'
    },
    desc: 'receiptDesc',
    total: '合计'
  },
  deptIncomeCompleteList: {
    key: 'incomeList',
    name: '部门收入完成情况分析',
    keys: {
      dept: 'deptName',
      actualIncomme: 'actualIncomeAmount',
      planIncome: 'planIncomeAmount',
      completionProgress: 'completeRatio',
      synchronismLastYear: 'lastYearAmount',
      incrDecrease: 'increaseDecreaseAmount',
      incrDecreaseRate: 'increaseDecreaseRatio'
    },
    desc: 'incomeDesc',
    total: '合计'
  },
  eachCompanyProfitList: {
    key: 'profitsList',
    name: '各公司利润贡献情况',
    keys: {
      profit: 'companyName',
      zhjc: 'zhjc',
      funi: 'flym',
      cddz: 'cddz',
      ncdz: 'ncdz',
      flty: 'flty',
      jzb: 'jzb',
      mergeOffset: 'mergeOffset'
    }
  },
  receivableAmountList: {
    key: 'accountReceivableList',
    name: '应收款项管理情况',
    keys: {
      deptName: 'deptName',
      projectName: 'projectName',
      partyA: 'partyA',
      currentIssueAdd: 'currentIssueAdd',
      receiptAmount: 'receiptAmount',
      receivableAmount: 'receivableAmount',
      overdueAmount: 'overdueAmount',
      overdueDays: 'overdueDays',
      notes: 'notes'
    },
    desc: 'receivableFileDesc'
  }
};

const props = defineProps({
  ...baseInfoProps,
  uploadUrl: {
    type: String,
    default: '/erm/financialReport/uploadFile'
  }
});
const baseData = reactive({
  budgetList: [],
  compareList: [],
  costInputList: [],
  signingList: [],
  receiptList: [],
  incomeList: [],
  profitsList: [],
  accountReceivableList: [],
  signingDesc: '',
  receiptDesc: '',
  incomeDesc: '',
  receivableFileDesc: ''
});
const init = ({
  targetBudget,
  targetCompare,
  deptSigning,
  deptReceipt,
  deptIncome,
  companyProfits,
  accountReceivable,
  costInput
}) => {
  const budgetFields = ['销售回款', '销售收入', '利润总额', '研发投入'];
  const compareFields = [
    '一、销售收入',
    '减：外采成本',
    '二、销售净收入',
    '减：成本总投入',
    '加：自主研发项目资本化',
    '待结转合同履约成本',
    '其他 (利息+补贴-坏账)',
    '三、利润总额',
    '四、经营性现金流'
  ];
  const costInputFields = [
    '成本投入合计',
    '人力成本',
    '其中：工资及绩效',
    '社保及公积金',
    '工会经费',
    '福利费',
    '辞退补偿金',
    '劳务外包',
    '折旧及摊销',
    '税金及附加',
    '经营管理费用'
  ];
  const commonFields = [
    '合计',
    '新业务事业群',
    '住建事业群',
    '其中：大西区',
    '外区',
    '增值业务中心',
    '其中：金融业务部',
    '公示系统部',
    '城市研究院',
    '数据治理部'
  ];
  budgetFields.map(key => {
    baseData.budgetList.push({
      ...getBudgetFields(),
      type: key,
      ...(JSON.parse(targetBudget || '[]').find(item => item?.type?.trim() === key) || {})
    });
  });
  compareFields.map(key => {
    baseData.compareList.push({
      ...getCompareFields(),
      projectName: key,
      ...(JSON.parse(targetCompare || '[]').find(item => item?.projectName?.trim() === key) || {})
    });
  });
  costInputFields.map(key => {
    baseData.costInputList.push({
      ...getCompareFields(),
      projectName: key,
      ...(JSON.parse(costInput || '[]').find(item => item?.projectName?.trim() === key) || {})
    });
  });
  const { list: signingList, desc: signingDesc } = JSON.parse(deptSigning || '{}');
  const { list: receiptList, desc: receiptDesc } = JSON.parse(deptReceipt || '{}');
  const { list: incomeList, desc: incomeDesc } = JSON.parse(deptIncome || '{}');
  baseData.signingDesc = signingDesc;
  baseData.receiptDesc = receiptDesc;
  baseData.incomeDesc = incomeDesc;
  commonFields.map(key => {
    baseData.signingList.push({
      ...getSigningFields(),
      dept: key,
      ...(signingList?.find(item => item?.dept?.trim() === key) || {})
    });

    console.log('baseData.signingList', baseData.signingList);

    baseData.receiptList.push({
      ...getReceiptFields(),
      dept: key,
      ...(receiptList?.find(item => item?.dept?.trim() === key) || {})
    });
    baseData.incomeList.push({
      ...getIncomeFields(),
      dept: key,
      ...(incomeList?.find(item => item?.dept?.trim() === key) || {})
    });
  });
  baseData.profitsList = [
    {
      ...getProfitsFields(),
      profit: '利润',
      ...(JSON.parse(companyProfits || '[]')[0] || {})
    }
  ];
  baseData.accountReceivableList = accountReceivable ? JSON.parse(accountReceivable) : [];
};
onMounted(() => {
  if (props.id) {
    infoHttp({
      financialReportId: props.id
    }).then(res => {
      const {
        title,
        month,
        creator,
        createTime,
        receivableFilePath,
        receivableFileName,
        receivableFileDesc,
        accountReceivable
      } = res;
      const parmas = {
        month,
        title
      };
      if (!props.isEdit) {
        Object.assign(parmas, {
          creator,
          createTime,
          month: month ? $utils.Date(month).format('YYYY-MM') : ''
        });
      }
      from.value.setValues(parmas);
      baseData.receivableFileDesc = receivableFileDesc;
      if (receivableFilePath) {
        fileList.value = [
          {
            name: receivableFileName,
            id: receivableFilePath,
            status: 'success',
            uid: genFileId(),
            receivableAmountList: accountReceivable
          }
        ];
      }
      init(res);
    });
  } else {
    init({});
  }
});

/**
 * @description 表单配置
 * **/
const schema = computed(() => {
  return useSchema({
    isEdit: props.isEdit
  });
});
/**
 * 计算合计
 */
const calculateTotal = list => {
  if (list === 'signingList') {
    const { actualSigning, planSigning } = baseData[list][0] || {};
    baseData[list][0].completionProgress =
      actualSigning && planSigning ? $utils.round((actualSigning / planSigning) * 100, 2) : '';
  }
  if (list === 'receiptList') {
    const { actualCollection, planCollection } = baseData[list][0] || {};
    baseData[list][0].completionProgress =
      actualCollection && planCollection ? $utils.round((actualCollection / planCollection) * 100, 2) : '';
  }
  if (list === 'incomeList') {
    const { actualIncomme, planIncome } = baseData[list][0] || {};
    baseData[list][0].completionProgress =
      actualIncomme && planIncome ? $utils.round((actualIncomme / planIncome) * 100, 2) : '';
  }
};
const iptChange = (index, list, key, value) => {
  baseData[list][index][key] = value;
  if (
    ['signingList', 'receiptList', 'incomeList'].includes(list) &&
    !['desc', 'completionProgress', 'incrDecreaseRate'].includes(key)
  ) {
    baseData[list][0][key] = $utils.sum(
      baseData[list].map(item =>
        ['新业务事业群', '住建事业群', '增值业务中心', '数据治理部'].includes(item.dept.trim()) ? item[key] : 0
      )
    );
    /**
     * 单独计算 完成进度 增减率
     */
    const { synchronismLastYear, incrDecrease } = baseData[list][0] || {};
    baseData[list][0].incrDecreaseRate =
      synchronismLastYear && incrDecrease ? $utils.round((incrDecrease / synchronismLastYear) * 100, 2) : '';
    calculateTotal(list);
  }

  if (['costInputList'].includes(list) && !['desc', 'incrDecreaseRate'].includes(key)) {
    baseData[list][0][key] = $utils.sum(
      baseData[list].map(item =>
        !['成本投入合计', '其中：工资及绩效', '社保及公积金', '工会经费', '福利费', '辞退补偿金'].includes(
          item.projectName.trim()
        )
          ? item[key]
          : 0
      )
    );
    /**
     * 同比增长率
     */
    const { synchronismLastYear, incrDecrease } = baseData[list][0] || {};
    baseData[list][0].incrDecreaseRate =
      synchronismLastYear && incrDecrease ? $utils.round((incrDecrease / synchronismLastYear) * 100, 2) : '';
  }
};
/**
 * 表格配置
 */
const budgetColumns = computed(() => {
  return useBudgetColumns({
    isEdit: props.isEdit,
    iptChange
  });
});
const compareColumns = computed(() => {
  return useCompareColumns({
    isEdit: props.isEdit,
    iptChange
  });
});
const costInputListColumns = computed(() => {
  return useCostInputListColumns({
    isEdit: props.isEdit,
    iptChange
  });
});

const signingColumns = computed(() => {
  return useSigningColumns({
    isEdit: props.isEdit,
    iptChange
  });
});
const receiptColumns = computed(() => {
  return useReceiptColumns({
    isEdit: props.isEdit,
    iptChange
  });
});
const incomeColumns = computed(() => {
  return useIncomeColumns({
    isEdit: props.isEdit,
    iptChange
  });
});
const profitsColumns = computed(() => {
  return useProfitsColumns({
    isEdit: props.isEdit,
    iptChange
  });
});
const accountReceivableColumns = computed(() => {
  return useAccountReceivableColumns();
});
const rules = computed(() => {
  return useRule({ isEdit: props.isEdit });
});
const tableRowClassName = ({ row, rowIndex }) => {
  if (rowIndex === 0) {
    return 'sum-row';
  }
};
const handleExceed = files => {
  upload.value?.clearFiles();
  const file = files[0];
  file.uid = genFileId();
  upload.value?.handleStart(file);
  upload.value?.submit();
};
const request = function (params) {
  let formData = new FormData();
  formData.append('file', params.file);
  for (const [key, value] of Object.entries(params.data)) {
    formData.append(key, value);
  }
  $http.upload(params.action, formData).then(res => {
    params.onSuccess(res);
    // baseData.accountReceivableList = JSON.parse(res?.accountReceivable) || [];
    // console.log('res', res);
    let resData = JSON.parse(res.fileContentJson);
    console.log('resData', resData);

    Object.keys(resData).forEach(key => {
      if (Array.isArray(resData[key]) && resData[key].length) {
        baseData[eunms[key].key] = [];
      }

      if (eunms[key].total && resData[key] && Array.isArray(resData[key])) {
        baseData[eunms[key].key] = [
          {
            projectName: eunms[key].total,
            dept: eunms[key].total,
            totalBool: true
          }
        ];
      }

      if (resData[key] && Array.isArray(resData[key])) {
        resData[key].forEach(item => {
          if (!item.isDesc) {
            let obj = {};
            Object.keys(eunms[key].keys).forEach(_key => {
              obj[_key] = item[eunms[key].keys[_key]];
            });
            baseData[eunms[key].key].push($utils.clone(obj, true));
          } else {
            baseData[eunms[key].desc] = item.deptName;
          }
        });
      }
    });
    ['budgetList', 'compareList', 'costInputList', 'signingList', 'receiptList', 'incomeList', 'profitsList'].forEach(
      key => {
        // console.log('key', baseData[key]);
        if (baseData[key] && Array.isArray(baseData[key])) {
          baseData[key].forEach((item, index) => {
            Object.keys(item).forEach(keys => {
              if (typeof item[keys] === 'number' && !item.totalBool) {
                iptChange(index, key, keys, item[keys]);
              }
            });
          });
        }
      }
    );
  });
};

/**
 * @description 数据保存函数
 * **/
const saveDate = async type => {
  let data = await getData(type);
  if (!data) return;
  loadingStatus.value.status = true;
  let resolveData = await newHttp(data).finally(() => {
    loadingStatus.value.status = true;
  });
  emit('updateID', resolveData.id);
  await nextTick();
  if (!props.id) return Promise.reject();
  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  } else {
    su.value.show();
  }
  return Promise.resolve({});
};

/**
 * @description 表单数据处理
 * **/
const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await from.value.validate();
  if (type == 'ts' || isValid) {
    let fromData = from.value.getValues();
    const { name, id, response, receivableAmountList } = fileList.value[0] || {};
    let obj = {
      id: props.id || '',
      ...fromData,
      targetBudget: JSON.stringify(baseData.budgetList),
      targetCompare: JSON.stringify(baseData.compareList),
      costInput: JSON.stringify(baseData.costInputList),
      deptSigning: JSON.stringify({
        list: baseData.signingList,
        desc: baseData.signingDesc
      }),
      deptReceipt: JSON.stringify({
        list: baseData.receiptList,
        desc: baseData.receiptDesc
      }),
      deptIncome: JSON.stringify({
        list: baseData.incomeList,
        desc: baseData.incomeDesc
      }),
      companyProfits: JSON.stringify(baseData.profitsList),
      receivableFilePath: response?.receivableFilePath || id,
      receivableFileName: name,
      receivableFileDesc: baseData.receivableFileDesc,
      accountReceivable: JSON.stringify(baseData?.accountReceivableList)
    };
    Object.assign(obj, {
      month: obj.month ? $utils.Date(obj.month).format('YYYY-MM-DD') : ''
    });
    if (props.type === 'copy' && once.value) {
      Reflect.deleteProperty(obj, 'id');
      once.value = false;
    }

    return obj;
  } else {
    return false;
  }
};
/**
 * 下载模板
 */
const downloadTemplate = () => {
  let list = typeList.value.filter(item => {
    return checkList.value.indexOf(item) < 0;
  });
  if (!checkList.value.length) {
    ElNotification({
      title: '必须选择一个模板',
      type: 'error'
    });
    return;
  }
  downloadFileTemplateHttp({
    fileNameList: list
  });
};
/**
 * @description 下一步 提交数据
 * **/
const submit = () => {
  return saveDate();
};
const ts = () => {
  return saveDate('ts');
};

const onClickOutside = () => {
  unref(popoverRef).popperRef?.delayHide?.();
};

defineExpose({
  submit,
  ts
});
</script>
<style>
.sum-row {
  --el-table-tr-bg-color: yellowgreen;
}
.el-month-table td.current:not(.disabled) .cell {
  color: #fff;
  background-color: var(--el-datepicker-active-color);
}
</style>
<style scoped lang="scss">
.upload-file {
  padding: 10px 9px 0 9px;
}
</style>
