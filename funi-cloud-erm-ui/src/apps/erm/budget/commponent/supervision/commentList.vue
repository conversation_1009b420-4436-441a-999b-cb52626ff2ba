<!-- 督办件信息回复列表-->
<template>
  <div class="comment_wrapper">
    <div v-for="(item, index) in replyLists" class="comment-item-wrapper" :key="item.id">
      <div class="comment_owner">
        <div class="content">
          <div class="info">
            <span class="user_name">{{ item.replyEmpName }}</span>
            <span class="user_dept">{{ item.replyDeptName }}</span>
          </div>

          <template v-if="index == 0">
            <div class="comment_cotent" style="font-weight: bold; font-size: 15px; color: red">
              {{ item.replyContext }}
            </div>
          </template>
          <template v-else>
            <div class="comment_cotent">
              {{ item.replyContext }}
            </div>
          </template>

          <div class="comment_owner_date">
            <span>{{ item.createTime }}</span>
            <!-- <span style="color:var(--el-color-primary);">{{ item.childrenReplyList.length }}条回复</span> -->
            <!-- {{ props.isOpera }} -->
            <span v-if="props.isOpera" class="comment_opera">
              <el-icon :color="item.show ? 'var(--el-color-primary)' : ''">
                <ChatDotRound />
              </el-icon>
              <span :class="{ text: true, text_cancel: item.show }" @click="() => hideInput(item)">{{
                item.show ? '取消回复' : '回复'
              }}</span>
            </span>
          </div>
        </div>
      </div>

      <div class="reply_wrapper" v-show="item.show">
        <div ref="inputRef" class="input_box">
          <div
            ref="textRef"
            contenteditable="true"
            placeholder="请输入回复信息"
            class="rich-input"
            @input="e => input(e, item)"
          >
            {{ item._commentText }}
          </div>
          <div class="action_box">
            <el-button type="primary" :disabled="!item._disAble" @click="() => submit(item)">回复</el-button>
          </div>
        </div>
      </div>
      <div class="comment_list">
        <div class="comment_list_item" v-for="(child, idx) in item.childrenReplyList" :key="child.id">
          <div class="comment_list_main">
            <div class="comment_user_info">
              <span class="comment_user_name">{{ child.replyEmpName }}</span>
              <span class="comment_user_dept">{{ child.replyDeptName }}</span>
            </div>
            <div class="comment_user_cotent">
              {{ child.replyContext }}
            </div>
            <div class="comment_date">
              <span> {{ child.createTime }} </span>
              <span v-if="props.isOpera">
                <el-icon :color="child.show ? 'var(--el-color-primary)' : ''">
                  <ChatDotRound />
                </el-icon>
                <span :class="{ text: true, text_cancel: child.show }" @click="() => hideInput(child, idx)">{{
                  child.show ? '取消回复' : '回复'
                }}</span>
              </span>
            </div>
            <div class="reply_user_wrapper" v-show="child.show">
              <div ref="replyRef" :class="{ input_user_box: true }">
                <div
                  ref="twoReplyRef"
                  contenteditable="true"
                  placeholder="请输入回复信息"
                  class="rich-input"
                  @input="e => handleInput(e, child)"
                >
                  {{ child._commentText }}
                </div>
                {{ item.comment }}
                <div class="action_box">
                  <el-button type="primary" size="small" :disabled="!child._disable" @click="() => submit(child)"
                    >回复</el-button
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="owner-reply" v-for="cc in child.childrenReplyList" :key="cc.id">
            <div class="owner-reply-content">
              <span>
                <span style="font-size: 14px; font-weight: bold">{{ cc.replyEmpName }}</span>
                <span style="font-size: 14px; color: #999999; margin: 0 8px">{{ cc.replyDeptName }}</span></span
              >
              <span v-if="cc.parentReplyEmpName">
                <span>回复</span>
                <span>
                  <span style="font-weight: bold">
                    {{ cc.parentReplyEmpName }}
                  </span>
                  <span style="font-size: 14px; color: #999999; margin: 0 8px">{{ cc.replyDeptName }}</span>
                </span>
                <span>:</span>
              </span>
              <span class="text">{{ cc.replyContext }}</span>
            </div>
            <div class="owner-reply-date">
              <span class="date">{{ cc.replyTime }}</span>
              <span class="t_c" v-if="props.isOpera">
                <el-icon :color="cc.show ? 'var(--el-color-primary)' : ''">
                  <ChatDotRound />
                </el-icon>
                <span :class="{ text: true, text_cancel: cc.show }" @click="() => hideInput(cc)">{{
                  cc.show ? '取消回复' : '回复'
                }}</span>
              </span>
            </div>
            <div class="reply_user_wrapper" v-show="cc.show">
              <div ref="replyRef" :class="{ input_user_box: true }">
                <div
                  ref="twoReplyRef"
                  contenteditable="true"
                  placeholder="请输入回复信息"
                  class="rich-input"
                  @input="e => handleInput(e, cc)"
                >
                  {{ cc._commentText }}
                </div>
                {{ cc.comment }}
                <div class="action_box">
                  <el-button type="primary" size="small" :disabled="!cc._disable" @click="() => submit(cc)"
                    >回复</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue';
import { SupervisionReplyNewHttp } from '@/apps/erm/budget/hooks/supervisionApi.jsx';
import { useAppStore } from '@/stores/useAppStore';
const appStore = useAppStore();
let user = appStore.user;
let props = defineProps({
  isOpera: {
    type: Boolean,
    default: true
  },
  replyList: {
    type: Array,
    default: () => []
  }
});

const emits = defineEmits(['reload']);
let inputRef = ref(null);
let replyRef = ref(null);
let textRef = ref(null);
let replyLists = computed(() => props.replyList);

const hideInput = item => {
  if (!item['show']) {
    item['show'] = true;
  } else {
    item['show'] = false;
    item['_commentText'] = '';
  }
};
const input = (e, item) => {
  item['_disAble'] = !!e;
  item['_commentText'] = e.target.textContent;
};

//子回复点击事件
const submit = async item => {
  let params = {
    replyContext: item?._commentText,
    parentId: item?.id,
    supervisionSn: item?.supervisionSn,
    supervisionId: item?.supervisionId,
    replyeeId: user.userId
  };
  try {
    await SupervisionReplyNewHttp(params);
  } finally {
    textRef.value.textContent = '';
    item['show'] = false;
    emits('reload');
  }
};

const handleInput = (e, item) => {
  item['_disable'] = !!e;
  item['_commentText'] = e.target.textContent;
};
</script>
<style lang="scss" scoped>
.comment_wrapper {
  display: flex;
  flex-direction: column;
  // max-width: 90%;
  padding: 8px 10px 0 8px;
  word-wrap: break-word;

  .comment-item-wrapper {
    border-bottom: #48515e;

    .comment_owner {
      .content {
        padding-bottom: 12px;
        // border-bottom: 1px solid #8a919f;

        .info {
          .user_name {
            font-size: 14px;
            color: #48515e;
            font-weight: bold;
            margin-right: 8px;
          }

          .user_dept {
            font-size: 14px;
            color: #999999;
            padding: 4px;
            // background-color: rgba(var(--el-color-primary), .2);
          }
        }

        .comment_cotent {
          padding: 5px 0px;
          border-radius: 4px;
          // border-bottom: 1px solid #ecedf0;
          color: #303133;
          line-height: 22px;
        }

        .comment_owner_date {
          display: flex;
          align-items: center;
          padding-bottom: 8px;
          // border-bottom: 1px solid #ecedf0;
          color: #999999;
          font-size: 14px;

          span {
            margin-right: 30px;
          }

          .comment_opera {
            display: flex;
            align-items: center;

            .text {
              font-size: 14px;
              margin-left: 4px;

              &:hover {
                cursor: pointer;
              }
            }

            .text_cancel {
              font-size: 14px;
              margin-left: 4px;
              color: var(--el-color-primary);

              &:hover {
                cursor: pointer;
              }
            }
          }
        }
      }
    }

    .reply_wrapper {
      .input_box {
        // display: flex;
        // flex-direction: column;
        // min-height: 88px;
        // font-size: 0;
        // transition: all .3s;
        // // background: #f2f3f5;
        // border: 1px solid transparent;
        // border-radius: 4px;
        // position: relative;
        display: flex;
        flex-direction: column;
        min-height: 88px;
        font-size: 14px;
        background: #fff;
        border: 1px solid var(--el-color-primary);
        border-radius: 4px;
        position: relative;
        margin-bottom: 6px;

        .rich-input {
          flex: 1;
          position: relative;
          padding: 8px 12px;
          color: #606266;
          outline: none;
          box-sizing: border-box;
          line-height: 28px;
          font-size: 14px;
          min-height: 44px;
          max-height: 100px;
          overflow: auto;

          &:empty:before {
            content: attr(placeholder);
            color: #bbb;
          }
        }

        .action_box {
          flex-shrink: 0;
          height: 32px;
          padding: 0 12px 12px 6px;
          display: flex;
          align-items: flex-end;
          flex-direction: column-reverse;
        }
      }

      .input_box.focused {
        min-height: 120px;
        max-height: 240px;
        outline: none;
        border: 1px solid var(--el-color-primary);
      }
    }

    .comment_list {
      display: flex;
      flex-direction: column;
      // background: #f5f5f5;

      .comment_list_item {
        /* margin: 6px 0px; */
        // background: #f5f5f5;
        // padding: 6px 0;
        margin-bottom: 5px;
        // border-bottom: 1px solid #999999;

        .comment_list_main {
          padding: 0px 40px;
          // border-bottom: 1px solid #ecedf0;

          .comment_user_info {
            .comment_user_name {
              font-size: 14px;
              color: #515767;
              font-weight: bold;
              padding-right: 8px;
            }

            .comment_user_dept {
              font-size: 14px;
              color: #999999;
            }
          }

          .comment_user_cotent {
            padding: 5px 0px;
            border-radius: 4px;
            color: #303133;
            line-height: 22px;
          }

          .comment_date {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 14px;
            color: #999999;

            span {
              display: flex;
              align-items: center;
              margin-right: 30px;
            }

            .text {
              font-size: 14px;
              margin-left: 4px;

              &:hover {
                cursor: pointer;
              }
            }

            .text_cancel {
              font-size: 14px;
              margin-left: 4px;
              color: var(--el-color-primary);

              &:hover {
                cursor: pointer;
              }
            }
          }

          .reply_user_wrapper {
            .input_user_box {
              display: flex;
              flex-direction: column;
              min-height: 88px;
              font-size: 14px;
              background: #fff;
              border: 1px solid var(--el-color-primary);
              border-radius: 4px;
              position: relative;

              .rich-input {
                flex: 1;
                position: relative;
                padding: 8px 12px;
                color: #606266;
                outline: none;
                box-sizing: border-box;
                line-height: 28px;
                font-size: 14px;
                min-height: 44px;
                max-height: 100px;
                overflow: auto;

                &:empty:before {
                  content: attr(placeholder);
                  color: #bbb;
                }
              }

              .action_box {
                flex-shrink: 0;
                height: 32px;
                padding: 0 12px 12px 6px;
                display: flex;
                align-items: flex-end;
                flex-direction: column-reverse;
              }
            }
          }
        }

        .owner-reply {
          padding: 0px 80px;
          color: #606266;
          // border-bottom: 1px solid #ecedf0;

          .owner-reply-content {
            padding: 5px 5px 5px 0;

            span {
              margin-right: 6px;
            }

            .text {
              color: #303133;
            }
          }

          .owner-reply-date {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 14px;
            color: #999999;

            .date {
              margin-right: 30px;
            }

            .text {
              font-size: 14px;
              margin-left: 4px;
            }

            .text_cancel {
              font-size: 14px;
              margin-left: 4px;
              color: var(--el-color-primary);

              &:hover {
                cursor: pointer;
              }
            }

            .t_c {
              display: flex;
              align-items: center;
            }
          }

          .reply_user_wrapper {
            .input_user_box {
              display: flex;
              flex-direction: column;
              min-height: 88px;
              font-size: 0;
              background: #fff;
              border: 1px solid var(--el-color-primary);
              border-radius: 4px;
              position: relative;

              .rich-input {
                flex: 1;
                position: relative;
                padding: 8px 14px;
                color: #606266;
                outline: none;
                box-sizing: border-box;
                line-height: 28px;
                font-size: 16px;
                min-height: 44px;
                max-height: 100px;
                overflow: auto;

                &:empty:before {
                  color: #bbb;
                }
              }

              .action_box {
                flex-shrink: 0;
                height: 32px;
                padding: 0 12px 12px 6px;
                display: flex;
                align-items: flex-end;
                flex-direction: column-reverse;
              }
            }
          }
        }
      }
    }
  }
}
</style>
