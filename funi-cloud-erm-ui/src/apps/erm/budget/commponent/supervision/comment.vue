<!-- 督办件信息回复-->
<template>
  <div class="reply-wrapper">
    <div class="reply_container">
      <div class="reply-user">
        {{ user.userName }}
      </div>
      <div class="reply_wrapper">
        <div class='input_box'
          @click="inpClick"
          @input="input">
          <div ref="textRef"
            contenteditable="true"
            placeholder="请输入回复信息"
            class="rich-input">
          </div>
          <div class="action_box">
            <el-button type="primary"
              :disabled="!rDisAble"
              @click="submit">回复</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

import { useAppStore } from '@/stores/useAppStore';
const appStore = useAppStore();
let user = appStore.user;
const textRef = ref(null)
let replyText = ref('')
let rDisAble = computed(() => !!replyText.value && replyText.value.length > 0)
const input = (e) => {
  replyText.value = e.target.textContent
}
const emits = defineEmits(['submitReply'])
const submit = async () => {
  emits('submitReply', replyText.value, user)
  replyText.value = ''
  textRef.value.textContent = ''
}


</script>

<style lang="scss">
.reply-wrapper {
  border-top: 1px solid #ccc;
  padding: 10px 0;
  // width: 100%;

  .reply_container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    width: 95%;

    .reply-user {
      display: flex;
      align-items: center;
      justify-content: center;
      // width: 5%;
      font-size: 24px;
      padding: 10px;

    }

    .reply_wrapper {
      flex: 1;

      .input_box {
        display: flex;
        flex-direction: column;
        min-height: 88px;
        font-size: 0;
        transition: all .3s;
        background: #f2f3f5;
        border: 1px solid transparent;
        border-radius: 4px;
        position: relative;

        .rich-input {
          flex: 1;
          position: relative;
          padding: 8px 12px;
          color: #606266;
          outline: none;
          box-sizing: border-box;
          line-height: 28px;
          font-size: 16px;
          min-height: 44px;
          max-height: 100px;
          overflow: auto;

          &:empty:before {
            content: attr(placeholder);
            color: #bbb;
          }
        }

        .action_box {
          flex-shrink: 0;
          height: 32px;
          padding: 0 12px 12px 6px;
          display: flex;
          align-items: flex-end;
          flex-direction: column-reverse;
        }
      }
    }

  }
}
</style>
