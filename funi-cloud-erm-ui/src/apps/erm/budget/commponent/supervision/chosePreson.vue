<template>
  <div>
    <InfiniteSelect
      :multiple="false"
      :defaultProps="defaultProps"
      :api="apiUrl.chooseReminderPersonList"
      :isUseTable="true"
      v-model="currentPerson"
    />
    <div class="person_list">
      <p>已选择人员</p>
      <div>
        <el-tooltip
          v-for="item in selectedPerson"
          placement="top-start"
          :width="200"
          :key="item.id"
          trigger="hover"
          :content="item.contractCompanyName"
        >
          <el-tag size="large" :closable="true" @close="handleClose(item)">
            {{ item.name }}
          </el-tag>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script lang="jsx" setup>
import { onMounted, ref, watch } from 'vue';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import { apiUrl, chooseReminderPersonListHttp } from '@/apps/erm/budget/hooks/supervisionApi.jsx';
import { ElNotification } from 'element-plus';
let currentPerson = ref();
let selectedPerson = ref([]);
let taskSn = ref();

let defaultProps = {
  keyWord: 'keyword',
  name: 'employeeName',
  id: 'id',
  sn: 'contractCompanyName',
  nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
};
watch(
  () => currentPerson.value,
  newValue => {
    if (newValue) {
      let ids = selectedPerson.value.map(item => item.id);
      let regex = /(?<=\()(.+?)(?=\))/g;
      let cur = {
        contractCompanyName: newValue.name.match(regex)?.[0] || '',
        id: newValue.id,
        name: newValue.defaultName
      };
      if (!ids.includes(newValue.id)) selectedPerson.value.push(cur);
    }
  },
  {
    immediate: true,
    deep: true
  }
);

const handleClose = cur => {
  if (selectedPerson.value.length == 1) {
    ElNotification({
      message: '至少保留一个催办人员',
      type: 'warning'
    });
    return;
  }
  selectedPerson.value = selectedPerson.value.filter(item => item.id != cur.id);
};
const getRow = async row => {
  taskSn.value = row.id;
  await chooseReminderPersonListHttp({
    leaderId: row?.leaderId,
    employeeIds: row?.employeeIds,
    leadDeptId: row?.leadDeptId,
    assistDeptId: row?.assistDeptId
  }).then(res => {
    selectedPerson.value = res.list.map(item => {
      return {
        id: item.id,
        name: item.employeeName,
        contractCompanyName: item.contractCompanyName
      };
    });
  });
};

defineExpose({
  getRow,
  empIdList: selectedPerson,
  taskSn: taskSn
});
</script>
<style lang="scss" scoped>
.person_list {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-height: 400px;
  margin: 20px 0;
  p {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    background-color: #ccc;
    margin: 0;
    padding: 10px 0;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }
  :deep(.el-tag--large) {
    font-size: 14px;
    height: 40px;
    margin: 5px;
  }
}
</style>
