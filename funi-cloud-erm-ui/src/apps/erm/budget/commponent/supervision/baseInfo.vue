<template>
  <div v-loading="loading">
    <GroupTitle title="基本信息" />
    <funi-form :schema="schema" @get-form="setForm" :rules="rules" :col="3" :border="false" />
    <SubmitSuccess ref="submitSuccess" />
    <div v-if="!isEdit && type != 'audit'">
      <GroupTitle title="回复信息" />
      <CommentList :replyList="replyList" @reload="reload" />
      <Comment @submitReply="submitReply" />
    </div>
  </div>
</template>

<script setup>
import { computed, ref, reactive, onMounted, inject, watch, onActivated } from 'vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { useSchema, useRule } from '@/apps/erm/budget/hooks/supervisonInfo.jsx';
import {
  SupervisionReplyNewHttp,
  SupervisionContentInfoHttp,
  supervisionContentNewHttp
} from '@/apps/erm/budget/hooks/supervisionApi.jsx';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
import Comment from '@/apps/erm/budget/commponent/supervision/comment.vue';
import CommentList from '@/apps/erm/budget/commponent/supervision/commentList.vue';
import { openBusiness, submitBusiness } from '@/apps/erm/config/business.js';
import { creatBusHttp } from '@/apps/erm/budget/hooks/supervisionApi.jsx';

const submitSuccess = ref(null);
const loadingStatus = inject('loadingStatus');
const currentId = inject('currentId');
const props = defineProps({
  ...baseInfoProps,
  operation: {
    type: String,
    default: ''
  },
  reason: {
    type: String,
    default: ''
  },
  businessName: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  editType: {
    type: String,
    default: ''
  }
});
const emits = defineEmits(['updateInfo', 'submit']);
let loading = ref(false);

const _data = reactive({
  formData: null
});

const replyList = ref(void 0);
const supervisionInfo = ref(void 0);
const schema = computed(() => {
  return useSchema({
    isEdit: props.isEdit,
    operation: props.operation,
    editType: props.editType,
    type: props.type
  });
});

const setForm = e => {
  _data.formData = e;
};

const rules = computed(() => {
  return useRule({
    isEdit: props.isEdit
  });
});
//保存数据
const saveData = async () => {
  loadingStatus.value.status = true;
  let { isValid } = await _data.formData.validate();
  if (!isValid) return;
  let data = _data.formData.getValues();
  let employeeIds = data.employeeIds?.toString();
  let obj = {
    ...data,
    employeeIds: employeeIds
  };
  Reflect.deleteProperty(obj, 'employeeNames');
  Reflect.deleteProperty(obj, 'options');
  if (props.type == 'add' || props.editType == 'normal') {
    await supervisionContentNewHttp({ ...obj });
  } else {
    if (!props.businessId) {
      let resData = await openBusiness('ERM_SUPERVISION_CONTENT_CHANGE', obj, props.reason, props.businessName);
      await submitBusiness(
        resData.businessData.dicBusinessTypeCode,
        resData.businessData.businessId,
        'SUBMIT',
        '督办件延期申请'
      );
      emits('updateInfo', resData.businessData);
    } else {
      await supervisionContentNewHttp({ ...obj });
      await submitBusiness(obj.dicBusinessTypeCode, obj.businessId, 'SUBMIT', '督办件延期申请');
    }
  }

  submitSuccess.value.show();
  loadingStatus.value.status = false;
};
//提交评论重新加载
const submitReply = async (text, user) => {
  loading.value = true;
  let params = {
    replyContext: text,
    supervisionSn: supervisionInfo.value.supervisionSn,
    supervisionId: supervisionInfo.value.id,
    replyeeId: user.userId
  };
  try {
    await SupervisionReplyNewHttp(params);
  } finally {
    getSupervisionInfo();
    loading.value = false;
  }
};

//子回复重新加载
const reload = async cid => {
  console.log(cid);
  loading.value = true;
  await getSupervisionInfo(cid);
  loading.value = false;
};

//获取详情数据
const getSupervisionInfo = async cid => {
  let res;
  if (props.operation == 'change') {
    res = await creatBusHttp({ id: cid ?? props.id, dicBusinessTypeCode: 'ERM_SUPERVISION_CONTENT_CHANGE' });
  } else {
    res = await SupervisionContentInfoHttp({ supervisionId: cid ?? props.id });
  }
  replyList.value = res.supervisionReplyList;
  supervisionInfo.value = res;
  emits('updateInfo', res);
  _data.formData.setValues({
    ...res,
    employeeIds: res.supervisionEmployeeList?.map(item => {
      return item.employeeId;
    }),
    employeeNames: res.supervisionEmployeeList
      ?.map(item => {
        return item.employeeName;
      })
      .toString?.(),
    options: res.supervisionEmployeeList?.map(item => {
      return {
        id: item.employeeId,
        employeeName: item.employeeName
      };
    }),

    leaderOptions: [
      {
        id: res.leaderId,
        employeeName: res.leaderName
      }
    ],
    leadDeptOption: [
      {
        id: res.leadDeptId,
        deptName: res.leadDeptName
      }
    ],
    assistDeptOption: [
      {
        id: res.assistDeptId,
        deptName: res.assistDeptName
      }
    ],
    replyLength: res.supervisionReplyList.length > 0 ? `${res.supervisionReplyList.length}条` : '--'
  });
};

onMounted(() => {
  console.log(props.id, 'props.id');
  if (props.id) {
    getSupervisionInfo();
  } else {
    _data.formData.setValues({
      dicSupervisionTypeCode: '1'
    });
  }
});
//保存
const submit = () => {
  return saveData();
};

defineExpose({
  submit,
  reload
});
</script>
