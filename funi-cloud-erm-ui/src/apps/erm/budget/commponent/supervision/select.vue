<template>
  <div>
    <el-select
      v-model="selectValue"
      clearable
      style="width: 100%"
      ref="l_select"
      remote
      :multiple="props.multiple"
      filterable
      @visibleChange="visibleChange"
      :remote-method="e => queryMethod(e)"
      @change="selectChange"
    >
      <el-option
        v-for="item in options"
        :key="item[defaultProps.id]"
        :label="item[defaultProps.name]"
        :value="item[defaultProps.id]"
      />
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive, onUnmounted, watch, computed, nextTick } from 'vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { useAppStore } from '@/stores/useAppStore';
const store = useAppStore();
let l_select = ref();
const options = ref([]);
const props = defineProps({
  modelValue: {
    type: [Array, String]
  },
  defaultProps: {
    type: Object,
    default: {
      keyWord: 'keyWord',
      name: 'name',
      id: 'id'
    }
  },
  options: {
    type: Array,
    default: () => []
  },
  api: {
    type: String,
    default: ''
  },
  code: {
    type: String,
    default: ''
  },
  multiple: {
    type: Boolean,
    default: true
  }
});

console.log(props, 'props');
const emits = defineEmits(['update:modelValue', 'clear']);
const selectValue = computed(() => {
  if (props.multiple && !Array.isArray(props.modelValue)) {
    return [];
  }
  if (!props.multiple && Array.isArray(props.modelValue)) {
    return (selectValue.value = '');
  }
  return props.modelValue;
});
// options item 回显
watch(
  () => props.options,
  async newValue => {
    if (newValue) {
      options.value = newValue;
    }
  },
  { immediate: true, deep: true }
);

const params = reactive({
  pageNo: 1,
  pageSize: 10,
  [props.defaultProps.keyWord]: ''
});

const visibleChange = () => {
  params.pageNo = 1;
  params.pageSize = 10;
  l_select.value?.scrollbarRef.setScrollTop(0);
};

const loadData = async params => {
  if (props.code) {
    let res = await $http.fetch(ermGlobalApi.dictList, { typeCode: props.code, clientId: store.system.clientId });
    return res;
  } else {
    let res = await $http.post(props.api, params);
    return res.list;
  }
};

//初始化options 清除回显的option
const initData = async () => {
  let res = await loadData(params);
  if (props.code) {
    options.value = res;
  } else {
    options.value = [];
    options.value.push(res);
  }
};
const queryMethod = async keyword => {
  params[props.defaultProps.keyWord] = keyword;
  let res = await loadData({ [props.defaultProps.keyWord]: params[props.defaultProps.keyWord] });
  options.value = res;
};

const selectChange = e => {
  emits('update:modelValue', e);
};

onMounted(async () => {
  initData();
  l_select.value?.scrollbarRef.wrapRef?.addEventListener('scroll', async () => {
    const { scrollTop, scrollHeight, clientHeight } = l_select.value.scrollbarRef.wrapRef;
    const scrollDistance = scrollHeight - scrollTop <= clientHeight + 10;
    if (scrollDistance) {
      params.pageNo++;
      let res = await loadData(params);
      options.value.push(...res);
    }
  });
});
onUnmounted(() => {
  l_select.value?.scrollbarRef?.wrapRef?.removeEventListener('scroll');
});
</script>
<style lang="scss" scoped></style>
