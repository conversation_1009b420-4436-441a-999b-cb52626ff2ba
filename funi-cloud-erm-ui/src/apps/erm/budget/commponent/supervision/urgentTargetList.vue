<template>
  <funi-dialog
    :hideFooter="false"
    v-model="show"
    :align-center="true"
    :title="step == 1 ? '选择催办人员' : '催办内容'"
    :onConfirm="onConfirm"
    :onCancel="onCancel"
    @close="onCancel"
  >
    <chosePerson v-if="step == 1" ref="chosePersonRef" />
    <funi-form v-else :schema="schema" @get-form="setForm" :rules="rules" :col="1" :border="false" />
  </funi-dialog>
</template>
<script lang="jsx" setup>
import { computed, ref, onActivated, reactive, nextTick } from 'vue';
import { chooseReminderPersonListHttp, budgetProjectReminderHttp } from '@/apps/erm/budget/hooks/supervisionApi.jsx';
import { useUrgentColumns, useSearchConfig } from '@/apps/erm/budget/hooks/supervision.jsx';
import chosePerson from '@/apps/erm/budget/commponent/supervision/chosePreson.vue';
import { ElNotification } from 'element-plus';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['close', 'confirm']);
const show = computed(() => props.modelValue);
const chosePersonRef = ref();
let empIdList = ref([]);
let taskSn = ref(void 0);
const step = ref(1);

const schema = computed(() => {
  return [
    {
      label: '催办内容',
      component: 'el-input',
      prop: 'reminderContent',
      props: {
        placeholder: '请输入催办内容',
        maxlength: 200,
        clearable: true,
        type: 'textarea'
      },
      colProps: {
        span: 24
      }
    }
  ];
});
const _data = reactive({
  formData: null
});

const setForm = e => {
  _data.formData = e;
};

const rules = computed(() => {
  return { reminderContent: [{ required: true, message: '必填', trigger: 'change' }] };
});

const onConfirm = async () => {
  if (step.value == 1) {
    empIdList.value = chosePersonRef.value.empIdList.map(item => item.id);
    taskSn.value = chosePersonRef.value.taskSn;
    step.value++;
    await nextTick();
    _data.formData.setValues({
      reminderContent:
        '您有一项督办件尚未办结，请尽快办理并填报事项进度情况。因特殊情况需进行延期，请及时在经营协同平台办理延期流程。'
    });
    return;
  }
  if (step.value == 2) {
    let { isValid } = await _data.formData.validate();
    if (isValid) {
      let data = _data.formData.getValues();
      await budgetProjectReminderHttp({
        dicReminderTypeCode: '3',
        reminderContent: data.reminderContent,
        taskSn: taskSn.value,
        empIdList: empIdList.value
      }).then(() => {
        ElNotification({
          message: '催办成功！',
          type: 'success'
        });
        show.value = false;
        step.value = 1;
        emits('confirm');
      });
    }
  }
};

const onCancel = () => {
  show.value = false;
  step.value = 1;
  emits('close');
};

defineExpose({
  ref: chosePersonRef
});
</script>
<style lang="scss" scoped></style>
