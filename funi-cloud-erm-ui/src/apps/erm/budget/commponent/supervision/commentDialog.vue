<template>
  <div class="dialog_wrapper">
    <funiDialog
      v-bind="$attrs"
      v-model="visible"
      :size="type === 'info' ? 'large' : 'default'"
      @close="close"
      :hideFooter="true"
    >
      <div
        class="left"
        @click="click(-1)"
        v-if="showLeft"
      >
        <el-icon>
          <ArrowLeftBold />
        </el-icon>
      </div>
      <div
        class="right"
        @click="click(1)"
        v-if="showRight"
      >
        <el-icon>
          <ArrowRightBold />
        </el-icon>
      </div>

      <el-scrollbar>
        <div v-if="props.type === 'info'">
          <BaseInfo
            ref="baseInfoRef"
            :isEdit="false"
            :id="currentId"
          />
        </div>
        <div v-else>
          <div class="title">
            {{ title }}
          </div>
          <el-divider />
          <CommentList
            :isOpera="false"
            :replyList="replyList"
          />
        </div>
      </el-scrollbar>
    </funiDialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import CommentList from '@/apps/erm/budget/commponent/supervision/commentList.vue'
import { querySupervisionContentListHttp, SupervisionContentDeleteHttp, SupervisionContentFinishHttp } from '@/apps/erm/budget/hooks/supervisionApi.jsx'
import BaseInfo from '@/apps/erm/budget/commponent/supervision/baseInfo.vue'
import { SupervisionContentInfoHttp } from '@/apps/erm/budget/hooks/supervisionApi.jsx'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  supervisionId: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'info'
  },
  conditions: {
    type: Object,
    default: () => { }
  }
})
const emits = defineEmits(['refreshList'])
let replyList = ref(void 0)
let title = ref(void 0)

console.log(props)
const currentId = ref()

const supervisionListIds = ref([])
const baseInfoRef = ref()

const showLeft = ref(true)
const showRight = ref(true)
const total = ref()
const pageNo = ref()
const last = ref()
const first = ref()
let visible = computed(() => props.modelValue)


const close = () => {
  emits('refreshList')
}
const getSupervisionContentInfo = async (id) => {
  let res = await SupervisionContentInfoHttp({ supervisionId: id })
  replyList.value = res.supervisionReplyList
  title.value = res.supervisionContext

}
watch(() => props.supervisionId, async (newValue) => {
  currentId.value = newValue
  if (newValue) {
    if (props.type === 'commentList') {
      await getSupervisionContentInfo(newValue)
    } else {
      console.log( 9999 )
      init()
    }
  }
}, { immediate: true })

const getForLId = () => {
  if (pageNo.value === 1) {
    first.value = supervisionListIds.value[0].id
  }
  if (props.conditions.pageSize * pageNo.value >= total.value) {
    last.value = supervisionListIds.value[supervisionListIds.value.length - 1].id
  }
  if (currentId.value === last.value) {
    showRight.value = false
  } else {
    showRight.value = true
  }
  if (currentId.value === first.value) {
    showLeft.value = false
  } else {
    showLeft.value = true
  }
}
const init = async () => {
  let resData = await querySupervisionContentListHttp(props.conditions)
  supervisionListIds.value = resData.list
  total.value = resData.total
  pageNo.value = props.conditions.pageNo
  getForLId()
}

const loadData = async (num = 0) => {
  pageNo.value = props.conditions.pageNo + num
  let queryObj = Object.assign(props.conditions, { pageNo: props.conditions.pageNo + num })
  let resData = await querySupervisionContentListHttp(queryObj)
  supervisionListIds.value = resData.list
  getForLId()

}
const click = async (num) => {
  let cIdx = supervisionListIds.value.findIndex(item => item.id === currentId.value)
  cIdx = cIdx + num
  if (props.conditions.pageSize * pageNo.value < total.value && num > 0 && cIdx>= props.conditions.pageSize-1) {
    pageNo.value++
    await loadData(num)
    cIdx = 0
  }
  if (pageNo.value!== 1 && num < 0 && cIdx<= 0) {
    pageNo.value--
    await loadData(num)
    cIdx = props.conditions.pageSize-1
  }
  currentId.value = supervisionListIds.value[cIdx].id
  getForLId()
  await getSupervisionContentInfo(supervisionListIds.value[cIdx].id)
  baseInfoRef.value.reload(currentId.value)
}

</script>

<style
  lang='scss'
  scoped
>
.title {
  font-size: 18px;
  color: #007fff;
  overflow-wrap: break-word;
}

.dialog_wrapper {
  position: relative;

  .left {
    position: fixed;
    top: 50vh;
    left: 10vw;
    height: 50px;
    width: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    background-color: #3031338c;
    z-index: 9999999;

  }

  .right {
    position: fixed;
    top: 50vh;
    right: 10vw;
    display: flex;
    height: 50px;
    width: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #3031338c;
    font-size: 28px;
    z-index: 9999999;

  }
}

:deep(.el-divider--horizontal) {
  margin: 10px 0 !important;
}
</style>
