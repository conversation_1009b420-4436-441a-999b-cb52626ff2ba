<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-04 18:50:43
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-08 15:24:26
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/budget/commponent/financeDialog/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <FuniDialog v-model="show" ref="dialog" size="large" :title="rowParams?.title">
      <div
        class="pptDialogBody"
        @click="pptDialogBodyClick"
        :style="{
          padding: isFullscreen ? '40px' : '0'
        }"
      >
        <SimulatePPT :sequence="sequence" :rowParams="rowParams" @changeSequence="changeSequence"></SimulatePPT>
      </div>
      <template #footer>
        <div class="footerBox">
          <div class="footerBox_image">
            <div v-for="(item, index) in image" :class="{ currentIndex: index === sequence }" @click="sequence = index">
              <img :src="item" alt="" />
            </div>
          </div>
          <div class="pptFullIcon">
            <funi-icon @click="toggleFullscreen" icon="icon-park-outline:ppt"></funi-icon>
          </div>
        </div>
      </template>
    </FuniDialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, onMounted, provide } from 'vue';
import screenfull from 'screenfull';
import SimulatePPT from '@/apps/erm/component/simulatePPT/index.vue';

const dialog = ref();
const show = ref(false);
const image = ref([]);
const sequence = ref(0);
const isFullscreen = ref(false);
const rowParams = ref();
onMounted(() => {
  window.onresize = () => {
    isFullscreen.value = screenfull.isFullscreen;
  };
});
provide('isFullscreen', isFullscreen);
provide('sequence', sequence);

const showPPT = row => {
  sequence.value = 0;
  show.value = true;
  rowParams.value = row;
  image.value = [];
  for (let i = 0; i < 8; i++) {
    image.value.push(new URL(`../../../component/simulatePPT/image/ppt${i + 1}.jpg`, import.meta.url).href);
  }
};

const toggleFullscreen = () => {
  let ele = document.querySelector('.pptDialogBody');
  screenfull.request(ele);
};

const changeSequence = type => {
  let s = sequence.value;
  let i = image.value.length - 1;
  if (type == 'prev') {
    sequence.value = s - 1 < 0 ? 0 : s - 1;
  } else {
    sequence.value = s + 1 > i ? i : s + 1;
  }
};

const pptDialogBodyClick = e => {
  if (isFullscreen.value) {
    changeSequence('next');
  }
};
defineExpose({ showPPT });
</script>
<style scoped>
.pptDialogBody {
  height: 70vh;
  background-color: #fff;
  div {
    overflow: auto;
  }
}

.footerBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}

.footerBox_image {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 600px;
  height: 100%;
  gap: 3px;
  div {
    width: 80px;
    height: 55px;
    overflow: hidden;
    position: relative;
    border: 1px solid rgb(206 206 206);
    border-radius: 4px;
    cursor: pointer;
  }
  div::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: #0000008c;
  }
  div:hover::after {
    display: none;
  }
}
img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(1.8px);
}

.currentIndex::after {
  display: none;
}

.pptFullIcon {
  font-size: 24px;
  transition: all 0.2s;
  cursor: pointer;
}
.pptFullIcon:hover {
  transform: scale(1.2);
  color: var(--el-color-primary);
}
</style>
