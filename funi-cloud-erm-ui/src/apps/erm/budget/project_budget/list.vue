<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
  </div>
</template>

<script setup lang="jsx">
import { userProjectColumns, useBtnsConfig } from '@/apps/erm/budget/hooks/projectBudget.jsx';
import { queryBudgetPlanListHttp, deleteBudgetPlanHttp, getBudgetPlanInfoHttp } from '@/apps/erm/budget/hooks/api.js';
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { usePermissionStore } from '@/stores/usePermissionStore';
const permissions = usePermissionStore().permissionsInCurrentPage || [];

const router = useRouter();
const rowData = ref(void 0);
const listPage = ref(null);
const dialogVisible = ref(false);
// 加载数据
const lodaData = async (pages, params) => {
  listPage.value.activeCurd.resetCurrentRow();
  const data = await queryBudgetPlanListHttp({
    ...pages,
    ...params
  });
  rowData.value = void 0;
  return data;
};

// 解决警告
onMounted(() => {});
// 查看详情
const seeDetails = row => {
  let auth = permissions.some(item => item === 'ERM_PROJECTPUDGET_EDIT');
  router.push({
    name: 'ermProjectBudgetInfo',
    query: {
      title: row.planName,
      bizName: '详情',
      type: 'info',
      tab: ['项目预算', row.planName, '详情'].join('-'),
      id: row?.id,
      year: row.planYear,
      auth: auth
    }
  });
};

// 新建预算项目
const addFunc = () => {
  router.push({
    name: 'ermProjectPudgetAdd',
    query: {
      title: '新建项目预算',
      bizName: '新建',
      type: 'add',
      tab: '项目预算-新建'
    }
  });
};

// 删除选中数据
const delFunc = async () => {
  await deleteBudgetPlanHttp({ budgetPlanId: rowData.value?.id });
  listPage.value.reload({ resetPage: false });
  rowData.value = void 0;
};

// 修改选中数据
const editFunc = () => {
  router.push({
    name: 'ermProjectPudgetAdd',
    query: {
      title: rowData.value?.planName,
      bizName: '编辑',
      type: 'edit',
      tab: ['项目预算', rowData.value?.planName, '编辑'].join('-'),
      id: rowData.value.id,
      planYear: rowData.value?.planYear
    }
  });
};

// 表单配置
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        lodaData: lodaData,
        btns: useBtnsConfig(addFunc, editFunc, delFunc, !rowData.value),
        checkOnRowClick: true,
        reloadOnActive: true,
        fixedButtons: true,
        columns: userProjectColumns({ seeDetails }),
        on: {
          rowClick: selection => {
            rowData.value = selection.row;
          }
        }
      }
    }
  ];
});
</script>
