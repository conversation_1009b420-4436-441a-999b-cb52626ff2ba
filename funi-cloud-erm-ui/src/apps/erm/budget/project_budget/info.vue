<template>
  <div>
    <funi-detail :steps="steps" :bizName="bizName" :detailHeadOption="detailHeadOption || {}" />
  </div>
</template>

<script lang="jsx" setup>
import BaseInfo from './../commponent/project_budget/baseInfo.vue';
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';

const showHead = ref(false);
const route = useRoute();
const cid = ref(route.query?.id);
const bizName = ref(route.query?.bizName);
let auth = route.query.auth;
const detailHeadOption = computed(() => {
  return {
    title: route.query.title,
    hideStatusBar: true
  };
});
const listData = ref([]);

const steps = computed(() => {
  return [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        isEdit: false,
        id: cid.value,
        listData: [],
        planYear: route.query.year,
        auth
      },
      on: {
        updateInfo: e => {
          listData.value = e;
        }
      }
    }
  ];
});
</script>
