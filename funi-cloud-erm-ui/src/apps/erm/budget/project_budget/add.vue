<template>
  <div>
    <div>
      <funi-detail
        :bizName="bizName"
        :auditButtons="buttons"
        :steps="steps"
        :detailHeadOption="detailHeadOption || {}"
      />
      <SubmitSuccess ref="submitSuccess" />
    </div>
  </div>
</template>

<script lang="jsx" setup>
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from './../commponent/project_budget/baseInfo.vue';

const route = useRoute();
const type = ref(route.query.type);
const cid = ref(route.query?.id);
const bizName = ref(route.query.bizName);
const detailHeadOption = computed(() => {
  return {
    title: route.query.title,
    hideStatusBar: true
  };
});

const steps = computed(() => {
  return [
    {
      title: '基本信息',
      // preservable: true,
      type: BaseInfo,
      props: {
        id: cid.value,
        isEdit: !['audit', 'info'].includes(route.query.type),
        planYear: route.query.planYear
      },
      on: {
        updateID: v => {
          cid.value = v;
        }
      }
    }
  ];
});
</script>
