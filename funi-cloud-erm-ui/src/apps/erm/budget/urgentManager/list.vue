<template>
  <div>
    <funi-list-page-v2 ref="pageList" :cardTab="useCardTab" />
  </div>
</template>
<script lang="jsx" setup>
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useColumns, useBtnsConfig } from '@/apps/erm/budget/hooks/urgentManager.jsx';
import {
  queryReminderManagementListHttp,
  reminderManagementDeleteHttp
} from '@/apps/erm/budget/hooks/urgentManagerApi.jsx';

const router = useRouter();
const pageList = ref(null);
let selectData = ref(void 0);
let conditions = ref(void 0);

const lodaData = async (params, queryParams) => {
  conditions.value = { ...params, ...queryParams };
  selectData.value = void 0;
  pageList.value.activeCurd.resetCurrentRow();
  let resData = await queryReminderManagementListHttp({ ...params, ...queryParams });
  return resData;
};

//查看详情
const seeDetails = row => {
  // 经营性项目
  if (row.dicReminderTypeCode == '1') {
    router.push({
      name: 'erm_projectManage_two',
      query: {
        title: row.reminderTask || '项目管理',
        bizName: '详情',
        type: 'info',
        id: row.taskId,
        projectSn: row.projectSn,
        tab: ['项目管理', row.reminderTask, '详情'].join('-')
      }
    });
  }
  // 收款合同
  if (row.dicReminderTypeCode == '2') {
    router.push({
      name: 'ermContractCollectionInfo',
      query: {
        title: row.reminderTask || '收款合同',
        bizName: '详情',
        type: 'info',
        id: row.taskId,
        tab: ['收款合同', row.reminderTask || '', '详情'].join('-')
      }
    });
  }
  //督办件
  if (row.dicReminderTypeCode == '3') {
    router.push({
      name: 'ermSupervisionInfo',
      query: {
        bizName: '详情',
        title: '督办件管理-详情',
        type: 'audit',
        id: row.taskSn,
        tab: ['督办件管理', '详情'].join('-'),
        businessId: row?.businessId
      }
    });
  }
};
// const refreshList = () => {
//   pageList.value.reload({ resetPage: false });
// };

//删除
const delFunc = async () => {
  await reminderManagementDeleteHttp({ reminderIdList: [...selectData.value.map(item => item.id)] });
  pageList.value.reload({ resetPage: false });
};

// 表单配置
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        lodaData: lodaData,
        btns: useBtnsConfig({ delFunc, isEnable: !selectData.value || selectData.value.length == 0 }),
        checkOnRowClick: true,
        reloadOnActive: true,
        fixedButtons: true,
        columns: useColumns(seeDetails),
        on: {
          selectionChange: selection => {
            console.log(selection);
            selectData.value = selection;
          }
        }
      }
    }
  ];
});
</script>
