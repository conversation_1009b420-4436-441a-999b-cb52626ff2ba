// 金额验证
const validateMoney = (rule, value, callback) => {
  if (value === '' || value === null || value === undefined) {
    callback(new Error('必填'));
  }
  // else if (!/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(value)) {
  //   callback(new Error('请输入大于0且最多保留两位小数的数字'));
  // }
  else {
    callback();
  }
};

//联系电话验证
const validPhone = (rule, value, callback) => {
  let reg = /^((0\d{2,3}(-)?\d{7,8})|(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8})$/;
  let arr = [1, 8, 9, 8, 8, 8, 8, 8, 8, 8, 8, '/', 0, 2, 8, '-', 8, 8, 8, 8, 8, 8, 8];
  if (rule.required) {
    if (!reg.test(value)) {
      callback(new Error('请输入正确的手机号/座机号，如' + arr.join('')));
    } else {
      callback();
    }
  } else {
    if (!value) return callback();
    setTimeout(() => {
      if (!reg.test(value)) {
        callback(new Error('请输入正确的手机号/座机号，如' + arr.join('')));
      } else {
        callback();
      }
    }, 100);
  }
};

export { validateMoney, validPhone };
