/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-09-27 20:51:35
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-16 11:13:43
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/config/style.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const style = () => {
  return `
    .funi-erm-change{
        color:rgb(255, 153, 0)
    }
    .funi-form-item__extra:has(.funi-erm-change){
        position:relative
    }
    .funi-form-item__extra:has(.funi-erm-change):before{
        content: '变更前:';
        display: inline-block;
        width: 70px;
        position: absolute;
        left: -70px;
        text-align: right;
        padding: 0 12px 0 0;
        box-sizing: border-box;
        color:rgb(255, 153, 0)
    }
    .el-form-item__content .el-button.is-link,
    .funi-form-item__content .el-button.is-link{
        text-align: left;
        justify-content: start;
        white-space:wrap
    }
    .funi-form .el-input-number .el-input__inner {
        text-align: left !important;
      }
    `;
};

export const formErrorShow = () => {
  return `
    .el-form-item{
        margin-bottom: var(--item-mb,14px)
    }
    .el-form-item.is-error:has(.erm-from-item__mb26){
        margin-bottom: 26px
    }
    `;
};
