/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-16 09:52:36
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-31 17:31:58
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/config/config.jsx
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { ElNotification } from 'element-plus';
const ermGlobalApi = {
  // dictList: '/csccs/dicList/findDicByTypeCode',
  dictList: '/csccs/dicTypeList/findDicByTypeCodeAndClientId',
  findRootRegion: '/csccs/region/findRootRegion',
  findSonRegionTreeNoRestriction: '/csccs/region/findSonRegionTreeNoRestriction',
  findCurrentUserDept: '/erm/employee/findCurrentUserDept',
  queryEmployeeList: '/erm/employeeList/queryEmployeeList',
  findCurrentUserCompany: 'erm/employee/findCurrentUserCompany',
  findByEmployee: '/erm/employee/findByEmployee',
  startBusiness: '/bpmn/businessManage/startBusiness',
  executeBusiness: '/bpmn/businessManage/executeBusiness',
  queryCompanyList: '/csccs/companyList/queryCompanyList',
  getOrgTree: 'csccs/dept/getOrgTree',
  // queryValidCollectionContractList: '/erm/collectionContractList/queryValidCollectionContractList',
  queryValidCollectionContractList: '/erm/collectionContractList/queryValidContractListByUserDeptInfo',
  queryValidContractListToPayContract: '/erm/collectionContractList/queryValidContractListToPayContract',
  queryCustomerList: '/erm/customerList/queryCustomerList',
  chooseBudgeProject: '/erm/chooseBudgetProject/page',
  queryProjectListByName: 'erm/projectManagementList/queryProjectListByName',
  delBusiness: '/csccs/businessControl/delBusiness',
  queryIncomeConfirmInfoByServiceId: '/erm/incomeConfirmInfoList/queryIncomeConfirmInfoByServiceId',
  queryCollectionServiceListByContractId: '/erm/collectionServiceList/queryCollectionServiceListByContractId',
  queryBusinessUserRecordInfo: '/erm/clientDataInfo/queryBusinessUserRecordInfoList',
  queryValidPurchaseList: '/erm/purchaseManagementList/queryValidPurchaseList'
};
//区域
const findRootRegionHttp = params => {
  return $http.post(ermGlobalApi.findRootRegion, params);
};
//区域
const findSonRegionTreeNoRestrictionHttp = params => {
  return $http.post(ermGlobalApi.findSonRegionTreeNoRestriction, params);
};
// 获取当前登陆账号的公司部门信息
const findCurrentUserCompanyHttp = params => {
  return $http.fetch(ermGlobalApi.findCurrentUserCompany, params);
};

//获取当前登陆账号的部门信息
const findCurrentUserDeptHttp = params => {
  return $http.fetch(ermGlobalApi.findCurrentUserDept, params);
};

//获取公司列表
const queryCompanyListHttp = params => {
  return $http.fetch(ermGlobalApi.queryCompanyList, params);
};

// 启动工作流
const startBusinessHttp = params => {
  return $http.post(ermGlobalApi.startBusiness, params);
};
// 执行工作流
const executeBusinessHttp = params => {
  return $http.post(ermGlobalApi.executeBusiness, params);
};
// 从csccs获取公司组织信息
const getOrgTreeHttp = params => {
  return $http.post(ermGlobalApi.getOrgTree, params);
};
// 工作流删除要件
const delBusinessHttp = params => {
  return $http.post(ermGlobalApi.delBusiness, params);
};

const queryIncomeConfirmInfoByServiceIdHttp = params => {
  return $http.fetch(ermGlobalApi.queryIncomeConfirmInfoByServiceId, params);
};

const queryCollectionServiceListByContractIdHttp = params => {
  return $http.fetch(ermGlobalApi.queryCollectionServiceListByContractId, params);
};

// 字典编码
const dicCode = {
  //项目类型
  dicProjectTypeCode: 'project_type',
  //项目分类
  dicProjectCategoryCode: 'project_category',
  //客户维度
  dicCustomerDimeCode: 'customer_dime',
  //建设模式
  dicConstructModelCode: 'construct_model',
  //项目阶段
  dicProjectStageCode: 'project_stage',
  //项目状态
  dicProjectStatusCode: 'project_status',
  //行业领域
  dicIndustryFieldCode: 'industry_field',
  //所属业务板块
  dicBusinessSegCode: 'business_seg',
  //项目周期
  dicProjectCycleCode: 'project_cycle',
  //建设规模
  projectScaleCode: 'project_scale',
  //项目实际角色
  dicActualRoleCode: 'actual_role',
  //项目来源
  dicProjectSourceCode: 'project_source',
  //建设方式
  dicConstructModeCode: 'construct_mode',
  //协议类型
  dicProtocolTypeCode: 'protocol_type',
  //项目属性
  dicProjectPropertyCode: 'project_property',
  //用户区域
  dicUserAreaCode: 'user_area',
  //实施方式
  dicImplModeCode: 'impl_mode',
  //经费来源
  dicExpendSourceCode: 'expend_source',
  //采购方式
  dicPurchModeCode: 'purch_mode',
  //成功几率
  dicSuccessOddsCode: 'success_odds',
  //性别
  genderCode: 'gender',
  //客商类型 客户
  customerTypeCode: 'customer_type',
  //客商类型 供应商
  customerSignCode: 'customer_sign',
  //重要性
  importanceType: 'importance_type',
  serviceType: 'service_type',
  onJobStatusType: 'on_job_status',
  educationalType: 'educational',
  //职类
  jobCategoryType: 'job_category',
  //职位
  positionType: 'position',
  //职务
  dutyType: 'duty',
  //职务级别
  dutyLevelType: 'duty_level',
  //涉及职务待遇
  dutyTreatmentType: 'duty_treatment',

  //收入小类
  incomeSubclassType: 'income_subclass',
  //收入大类
  incomeCategoryType: 'income_category',
  //计提类型
  accrualType: 'accrual_type',
  //非合同计提类型
  dicUnaccrualTypeCode: 'unaccrual_type',
  settlement_type: 'settlement_type', //结算方式
  coll_type: 'coll_type', //收款方式
  format_type: 'format_type', // 格式合同类型
  seal_type: 'seal_type', // 用印类型
  service_source: 'service_source', //服务来源
  service_content: 'service_content', //服务内容
  sign_type: 'sign_type', //签约类型
  confirm_mode: 'confirm_mode', //确收方式
  confirm_mode: 'confirm_mode', //确收方式
  contract_type: 'contract_type', // 合同分类
  pay_type: 'pay_type', //
  fapiao_type: 'fapiao_type', //开票类型,
  supervision_type_code: 'supervision_type',
  recruitmentTypeCode: 'recruitment_type',
  recruitmentNatureCode: 'recruitment_nature',
  implementTypeCode: 'implement_type',
  confirmModeCode: 'confirm_mode',
  budgetTag: 'budget_tag',
  changeType: 'change_type',
  controlPricesTag: 'control_prices_tag', //控制价标签
  reviewDrawMode: 'review_draw_mode'
};

// 金额验证
const validateMoney = (rule, value, callback) => {
  if (value === '' || value === null || value === undefined) {
    callback(new Error('必填'));
  } else if (value == 0 || !/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(value)) {
    callback(new Error('请输入大于0且最多保留两位小数的数字'));
  } else {
    callback();
  }
};
const negative_validateMoney = (rule, value, callback) => {
  // console.log('negative_validateMoney', value)
  if (value === '' || value === null || value === undefined) {
    callback(new Error('必填'));
  } else if (value == 0) {
    callback();
  } else if (!/^-(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(value)) {
    callback(new Error(`请输入小于等于0保留两位小数的数字`));
  } else {
    callback();
  }
};

/**
 * @description Add||info ===>  baseInfo页面默认props
 * **/
const baseInfoProps = {
  isEdit: {
    type: Boolean,
    default: true
  },
  id: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  dicBusinessTypeCode: String,
  businessId: String,
  bizName: String,
  lastId: String
};

const moreBtnRender = (btnList, operationBtn) => {
  // console.log(btnList, operationBtn);
  if (btnList.length < 4) {
    return btnList?.map(item => operationBtn[item]);
  } else {
    let a = btnList.slice(0, 2),
      b = btnList.slice(2);
    return [
      ...a.map(item => operationBtn[item]),
      <el-popover placement="bottom">
        {{
          default: (
            <div style="display: flex;flex-direction: column;justify-content: space-around;align-items: center;gap: 12px">
              {...b.map(item => operationBtn[item])}
            </div>
          ),
          reference: <Hyperlink text={'更多'}></Hyperlink>
        }}
      </el-popover>
    ];
  }
};

// 导出表格
const expotrFunction = async ({ url, params, method = 'post', FileName, FileType = 'xls' }) => {
  let resData = await $http[method](url, params, {
    responseType: 'blob'
  });
  if (resData.type == 'application/json') {
    const reader = new FileReader();
    reader.onload = function () {
      const result = JSON.parse(reader.result); //此处的msg就是后端返回的msg内容
      if (result.success === false) {
        ElNotification({ title: result.message, type: 'error' });
      }
    };
    reader.readAsText(resData);
  } else {
    let downloadElement = document.createElement('a');
    let href = window.URL.createObjectURL(resData); //创建下载的链接
    downloadElement.href = href;
    downloadElement.download = FileName + '.' + FileType; //下载后文件名
    document.body.appendChild(downloadElement);
    downloadElement.click(); //点击下载
    document.body.removeChild(downloadElement); //下载完成移除元素
  }
};

export {
  ermGlobalApi,
  dicCode,
  baseInfoProps,
  findRootRegionHttp,
  findSonRegionTreeNoRestrictionHttp,
  findCurrentUserCompanyHttp,
  findCurrentUserDeptHttp,
  startBusinessHttp,
  validateMoney,
  executeBusinessHttp,
  moreBtnRender,
  expotrFunction,
  delBusinessHttp,
  queryIncomeConfirmInfoByServiceIdHttp,
  queryCollectionServiceListByContractIdHttp,
  negative_validateMoney
};
