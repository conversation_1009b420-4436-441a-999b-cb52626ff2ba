/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-18 11:05:08
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-20 13:59:33
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/config/business.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */

import { startBusinessHttp, executeBusinessHttp, delBusinessHttp } from './config.jsx';
import { useAppStore } from '@/stores/useAppStore';

import Vrouter from '@/router';
import { usePermissionStore } from '@/stores/usePermissionStore.js';
const appStore = useAppStore();
const router = Vrouter;

/**
 * @description 开启工作流
 * **/
export const openBusiness = async (businessConfigCode, businessJson, applicationReason, businessName) => {
  const sysInfo = appStore.system;
  return await startBusinessHttp({
    businessConfigCode,
    sysId: sysInfo.id,
    businessJson,
    applicationReason,
    businessName
  });
};

/**
 * @description 提交工作流
 * **/

export const submitBusiness = async (businessConfigCode, businessId, businessExecutionType, businessName) => {
  const sysInfo = appStore.system;
  return await executeBusinessHttp({
    businessConfigCode,
    sysId: sysInfo.id,
    businessId,
    businessExecutionType,
    businessJson: {},
    businessName
  });
};

/**
 * @description 删除工作流
 * **/

export const deleteBusiness = async businessId => {
  const sysInfo = appStore.system;
  return await delBusinessHttp({
    sysId: sysInfo.id,
    businessId
  });
};



// 审核完成时调用
export const auditEndFunc = async (func) => {
  const permissionStore = usePermissionStore();
  const menuId = router.currentRoute.value.meta.menus[0];
  const menu = permissionStore.menus.filter(item => item.id === menuId);
  func && func()
  if (menu && menu.length) {
    const name = menu[0]?.defaultPage?.route?.name;
    router.push({
      name
    });
  }
}