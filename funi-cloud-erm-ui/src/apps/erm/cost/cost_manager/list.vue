<template>
  <div>
    <funi-list-page-v2 ref="pageList" :cardTab="useCardTab" />
  </div>
</template>

<script lang='jsx' setup>
import { useColumns, useBtnsConfig } from '@/apps/erm/cost/hooks/cost_manager/useCostManager'
import { queryCostApportionInfoListHttp, apiUrl, setYearEndDay } from '@/apps/erm/cost/hooks/cost_manager/costManagerApi.jsx'
import { computed, ref } from 'vue';
import { expotrFunction } from '@/apps/erm/config/config.jsx'
import { useRouter } from 'vue-router'
const router = useRouter()
const pageList = ref(null)
const queryData = ref(void 0)
const loadData = async (params, queryParams) => {
  queryData.value = queryParams
  let resData = await queryCostApportionInfoListHttp({ ...params, ...queryParams })
  return resData
}
const seeDetails = (row) => {
  router.push({
    name: 'erm_cost_manager_two',
    query: {
      title: row.costSn || '成本管理',
      bizName: '详情',
      type: 'info',
      id: row.id,
      tab: ['成本管理', row.costSn, '详情'].join('-')
    }
  })
}

//导出
const exportFn = () => {
  expotrFunction({ url: apiUrl.queryCostInfoListExport, params: queryData.value, FileName: '成本', })
  // let res = await queryCostInfoListExportHttp()
}
//导出
const setYearEnd = async () => {
  let res = await setYearEndDay()
  pageList.value.reload({ resetPage: false });
}

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig(exportFn, setYearEnd),
        lodaData: loadData,
        fixedButtons: true,
        columns: useColumns(seeDetails),
        reloadOnActive: true,
      }
    }
  ]
})




</script>
<style lang='scss' scoped></style>