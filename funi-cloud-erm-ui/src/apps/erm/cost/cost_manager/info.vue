
<template>
  <div>
    <funi-detail 
      :bizName="bizName"
      :auditButtons="buttons"
      :steps="steps"
      :showWorkflow="true"
      :detailHeadOption="detailHeadOption || {}" />
  </div>
</template>


<script lang='jsx' setup>
import BaseInfo from '@/apps/erm/cost/component/cost_manager/baseInfo.vue'
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router'
const route = useRoute()

let showHead = ref(false)
let bizName = '详情'
const id = ref(route.query.id)
const detailHeadOption = computed(() => {
  return {
    title: route.query.title,
    hideStatusBar: true,
    // no: infoData.value?.businessId,
    // status: infoData.value?.businessNode
  }
})
let steps = computed(() => {
  return [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value
      },
    },
  ]
})

</script>
<style lang='scss' scoped></style>