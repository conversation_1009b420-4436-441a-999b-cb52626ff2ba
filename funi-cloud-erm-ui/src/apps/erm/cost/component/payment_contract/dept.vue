<template>
  <el-tree-select
    v-model="checkData"
    :data="deptList"
    :render-after-expand="false"
    node-key="companyId"
    check-on-click-node
    style="width: 240px"
    @currentChange="currentChange"
    :props="{
      value: 'id',
      label: 'deptName',
      children: 'childList'
    }"
  />
</template>

<script lang="jsx" setup>
import { ref, watchEffect } from 'vue';
import {
  queryDeptTreeHttp,
  queryRequestDeptTreeHttp
} from '@/apps/erm/cost/hooks/payment_contract/paymentContractApi.jsx';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  companyId: {
    type: String,
    default: ''
  },
  deptId: {
    type: String,
    default: ''
  }
});

let checkData = ref();
let deptList = ref();
const emits = defineEmits(['update:modelValue', 'deptChange']);
const getList = async () => {
  try {
    let res;
    if (props.companyId) {
      res = await queryDeptTreeHttp({ companyId: props.companyId });
    } else if (props.deptId) {
      res = await queryRequestDeptTreeHttp({ deptId: props.deptId });
    }
    deptList.value = res.list || res.childList;

    console.log(deptList.value, '1231231');
  } catch (error) {
    console.log(error);
  }
};
watchEffect(async () => {
  if (props.companyId || props.deptId) {
    getList();
  }
});
watchEffect(() => {
  checkData.value = props.modelValue;
});

const currentChange = e => {
  emits('deptChange', e);
  emits('update:modelValue', e.id);
};
</script>
