<template>
  <div>
    <FuniCurd :isEdit="isEdit" :listData="listData" :columns="columns" />

  </div>
</template>

<script lang='jsx' setup>
// import { useColumns } from '@/apps/erm/cost/hooks/payment_contract/usePaymentContractInfo'
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: true
  },
  listData: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => []
  }
})

// const columns = useColumns()

</script>
<style lang='scss' scoped></style>