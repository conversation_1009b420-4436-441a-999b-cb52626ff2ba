<template>
  <div class="base_info">
    <GroupTitle title="基本信息" />
    <funi-form :schema="schema" @get-form="e => (formData = e)" :rules="rules" :col="3" />
    <div>
      <GroupTitle title="计提明细">
        <el-button type="primary" v-if="isEdit" @click="addFn">添加</el-button>
      </GroupTitle>
      <funi-curd
        ref="tableRef"
        row-key="uuId"
        :data="tableList"
        :columns="column"
        :pagination="false"
        :max-height="600"
        @rowClick="rowClick"
      >
        <template #projectHead>
          <div class="musterW"><span v-if="isEdit">*</span>项目名称</div>
        </template>
        <template #customerHead>
          <div class="musterW"><span v-if="isEdit">*</span>供应商名称</div>
        </template>
        <template #paymentNatureHead>
          <div class="musterW"><span v-if="isEdit">*</span>款项性质</div>
        </template>
        <template #contractAmountHead>
          <div class="musterW"><span v-if="isEdit">*</span>合同金额</div>
        </template>
        <template #accrualIncludeTaxAmountHead>
          <div class="musterW"><span v-if="isEdit">*</span>计提含税金额</div>
        </template>
        <template #taxRateHead>
          <div class="musterW"><span v-if="isEdit">*</span>税率</div>
        </template>
      </funi-curd>
    </div>
    <div v-if="!['编辑', '新建'].includes(props.bizName) && fileParams.params.businessId">
      <GroupTitle title="要件信息" />
      <funi-file-table :params="fileParams.params" onlyShow />
    </div>
  </div>
</template>

<script lang="jsx" setup>
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { useScheme, useRules, useColumns } from '@/apps/erm/cost/hooks/payment_contract/usePaymentContractInfo';
import { computed, onMounted, reactive, ref, nextTick } from 'vue';
import { ElNotification } from 'element-plus';
import { openBusiness } from '@/apps/erm/config/business.js';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import {
  accrualInfoHttp,
  accrualNewHttp,
  queryCurrentUserInfoHttp
} from '@/apps/erm/cost/hooks/payment_contract/paymentContractApi.jsx';

const props = defineProps({
  ...baseInfoProps,
  bizName: {
    type: String,
    default: '新建'
  },
  isCopy: {
    type: Boolean,
    default: false
  }
});

console.log(props, 'props');
const getCurrentUserInfo = async () => {
  let res = await queryCurrentUserInfoHttp();
  formData.value.setValues({ companyId: res.companyId, deptId: res.deptId });
  deptId.value = res.deptId;
};
const emits = defineEmits(['updateInfo', 'updateID']);
const formData = ref();
const deptId = ref();
const tableList = ref([
  {
    projectName: '',
    contractName: '',
    customerId: '',
    paymentNature: '',
    contractAmount: '',
    accrualIncludeTaxAmount: '',
    taxRate: '',
    accrualExcludeTaxAmount: '',
    remark: '',
    uuId: $utils.guid()
  }
]);
const listRef = {};
const fileParams = reactive({
  params: {
    businessId: ''
  }
});
const iptChange = (index, name, e) => {
  tableList.value[index][name] = e;
  if (name == 'accrualIncludeTaxAmount') {
    let total = 0;
    tableList.value.forEach(item => {
      total = total + (item.accrualIncludeTaxAmount || 0);
    });
    formData.value.setValues({ totalIncludeTaxAmount: total.toFixed(2) });
  }

  if (name == 'accrualExcludeTaxAmount') {
    console.log(tableList.value, ' tableList.value tableList.value');
    let total = 0;
    tableList.value.forEach(item => {
      total = total + (item.accrualExcludeTaxAmount || 0);
    });

    formData.value.setValues({ totalExcludeTaxAmount: total.toFixed(2) });
  }
};
const addFn = () => {
  tableList.value.push({
    projectName: '',
    contractName: '',
    customerId: '',
    paymentNature: '',
    contractAmount: '',
    accrualIncludeTaxAmount: '',
    taxRate: '',
    accrualExcludeTaxAmount: '',
    remark: '',
    uuId: $utils.guid()
  });
};
const delDicItem = (row, index) => {
  if (tableList.value.length == 1) {
    ElNotification({
      type: 'warning',
      title: '请至少保留一条明细数据！'
    });
    return false;
  } else {
    for (let key in listRef) {
      if (key.indexOf(row.uuId) > -1) {
        Reflect.deleteProperty(listRef, key);
      }
    }
    tableList.value.splice(index, 1);
  }
};
const verificationTableForm = () => {
  let v_l = [];
  for (let key in listRef) {
    let v = listRef[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};

const column = computed(() => {
  return useColumns({ iptChange, delDicItem, isEdit: props.isEdit, listRef, deptId });
});
const deptChange = e => {
  deptId.value = e.id;
  tableList.value = [
    {
      projectName: '',
      contractName: '',
      customerId: '',
      paymentNature: '',
      contractAmount: '',
      accrualIncludeTaxAmount: '',
      taxRate: '',
      accrualExcludeTaxAmount: '',
      remark: '',
      uuId: $utils.guid()
    }
  ];
  formData.value.setValues({
    totalIncludeTaxAmount: void 0,
    totalExcludeTaxAmount: void 0,
    requestDeptId: void 0,
    requestDeptName: void 0
  });
};

const requestDeptChange = e => {};

const schema = computed(() => {
  return useScheme({
    isEdit: props.isEdit,
    deptChange
  });
});
const rules = computed(() => {
  return useRules({ isEdit: props.isEdit });
});
const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await formData.value.validate();
  if (type == 'ts' || (isValid && verificationTableForm())) {
    let data = formData.value.getValues();
    let obj = {
      id: props.id || '',
      ...data,
      paymentContractAccrualDetailRequestList: tableList.value.map(item => {
        let cloneData = $utils.clone(item, true);
        if (!cloneData.contractId) {
          cloneData['customerName'] = cloneData.customerId;
          Reflect.deleteProperty(cloneData, 'customerId');
        }
        return cloneData;
      }),
      accrualYearDate: data.accrualYearDate ? `${data.accrualYearDate}-01` : void 0
    };
    emits('updateInfo', obj);
    return obj;
  } else {
    return false;
  }
};

const saveData = async type => {
  let res = await getData(type);
  if (!res) return Promise.reject();
  if (!props.id) {
    let resData = await openBusiness('ERM_PAYMENT_CONTRACT_ACCRUAL_ADD', res, void 0, '付款合同计提新增');
    let { id, businessId } = resData.businessData.paymentContractAccrualVo;

    emits('updateID', {
      id,
      businessId: businessId || props.businessId
    });
    emits('updateInfo', resData.businessData.paymentContractAccrualVo);
  } else if (props.id && type == 'ts') {
    await accrualNewHttp({
      ...res,
      id: props.id,
      isSubmit: false
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject();
  if (props.id && !type) {
    await accrualNewHttp({
      ...res,
      id: props.id,
      isSubmit: true
    });
  }
  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  return Promise.resolve({});
};
const getAccrualInfo = async id => {
  let { paymentContractAccrualVo, paymentContractAccrualDetailListVoList } = await accrualInfoHttp({
    paymentContractAccrualId: id
  });
  let obj;
  if (props.isCopy) {
    obj = {
      ...paymentContractAccrualVo
    };

    Reflect.deleteProperty(obj, 'id');
    Reflect.deleteProperty(obj, 'accrualSn');
    Reflect.deleteProperty(obj, 'businessId');
    Reflect.deleteProperty(obj, 'dicDataStatusCode');
    Reflect.deleteProperty(obj, 'dicAccrualStatusName');
    Reflect.deleteProperty(obj, 'dicAccrualStatusCode');
    Reflect.deleteProperty(obj, 'businessId');
    Reflect.deleteProperty(obj, 'businessNode');
    obj.dicAccrualStatusCode = '0';
    obj.dicAccrualStatusName = '草稿';
    formData.value.setValues({
      ...obj,
      accrualYearDate: paymentContractAccrualVo.accrualYearDate.split('-01')[0]
    });
    tableList.value = paymentContractAccrualDetailListVoList.map(item => {
      if (item.customerName && !item.contractId) {
        item.customerId = item.customerName;
      }
      return item;
    });

    deptId.value = paymentContractAccrualVo.deptId;
    emits('updateID', {
      id: '',
      businessId: ''
    });
  } else {
    obj = {
      ...paymentContractAccrualVo
    };
    formData.value.setValues({
      ...obj,
      accrualYearDate: obj.accrualYearDate ? obj.accrualYearDate.replace('-01', '') : void 0
    });
    tableList.value = paymentContractAccrualDetailListVoList.map(item => {
      if (item.customerName && !item.contractId) {
        item.customerId = item.customerName;
      }
      return item;
    });
    deptId.value = obj.deptId;
    fileParams.params.businessId = obj.businessId;
    emits('updateID', { id: obj.id, businessId: obj.businessId });
    emits('updateInfo', obj);
  }
};

const ts = () => {
  return saveData('ts');
};

const nextStep = () => {
  return saveData();
};

defineExpose({
  ts,
  nextStep
});

onMounted(() => {
  if (props.id) {
    getAccrualInfo(props.id);
  } else {
    getCurrentUserInfo();
  }
});
</script>
<style lang="scss" scoped>
.base_info {
  // padding: 20px;
  box-sizing: border-box;
}
.musterW {
  overflow: hidden;
  width: 100%;
  height: 26px;
  padding-left: 5px;
}
.musterW > span {
  color: red;
  display: inline-block;
  /* margin: 3px 3px 0 0; */
  position: relative;
  top: 4px;
  right: 3px;
}
:deep(.el-input__inner) {
  text-align: left;
}
</style>
