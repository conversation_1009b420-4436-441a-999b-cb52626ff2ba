<template>
  <div class="base_info">
    <GroupTitle title="基本信息" />
    <funi-form :schema="scheme" :col="3" @get-form="setForm" />
    <GroupTitle title="公司额外成本" />
    <funi-form :schema="extralScheme" @get-form="setExtralForm" :col="3" />
  </div>
</template>

<script lang="jsx" setup>
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { baseScheme, additionalScheme } from '@/apps/erm/cost/hooks/wages_manager/useWagesManagerInfo.jsx';
import { computed, reactive, onMounted } from 'vue';
import { salaryManagementInfoHttp } from '@/apps/erm/cost/hooks/wages_manager/wagesManagerApi.jsx';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
const props = defineProps({
  ...baseInfoProps
});

const datas = reactive({
  formData: null,
  extralForm: null
});

const setForm = e => {
  datas.formData = e;
};

const setExtralForm = e => {
  datas.extralForm = e;
};

const scheme = computed(() => {
  const { isEdit } = props;
  return baseScheme({
    isEdit
  });
});

const extralScheme = computed(() => {
  const { isEdit } = props;
  return additionalScheme({
    isEdit
  });
});

const getSalaryInfo = async id => {
  let { salaryManagementVo } = await salaryManagementInfoHttp({ salaryManagementId: id });
  datas.formData.setValues({
    ...salaryManagementVo
  });
  datas.extralForm.setValues({
    ...salaryManagementVo
  });
};

onMounted(() => {
  if (props.id) {
    getSalaryInfo(props.id);
  }
});
</script>
<style lang="scss" scoped>
.base_info {
  // padding: 20px;
  box-sizing: border-box;
}
</style>
