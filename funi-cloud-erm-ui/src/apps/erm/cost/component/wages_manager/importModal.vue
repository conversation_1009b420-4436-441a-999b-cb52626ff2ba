<template>
  <FuniDialog
    custom-class="dialogLoading"
    v-bind="$attrs"
    v-model="visible"
    :onConfirm="onConfirm"
    :onCancel="onCancel"
    title="工资导入"
    size="large"
    @close="onCancel"
    :hideFooter="true"
    :append-to-body="true"
  >
    <FuniCurd
      ref="curd"
      row-key="id"
      :lodaData="loadData"
      :columns="columns"
      :searchConfig="useSearchConfig()"
      :useSearchV2="false"
      :isShowSearch="true"
      :loading="loading"
      :pagination="false"
      @search="search"
    >
      <template #header>
        <div class="table-header">
          <div>请选择要导入的数据类型（ ①对所属公司及月份的选择。②.上传相关表数据。③点击“确定覆盖”进行数据覆盖）</div>
          <el-button type="primary" :disabled="!coverStatus" @click="() => coverFn()">确定覆盖</el-button>
        </div>
      </template>
    </FuniCurd>
  </FuniDialog>
</template>

<script lang="jsx" setup>
import { computed, ref, watch } from 'vue';
import {
  obtainCompanyQueryConditionHttp,
  querySalaryImportRecordListHttp,
  importSalaryExcelHttp,
  apiUrl
} from '@/apps/erm/cost/hooks/wages_manager/wagesManagerApi.jsx';
import Upload from '@/apps/erm/component/upload/upload.vue';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
import httpUtil from '@/utils/httpUtil.js';
import { ElMessage, ElMessageBox } from 'element-plus';
const visible = ref(false);
const options = ref([]);
const curd = ref();
const searchParams = ref();
const coverStatus = computed(() => {
  if (curd.value.visibleData.length === 0) return false;
  return curd.value.visibleData.every(item => item.isUpload);
});

const search = params => {
  searchParams.value = params;
  if (Object.keys(params).length === 0) {
    curd.value.searchParams = {};
    curd.value.visibleData = [];
    coverStatus.value = false;
  }
};

const coverFn = async () => {
  ElMessageBox.confirm('确定要重新覆盖吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await importSalaryExcelHttp({
      companyName: searchParams.value.companyName,
      salaryMonth: $utils.Date(searchParams.value.salaryMonth).format('yyyy-MM') + '-01'
    }).then(() => {
      ElMessage({
        message: '覆盖成功!',
        type: 'success'
      });
      reload();
    });
  });
};

const downloadTemplateFile = async (fileCode, fileName) => {
  let resData = await $http.post(
    `${apiUrl.downloadSalaryTemplate}?fileTypeCode=${fileCode}&companyName=${searchParams.value.companyName}`,
    {},
    { responseType: 'blob' }
  );
  var downloadElement = document.createElement('a');
  var href = window.URL.createObjectURL(resData); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = fileName + '.xlsx'; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
};

const downLoadCompleteFileFn = async (fileId, fileName) => {
  let resData = await $http.post(
    `/csfile/storage/read?id=${fileId}`,
    {},
    {
      responseType: 'blob'
    }
  );
  var downloadElement = document.createElement('a');
  var href = window.URL.createObjectURL(resData); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = fileName + '.xlsx'; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
};

const reload = () => {
  curd.value.reload();
};

const columns = computed(() => {
  return [
    {
      label: '类型',
      prop: 'fileName',
      align: 'center',
      render: ({ row, index }) => {
        return <span>{row.fileName}</span>;
      }
    },
    {
      label: '模板',
      prop: 'b',
      align: 'center',
      render: ({ row, index }) => {
        return (
          <el-link type={'primary'} onClick={async () => await downloadTemplateFile(row.dicFileTypeCode, row.fileName)}>
            下载模板
          </el-link>
        );
      }
    },
    {
      label: '操作',
      prop: 'c',
      align: 'center',
      render: ({ row, index }) => {
        return (
          <Upload
            showMessage={true}
            callbackFun={() => reload()}
            appendToBody={true}
            title={`上传${row.fileName}表`}
            url={`${apiUrl.uploadSalaryExcel}?logId=${row.id}`}
            type="success"
            onlyUpload
          >
            上传文件
          </Upload>
        );
      }
    },
    {
      label: '操作结果',
      prop: 'd',
      align: 'left',
      render: ({ row, index }) => {
        return row.isUpload ? (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ display: 'flex', alignItems: 'center', marginRight: '10px' }}>
              <el-icon color={'#00d26a'}>
                <SuccessFilled />
              </el-icon>
              <span style={{ display: 'inlineBlock', width: '200px' }}>{`已上传${row.issue}${row.fileName}数据`}</span>
            </div>
            <el-link
              type={'primary'}
              onClick={() => downLoadCompleteFileFn(row.fileUrl, `${row.issue}${row.fileName}`)}
            >
              查看已上传文件
            </el-link>
          </div>
        ) : (
          '--'
        );
      }
    },
    {
      label: '数据覆盖状态',
      prop: 'coverStatus',
      align: 'center',
      render: ({ row, index }) => {
        return <span>{row.coverStatus ? '已覆盖' : '未覆盖'}</span>;
      }
    }
  ];
});

const loadData = (_params, params) => {
  if (params.companyName && params.salaryMonth) {
    return querySalaryImportRecordListHttp({
      ..._params,
      companyName: params.companyName,
      salaryMonth: $utils.Date(params.salaryMonth).format('yyyy-MM') + '-01'
    });
  }
};
const useSearchConfig = () => {
  let schema = [
    {
      label: '所属公司',
      component: () => {
        return <funi-select options={options.value} filterable={true}></funi-select>;
      },
      prop: 'companyName',
      rules: {
        required: true,
        message: '请选择所属公司'
      },
      props: {
        placeholder: '请选择所属公司'
      },
      colProps: {
        span: 8
      }
    },
    {
      label: '选择月份',
      component: () => {
        return <el-date-picker type="month" placeholder="请选择月份" />;
      },
      prop: 'salaryMonth',
      rules: {
        required: true,
        message: '请选择月份'
      },
      props: {
        placeholder: '请选择月份'
      },
      colProps: {
        span: 8
      }
    }
  ];
  return {
    border: false,
    schema: schema,
    colNumber: 3
  };
};

const onConfirm = () => {};
const onCancel = () => {
  visible.value = false;
};
const getCompanyOption = async () => {
  let res = await obtainCompanyQueryConditionHttp();
  options.value = res.list.map(item => {
    return {
      label: item,
      value: item
    };
  });
};
getCompanyOption();

const show = () => {
  visible.value = true;
};

defineExpose({
  show
});
</script>
<style lang="scss" scoped>
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: -12px;
  margin-bottom: -3px;
}
</style>
