<template>
  <funi-dialog
    custom-class="dialogLoading"
    v-bind="$attrs"
    v-model="visible"
    :onConfirm="onConfirm"
    :onCancel="onCancel"
    :title="title"
    @close="close"
    append-to-body
  >
    <data>
      <funi-form @get-form="setForm" :schema="schema" :col="1" :rules="rules" />
    </data>
    <template #footer>
      <div class="funi-dialog__footer">
        <el-button @click="closeDialog" v-if="sharingId"> 取消 </el-button>
        <el-button type="primary" @click="onConfirm"> 确认 </el-button>
      </div>
    </template>
  </funi-dialog>
</template>

<script lang="jsx" setup>
import { computed, ref, watch } from 'vue';
import { ElDatePicker, ElNotification, ElLoading } from 'element-plus';

import { openBusiness, submitBusiness } from '@/apps/erm/config/business.js';
import { regularSalarySharingHttp } from '@/apps/erm/cost/hooks/wages_manager/wagesManagerApi.jsx';
const formValue = ref();
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: '请输入办理原因'
  }
});

const emits = defineEmits(['update:modelValue', 'confirm']);
const visible = ref(false);
const loading = ref(false);
const schema = computed(() => {
  return [
    {
      label: '请选择分摊计算月份：',
      component: () => (
        <ElDatePicker format="YYYY-MM" placeholder={'请选择分摊计算月份'} valueFormat="x" type={'month'} />
      ),
      prop: 'salaryMonths'
    },
    {
      label: '原因',
      component: 'el-input',
      prop: 'message',
      props: {
        type: 'textarea',
        maxlength: 1000
      }
    }
  ];
});
const rules = computed(() => {
  return {
    message: [{ required: true, message: '必填', trigger: 'blur' }],
    salaryMonths: [{ required: true, message: '必填', trigger: 'blur' }]
  };
});
const setForm = e => {
  formValue.value = e;
};

const onConfirm = async () => {
  let { message, salaryMonths } = formValue.value.getValues();
  let { isValid } = await formValue.value.validate();
  if (!isValid) return;
  let loading = ElLoading.service({
    target: '.dialogLoading',
    lock: true,
    background: 'rgba(0, 0, 0, 0.2)'
  });
  let { list } = await regularSalarySharingHttp({
    message,
    shareDate: $utils.Date(salaryMonths).format('YYYY-MM-DD')
  }).catch(() => {
    loading.close();
  });
  await setFentanFn(list);
  loading.close();
  visible.value = false;

  console.log(1231231231231);
  emits('confirm');
};

// 分摊开启工作流
async function setFentanFn(list) {
  let resList = await Promise.allSettled(
    list.map(item => {
      return openBusiness(
        'ERM_REGULAR_EMPLOYEES_SHARING_ADD',
        {
          ...item,
          isSubmit: true
        },
        void 0,
        '正式员工成本分摊'
      );
    })
  );
  resList = resList.filter(item => item.status === 'fulfilled').map(item => item.value);
  if (!resList.length) {
    loading.value = false;
    return;
  }
  let resList2 = await Promise.allSettled(
    resList.map(item => {
      let {
        businessInstanceInfo: { businessId },
        businessData: {
          employeeCostSharingVo: { dicDeptMarkName }
        }
      } = item;
      let businessName = ['正式员工成本分摊', dicDeptMarkName].join('-');
      return submitBusiness('ERM_REGULAR_EMPLOYEES_SHARING_ADD', businessId, 'SUBMIT', businessName);
    })
  );

  resList2 = resList2.filter(item => item.status === 'fulfilled').map(item => item.value);
  if (resList2.length == list.length && resList2.length) {
    ElNotification({
      title: '操作成功',
      type: 'success'
    });
  } else if (resList2.length != list.length && resList2.length) {
    let s = resList2.length;
    let f = parseInt(list.length - resList2.length);
    ElNotification({
      title: `成功${s}条，失败${f}条`,
      type: 'success'
    });
  }
}

const onCancel = () => {
  visible.value = false;
};
const close = () => {
  visible.value = false;
};
watch(
  () => props.modelValue,
  async newVal => {
    visible.value = newVal;
  },
  { immediate: true, deep: true }
);
watch(
  () => visible.value,
  async newVal => {
    emits('update:modelValue', newVal);
  },
  { immediate: true, deep: true }
);
</script>
