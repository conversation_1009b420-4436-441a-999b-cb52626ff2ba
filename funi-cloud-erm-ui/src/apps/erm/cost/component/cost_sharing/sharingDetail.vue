<template>
  <div>
    <funi-list-page ref="pageList"
      :cardTab="useCardTab"
      :teleported="false" />
  </div>
</template>
<script lang='jsx' setup>
import { computed, ref, onActivated } from 'vue'
import { queryCostApportionInfoListHttp, apiUrl } from '@/apps/erm/cost/hooks/cost_sharing/costharingApi.jsx'
import { useCostApportColumns, useBtnsConfig, useSharingDetailSearch } from '@/apps/erm/cost/hooks/cost_sharing/useCostSharingInfo.jsx'
import { baseInfoProps } from "@/apps/erm/config/config.jsx";
import { expotrFunction } from '@/apps/erm/config/config.jsx'
const pageList = ref(null)
const queryData = ref(void 0)
const props = defineProps({
  ...baseInfoProps,
})

const lodaData = async (params, queryParams) => {
  queryData.value = queryParams
  let resData = await queryCostApportionInfoListHttp({
    ...params,
    ...queryParams,
    apportId: props.id
  })
  return resData
};
const exportFn = () => {
  expotrFunction({ url: apiUrl.queryCostApportionInfoListExport, params: { ...queryData.value, apportId: props.id }, FileName: '分摊明细', })
}

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: useCostApportColumns(),
        lodaData: lodaData,
        fixedButtons: true,
        useSearchV2: false,
        isShowSearch: true,
        btns: useBtnsConfig(exportFn),
        searchConfig: useSharingDetailSearch()
      }
    }
  ]
})




</script>
<style lang='scss' scoped></style>