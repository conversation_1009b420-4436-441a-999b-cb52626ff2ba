<template>
  <div class="base_info">
    <GroupTitle title="基本信息" />
    <funi-form :schema="schema" :rules="rules" :col="3" @get-form="setForm" />

    <div>
      <GroupTitle title="参与分摊项目" :isSymbol="true">
        <el-button v-if="isEdit" type="primary" @click="addData">添加项目</el-button>
      </GroupTitle>
      <FuniCurd ref="curd" row-key="uuId" :data="costList" :columns="columns" :pagination="false" />
      <ChangCompareTable v-if="changeTable !== false">
        <div>
          <FuniCurd ref="curd" row-key="uuId" :data="changeTable" :columns="columns" :pagination="false" />
        </div>
      </ChangCompareTable>
    </div>

    <div v-if="(props.bizName == '详情' || props.bizName == '审核') && fileParams.params.businessId">
      <GroupTitle title="要件信息" />
      <funi-file-table :params="fileParams.params" onlyShow />
    </div>
    <funi-dialog
      :hideFooter="false"
      v-model="dialogVisible"
      :align-center="true"
      title="添加项目"
      :onConfirm="onConfirm"
      size="max"
    >
      <project-ship-list ref="shipList" />
    </funi-dialog>
    <SubmitSuccess ref="submitSuccess" />
  </div>
</template>

<script lang="jsx" setup>
import { computed, onMounted, inject, reactive, ref, nextTick } from 'vue';
import ProjectShipList from '@/apps/erm/cost/component/cost_sharing/projectShipList.vue';
import { ElNotification } from 'element-plus';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import ChangCompareTable from '@/apps/erm/component/changCompareTable/index.vue';
import { openBusiness } from '@/apps/erm/config/business.js';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
import {
  useAddScheme,
  useChangeScheme,
  useColumns,
  useRules
} from '@/apps/erm/cost/hooks/cost_sharing/useCostSharingInfo.jsx';
import {
  querycapProjectCostInfoHttp,
  capProjectCostApportionNewHttp,
  creatBusHttp
} from '@/apps/erm/cost/hooks/cost_sharing/costharingApi.jsx';
import { useRouter } from 'vue-router';
const props = defineProps({
  ...baseInfoProps,
  type: {
    type: String,
    default: 'info'
  },
  lastId: {
    type: String,
    default: ''
  },

  bizName: {
    type: String,
    default: '新建'
  },
  tab: {
    type: String,
    default: '详情'
  },
  code: {
    type: String,
    default: ''
  },
  reason: {
    type: String,
    default: ''
  },
  businessName: {
    type: String,
    default: ''
  },
  isCopy: {
    type: Boolean,
    default: false
  }
});
const router = useRouter();
const emits = defineEmits(['updateID', 'updatebusinessType', 'updateInfo']);
const datas = reactive({
  formData: null,
  tableList: []
});
const dialogVisible = ref(false);
const shipList = ref(null);
const selectRalateData = ref([]);
const extraList = ref();
const contactRef = {};
const changeData = ref();
const changeTable = ref(false);
const submitSuccess = ref(null);
const loadingStatus = inject('loadingStatus');
const fileParams = reactive({
  params: {
    businessId: ''
  }
});
const costList = computed(() => datas.tableList);
const setForm = e => {
  datas.formData = e;
};

/**
 * 输入框数据变更
 * @param index:number 当前行的index
 * @param name:string 当前输入框的keyName
 * @param e:any 当前输入框返回值
 * **/
const iptChange = (index, name, e) => {
  datas.tableList[index][name] = e;
};

/**
 * delDicItem 删除
 * @param row:obj 当前行数据
 * @param index:number 当前行index
 * **/
const delDicItem = (row, index) => {
  for (let key in contactRef) {
    if (key.indexOf(row.uuId) > -1) {
      Reflect.deleteProperty(contactRef, key);
    }
  }
  datas.tableList.splice(index, 1);
};

const addData = () => {
  dialogVisible.value = true;
};

const verificationTable = () => {
  if (datas.tableList.length == 0) {
    ElNotification({
      title: '提示',
      message: '请选择分摊项目',
      type: 'warning'
    });
    return false;
  } else {
    return verificationTableForm();
  }
};
const verificationTableForm = () => {
  let v_l = [];
  for (let key in contactRef) {
    let v = contactRef[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};

const onConfirm = () => {
  selectRalateData.value = shipList.value?.getRowData();
  for (let i = 0; i < selectRalateData.value.length; i++) {
    if (datas.tableList.findIndex(item => item.projectId == selectRalateData.value[i].projectId) == -1) {
      datas.tableList?.push(selectRalateData.value[i]);
      dialogVisible.value = false;
    } else {
      ElNotification({
        title: '请勿添加重复项目！',
        type: 'warning'
      });
    }
  }
};

const seeProjectInfo = () => {
  let { projectName, projectId, projectSn, dicBusinessTypeCode, businessId } = datas.formData.getValues();
  router.push({
    name: 'erm_projectManage_two',
    query: {
      title: projectName || '项目管理',
      bizName: '详情',
      type: 'info',
      id: projectId,
      projectSn: projectSn,
      tab: ['项目管理', projectName, '详情'].join('-'),
      dicBusinessTypeCode,
      businessId
    }
  });
};

const schema = computed(() => {
  if (props.tab == '变更') {
    return useChangeScheme({
      isEdit: props.isEdit,
      type: props.type,
      formInstance: datas.formData,
      tab: props.tab
    });
  }
  if (props.type == 'add' || props.type == 'edit') {
    return useAddScheme({
      isEdit: props.isEdit,
      type: props.type,
      formInstance: datas.formData,
      tab: props.tab
    });
  } else {
    return useChangeScheme({
      isEdit: props.isEdit,
      type: props.type,
      formInstance: datas.formData,
      tab: props.tab,
      seeProjectInfo,
      changeData: changeData.value
    });
  }
});

const rules = computed(() => {
  return useRules({
    isEdit: props.isEdit,
    type: props.type
  });
});

const columns = computed(() => {
  return useColumns({
    isEdit: props.isEdit,
    contactRef,
    iptChange,
    delDicItem
  });
});

const extraColumns = computed(() => {
  return useColumns({
    isEdit: false
  });
});

const getCostSharingInfo = async (id, isChange = false) => {
  loadingStatus.value.status = true;
  let capProjectCostApportionVo, capitalizeProjectShipListVoList;
  if (!props.reason) {
    let res = await querycapProjectCostInfoHttp({ capProjectCostApportionId: id }).finally(() => {
      loadingStatus.value.status = false;
    });

    if (props.isCopy) {
      capProjectCostApportionVo = res?.capProjectCostApportionVo;
      capProjectCostApportionVo.dicApportStatusCode = '0';
      capProjectCostApportionVo.dicApportStatusName = '草稿';
      capProjectCostApportionVo.dicBusinessTypeCode = props.code;
      capitalizeProjectShipListVoList = res?.capitalizeProjectShipListVoList;
      Reflect.deleteProperty(capProjectCostApportionVo, 'id');
      Reflect.deleteProperty(capProjectCostApportionVo, 'lastId');
      Reflect.deleteProperty(capProjectCostApportionVo, 'businessNode');
      Reflect.deleteProperty(capProjectCostApportionVo, 'dicDataStatusCode');
      Reflect.deleteProperty(capProjectCostApportionVo, 'businessId');
      Reflect.deleteProperty(capProjectCostApportionVo, 'apportSn');
      emits('updateID', {
        id: '',
        businessId: ''
      });
    } else {
      capProjectCostApportionVo = res?.capProjectCostApportionVo;
      capitalizeProjectShipListVoList = res?.capitalizeProjectShipListVoList;

      if (!isChange) {
        emits('updateInfo', capProjectCostApportionVo);
      }
    }
  } else {
    let res = await creatBusHttp({ id: id, dicBusinessTypeCode: props.code }).finally(() => {
      loadingStatus.value.status = false;
    });
    capProjectCostApportionVo = res?.capProjectCostApportionVo;
    capProjectCostApportionVo.dicApportStatusCode = '0';
    capProjectCostApportionVo.dicApportStatusName = '草稿';
    capProjectCostApportionVo.dicBusinessTypeCode = props.code;
    capitalizeProjectShipListVoList = res?.capitalizeProjectShipListVoList;
    datas.formData?.setValues({
      extraFormData: {
        ...$utils.clone(capProjectCostApportionVo)
      }
    });

    emits('updateInfo', datas.formData.getValues());
  }

  let apportCycleAry;
  if (capProjectCostApportionVo.startCapTime && capProjectCostApportionVo.endCapTime) {
    apportCycleAry = [capProjectCostApportionVo.startCapTime, capProjectCostApportionVo.endCapTime];
  }
  let projectObj = {
    id: capProjectCostApportionVo.projectId
  };

  if (isChange) {
    changeData.value = {
      ...capProjectCostApportionVo,
      projectObj,
      apportCycleAry
    };
    changeTable.value = capitalizeProjectShipListVoList;
  } else {
    datas.formData.setValues({
      ...capProjectCostApportionVo,
      projectObj,
      apportCycleAry
    });
    fileParams.params.businessId = capProjectCostApportionVo.businessId;
    datas.tableList = capitalizeProjectShipListVoList;
  }
};

const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await datas.formData.validate();
  if (type == 'ts' || (isValid && verificationTable())) {
    let formData = JSON.parse(JSON.stringify(datas.formData.getValues()));
    if (formData?.apportCycleAry) {
      formData.startCapTime = formData.apportCycleAry[0];
      formData.endCapTime = formData.apportCycleAry[1];
      Reflect.deleteProperty(formData, 'apportCycleAry');
    }
    if (formData.projectObj) {
      formData.projectId = formData.projectObj.id;
      formData.projectName = formData.projectObj.name;
      Reflect.deleteProperty(formData, 'projectObj');
    }
    let capitalizeProjectShipData = JSON.parse(JSON.stringify(datas.tableList));
    capitalizeProjectShipData.forEach(item => {
      Reflect.deleteProperty(item, 'addTime');
      Reflect.deleteProperty(item, 'countCapTotalAmount');
      Reflect.deleteProperty(item, 'confirmTotalAmount');
      Reflect.deleteProperty(item, 'receiptTotalAmount');
    });
    Reflect.deleteProperty(formData, 'preApportionAmount');
    let obj = {
      ...formData,
      id: props.id || '',
      capitalizeProjectShipRequestList: capitalizeProjectShipData
    };
    emits('updateInfo', obj);
    return obj;
  } else {
    return false;
  }
};

const saveData = async type => {
  let res = await getData(type);
  if (!res) return Promise.reject();
  let resData;
  if (!props.id) {
    if (props.reason) {
      resData = await openBusiness(props.code, res, props.reason, props.businessName);
    } else {
      resData = await openBusiness(props.code, res, '', props.businessName);
    }
    let {
      id,
      businessId,
      lastId,
      apportSn,
      dicApportStatusName,
      dicApportStatusCode,
      dicBusinessTypeCode,
      dicApportDoingBusCode,
      dicApportDoingBusName
    } = resData.businessData.capProjectCostApportionVo;
    res.dicApportDoingBusCode = dicApportDoingBusCode;
    res.dicApportDoingBusName = dicApportDoingBusName;
    emits('updateID', {
      id,
      lastId: props.lastId || lastId,
      businessId: businessId || props.businessId
    });

    emits('updateInfo', resData.businessData.capProjectCostApportionVo);
    datas.formData.setValues({
      apportSn,
      dicApportStatusName,
      dicApportStatusCode,
      dicBusinessTypeCode,
      dicApportDoingBusCode,
      dicApportDoingBusName
    });
  }
  if (props.id && type === 'ts') {
    await capProjectCostApportionNewHttp({
      ...res,
      id: props.id,
      isSubmit: false
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject();
  emits('updatebusinessType', props.code);
  if (props.id && !type) {
    await capProjectCostApportionNewHttp({
      ...res,
      id: props.id,
      isSubmit: true
    });
  }
  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  return Promise.resolve({});
};

onMounted(() => {
  if (props.id) {
    getCostSharingInfo(props.id);
  }
  if (props.lastId && props.type == 'audit' && props.code == 'ERM_CAP_PROJECT_COST_APPORTION_CHANGE') {
    getCostSharingInfo(props.lastId, true);
  }
});

const ts = () => {
  return saveData('ts');
};

const nextStep = () => {
  return saveData();
};
defineExpose({
  ts,
  nextStep
});
</script>
<style lang="scss" scoped>
.base_info {
  // padding: 20px;
  box-sizing: border-box;
}
</style>
