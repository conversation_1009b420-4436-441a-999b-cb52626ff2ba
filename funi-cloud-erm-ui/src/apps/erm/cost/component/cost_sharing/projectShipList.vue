<template>
  <div>
    <funi-list-page ref="pageList"
      class="list_page"
      :cardTab="useCardTab"
      :teleported="false" />
  </div>
</template>
<script lang="jsx" setup>
import { computed, ref, onActivated } from 'vue';
import { queryProjectForCapHttp } from '@/apps/erm/cost/hooks/cost_sharing/costharingApi.jsx';
import { useProjectShipColumns, useSearchConfig } from '@/apps/erm/cost/hooks/cost_sharing/useCostSharingInfo.jsx';

const pageList = ref(null);
const rowData = ref([]);
const lodaData = async (page, params) => {
  let resData = await queryProjectForCapHttp({
    ...page,
    ...params
  });
  return resData;
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: useProjectShipColumns(),
        lodaData: lodaData,
        useSearchV2: false,
        isShowSearch: true,
        searchConfig: useSearchConfig(),
        colNumber: 4,
        fixedButtons: true,
        on: {
          selectionChange: selection => {
            rowData.value = selection;
          }
        }
      }
    }
  ];
});

const getRowData = () => {
  let shipData = rowData.value?.map(item => {
    return {
      projectId: item?.id,
      projectSn: item?.projectSn,
      projectName: item?.projectName,
      companyId: item?.companyId,
      companyShortName: item?.companyShortName,
      deptId: item?.deptId,
      deptName: item?.deptName,
      apportionTotalAmount: item?.apportionTotalAmount,
      countCapTotalAmount: item?.countCapTotalAmount ? String(item?.countCapTotalAmount) : '0',
      confirmTotalAmount: item?.confirmTotalAmount ? String(item?.confirmTotalAmount) : '0',
      receiptTotalAmount: item?.receiptTotalAmount ? String(item?.receiptTotalAmount) : '0'
    };
  });
  return shipData;
};

defineExpose({
  // rowData: rowData.value,
  getRowData
});
</script>
<style lang="scss" scoped></style>
