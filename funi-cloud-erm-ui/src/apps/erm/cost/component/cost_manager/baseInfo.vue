<template>
  <div class="base_info">
    <GroupTitle title="基本信息" />
    <funi-form :schema="schema" :col="3" @get-form="setForm" />
  </div>
</template>

<script lang="jsx" setup>
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { scheme } from '@/apps/erm/cost/hooks/cost_manager/useCostManagerInfo';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import { computed, onMounted, ref, reactive } from 'vue';
import { costInfoHttp } from '@/apps/erm/cost/hooks/cost_manager/costManagerApi.jsx';
import { useRouter } from 'vue-router';
import { ElNotification } from 'element-plus';

const router = useRouter();

const props = defineProps({
  ...baseInfoProps
});
const formData = ref();
const datas = reactive({
  projectName: void 0,
  projectId: void 0,
  projectSn: void 0
});

const setForm = e => {
  formData.value = e;
};

const seeProjectInfo = () => {
  const { projectName, projectId, projectSn } = formData.value.getValues();
  router.push({
    name: 'erm_projectManage_two',
    query: {
      title: projectName || '项目管理',
      bizName: '详情',
      type: 'info',
      id: projectId,
      projectSn: projectSn,
      tab: ['项目管理', projectName, '详情'].join('-')
    }
  });
};
//TODO
const seeBusinessInfo = formModel => {
  let type = {
    //计提-绩效
    accrualPerformance: 'accrual_performance',
    //计提-非合同
    accrualNoContract: 'accrual_no_contract',
    //计提-付款合同
    accrualPayContract: 'accrual_pay_contract',
    //工资分摊
    salarySalaryfentan: 'salary_salaryfentan',
    //研发绩效分摊
    salaryResearchfentan: 'salary_researchfentan',
    //资本化项目成本分摊
    capProCostApportion: 'cap_pro_cost_apportion'
  };
  let { dicSourceTypeCode, businessDataId, projectName } = formModel;
  switch (dicSourceTypeCode) {
    case type.accrualPerformance:
      break;
    case type.accrualNoContract:
      router.push({
        name: 'erm_non_contractual_two',
        query: {
          bizName: '详情',
          type: 'info',
          id: businessDataId,
          tab: ['非合同计提', projectName, '详情'].join('-'),
          title: projectName || '非合同计提'
        }
      });
      break;
    case type.accrualPayContract:
      router.push({
        name: 'erm_payment_contract_two',
        query: {
          bizName: '详情',
          type: 'info',
          id: businessDataId,
          tab: ['付款合同计提', projectName, '详情'].join('-'),
          title: projectName || '付款合同计提'
        }
      });
      break;
    case type.salarySalaryfentan:
      router.push({
        name: 'erm_cost_wages_manager_two',
        query: {
          bizName: '详情',
          type: 'info',
          id: businessDataId,
          tab: ['工资管理', projectName, '详情'].join('-'),
          title: projectName || '工资详情'
        }
      });
      break;
    case type.salaryResearchfentan:
      router.push({
        name: 'erm_cost_wages_manager_two',
        query: {
          bizName: '详情',
          type: 'info',
          id: businessDataId,
          tab: ['工资管理', projectName, '详情'].join('-'),
          title: projectName || '工资详情'
        }
      });
      break;
    case type.capProCostApportion:
      router.push({
        name: 'erm_cost_sharing_two',
        query: {
          title: projectName || '资本化项目成本分摊',
          bizName: '详情',
          type: 'info',
          id: businessDataId,
          tab: ['资本化项目成本分摊', projectName, '详情'].join('-')
        }
      });
      break;
    default:
      ElNotification({ type: 'info', message: '该业务为非本系统业务，无法查看。请到相关系统进行详情查看“。' });
      break;
  }
};

const schema = computed(() => {
  const { isEdit } = props;
  return scheme({
    isEdit,
    seeProjectInfo,
    seeBusinessInfo
  });
});

const getCostInfo = async id => {
  let res = await costInfoHttp({ costInfoId: id });
  datas.projectName = res?.projectName;
  datas.projectId = res?.projectId;
  datas.projectSn = res?.projectSn;

  formData.value.setValues({ ...res });
};

onMounted(() => {
  if (props.id) {
    getCostInfo(props.id);
  }
});
</script>
<style lang="scss" scoped>
.base_info {
  // padding: 20px;
  box-sizing: border-box;
}
</style>
