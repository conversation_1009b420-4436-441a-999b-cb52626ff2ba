<template>
  <div>
    <funi-file-table></funi-file-table>

  </div>
</template>

<script lang='jsx' setup>
import funiFileTable from '@/components/FuniFileTable/index.vue'
// import { useColumns } from '@/apps/erm/cost/hooks/payment_contract/usePaymentContractInfo'
const props = defineProps({
  // isEdit: {
  //   type: Boolean,
  //   default: true
  // },
  // listData: {
  //   type: Array,
  //   default: () => []
  // },
  // columns: {
  //   type: Array,
  //   default: () => []
  // }
})

// const columns = useColumns()

</script>
<style lang='scss' scoped></style>