<template>
  <div class="base_info">
    <GroupTitle title="基本信息" />
    <funi-form :schema="schema" :col="3" @get-form="setForm" :rules="rules" />
    <div v-show="isShow">
      <GroupTitle title="绩效明细" />
      <funi-list-page-v2 :cardTab="useCardTab" :teleported="false" ref="pageList" />
    </div>
    <div v-if="!['编辑', '新建'].includes(props.bizName) && fileParams.params.businessId">
      <GroupTitle title="要件信息" />
      <funi-file-table :params="fileParams.params" />
    </div>
  </div>
</template>

<script lang="jsx" setup>
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import formTable from '@/apps/erm/cost/component/non_contractual/formTable.vue';
import { ElNotification } from 'element-plus';
import { scheme } from '@/apps/erm/cost/hooks/non_contractual/useNonContractualInfo';
import {
  accrualInfoHttp,
  queryPerformanceDetailListHttp,
  apiUrl,
  accrualNewHttp
} from '@/apps/erm/cost/hooks/non_contractual/nonContractual.jsx';
import { openBusiness } from '@/apps/erm/config/business.js';
import {
  useColumns,
  useBtnsConfig,
  useRules,
  useSrearchConfig
} from '@/apps/erm/cost/hooks/non_contractual/useNonContractualInfo.jsx';
import { computed, onMounted, reactive, ref, nextTick, watch } from 'vue';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
const props = defineProps({
  ...baseInfoProps,
  bizName: {
    type: String,
    default: '新增'
  },
  isCopy: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['updateInfo', 'updateID']);
const rowData = ref(void 0);
const isShow = ref(false);
const pageList = ref(void 0);
const fileParams = reactive({
  params: {
    businessId: ''
  }
});
const loadData = async params => {
  let resData = await queryPerformanceDetailListHttp(params);
  return resData;
};

//导出
const exportFn = () => {
  expotrFunction({ url: apiUrl.queryAccrualNoContractListExport, params: {}, FileName: '绩效列表' });
};
//导入
const importFn = () => {
  pageList.value.reload({ resetPage: false });
};
//删除
const delFn = () => {};
//导入实发金额
const exportMoney = () => {};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig(exportFn, importFn),
        fixedButtons: true,
        lodaData: loadData,
        useSearchV2: false,
        isShowSearch: true,
        searchConfig: useSrearchConfig(),
        columns: useColumns(),
        checkOnRowClick: true,
        on: {
          rowClick: selection => {
            rowData.value = selection.row;
          }
        }
      }
    }
  ];
});

const datas = reactive({
  formData: null
});

const setForm = e => {
  datas.formData = e;
};

const form = computed(() => datas.formData?.getValues());
watch(
  () => form.value,
  newValue => {
    if (newValue && newValue.dicSecondAccrualTypeCode == '2') {
      isShow.value = true;
    } else {
      isShow.value = false;
    }
  },
  { immediate: true, deep: true }
);

const schema = computed(() => {
  const { isEdit } = props;
  return scheme({
    isEdit,
    formInstance: datas.formData
  });
});
const rules = computed(() => {
  return useRules({ isEdit: props.isEdit });
});

const getAccrualInfo = async id => {
  let { accrualVo } = await accrualInfoHttp({ accrualId: id });
  let obj;
  if (props.isCopy) {
    obj = {
      ...accrualVo,
      projectObj: {
        id: accrualVo.projectId,
        name: accrualVo.projectName
      }
    };
    Reflect.deleteProperty(obj, 'id');
    Reflect.deleteProperty(obj, 'accrualSn');
    Reflect.deleteProperty(obj, 'businessId');
    Reflect.deleteProperty(obj, 'dicDataStatusCode');
    Reflect.deleteProperty(obj, 'businessId');
    Reflect.deleteProperty(obj, 'businessNode');
    Reflect.deleteProperty(obj, 'dicPayStatusCode');
    obj.dicAccrualStatusCode = '0';
    obj.dicAccrualStatusName = '草稿';
    emits('updateID', {
      id: '',
      businessId: ''
    });
  } else {
    obj = {
      ...accrualVo,
      projectObj: {
        id: accrualVo.projectId
      }
    };
    fileParams.params.businessId = accrualVo.businessId;
    emits('updateInfo', obj);
  }
  datas.formData.setValues({
    ...obj
  });
};

const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await datas.formData.validate();
  if (isValid || type == 'ts') {
    let formData = JSON.parse(JSON.stringify(datas.formData.getValues()));
    if (formData.projectObj) {
      formData['projectId'] = formData.projectObj.id || '';
      formData['projectName'] = formData.projectObj.name || '';
      Reflect.deleteProperty(formData, 'projectObj');
    }
    let obj = {
      id: props.id || '',
      ...formData
    };
    emits('updateInfo', obj);
    return obj;
  } else {
    return false;
  }
};

const saveData = async type => {
  let res = await getData(type);
  if (!res) return Promise.reject();
  if (!props.id) {
    let resData = await openBusiness('ERM_ACCRUAL_NOCONTRACT_ADD', res, '', '非合同计提新增');
    let { id, businessId, accrualSn, dicAccrualStatusName } = resData.businessData.accrualVo;
    datas.formData.setValues({
      accrualSn,
      dicAccrualStatusName
    });
    emits('updateID', {
      id,
      businessId: businessId || props.businessId
    });
    emits('updateInfo', resData.businessData.accrualVo);
  } else if (props.id && type == 'ts') {
    await accrualNewHttp({
      ...res,
      id: props.id,
      isSubmit: false
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject();
  if (props.id && !type) {
    await accrualNewHttp({
      ...res,
      id: props.id,
      isSubmit: true
    });
  }
  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  return Promise.resolve({});
};

const ts = () => {
  return saveData('ts');
};

const nextStep = () => {
  return saveData();
};

defineExpose({
  ts,
  nextStep
});

onMounted(() => {
  if (props.id) {
    getAccrualInfo(props.id);
  }
});
</script>

<style lang="scss" scoped>
.base_info {
  // padding: 20px;
  box-sizing: border-box;
}
</style>
