<template>
  <el-tree-select
    v-model="modelVal"
    :data="treeData"
    check-strictly
    v-bind="$attrs"
    :props="propsMap"
    filterable
    expand-on-click-node
    default-expand-all
    @node-click="nodeClick"
  />
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { queryCurrentMarkDeptTreeHttp } from '@/apps/erm/cost/hooks/epibolyCost/epibolyCostApi.jsx';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  params: {
    type: Object,
    default: () => {}
  },
  propsMap: {
    type: Object,
    default: () => {}
  },
  api: {
    type: Function,
    default: () => {}
  },
  init: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:modelValue', 'change']);

const propsMap = {
  value: props.propsMap?.value,
  label: props.propsMap?.label,
  children: props.propsMap?.children
};
let treeData = ref([]);

const nodeClick = e => {
  emits('change', e[props.propsMap?.value], e[props.propsMap?.label]);
  emits('update:modelValue', e[props.propsMap?.value]);
};

const getTreeData = async () => {
  let res = await queryCurrentMarkDeptTreeHttp(props.params);
  treeData.value = res.list;
};


onMounted(() => {
  getTreeData();

});
</script>
