<template>
  <div>
    <funi-dialog v-model="visible" :onConfirm="onConfirm" :onCancel="onCancel" title="请选择发起业务">
      <el-radio-group v-model="type">
        <el-radio value="0" label="0" size="large">前台外包人员成本分摊确认</el-radio>
        <el-radio value="1" label="1" size="large">中台外包人员成本分摊确认</el-radio>
      </el-radio-group>
    </funi-dialog>
  </div>
  <funi-dialog v-model="foreVisible" size="large" :onConfirm="onForeConfirm" :onCancel="onForeCancel" title="选择人员">
    <funi-list-page ref="pageList" :cardTab="useCardTab" :teleported="false" />
  </funi-dialog>
  <funi-dialog
    v-model="middleVisible"
    size="large"
    :onConfirm="onMiddleConfirm"
    :onCancel="onMiddleCancel"
    title="请选择分摊核算的月份"
  >
    <span style="color: red">*</span>年月：
    <el-date-picker
      style="width: 500px"
      value-format="YYYY-MM"
      type="month"
      placeholder="请选择年月"
      v-model="monthRange"
    />
  </funi-dialog>
</template>

<script lang="jsx" setup>
import { computed, nextTick, reactive, ref } from 'vue';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
import { querySalaryDetailListHttp } from '@/apps/erm/cost/hooks/epibolyCost/epibolyCostApi.jsx';
import ErmTreeSelect from '@/apps/erm/component/ermTree/index.vue';
import DatePicker from '@/apps/erm/cost/component/epibolyCost/datePicker.vue';
import TreeSelect from '@/apps/erm/cost/component/epibolyCost/TreeSelect.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['updateStatus']);
const router = useRouter();
const foreVisible = ref(false);
const middleVisible = ref(false);
const monthRange = ref($utils.Date().subtract(1, 'months').format('YYYY-MM'));
const type = ref();
const pageList = ref();
const selectData = ref();
let date = ref();
let deptId = ref('');

const visible = computed(() => props.visible);
const onCancel = () => {
  type.value = void 0;
  emit('updateStatus');
};
const onConfirm = () => {
  emit('updateStatus');
  if (type.value === '0') {
    foreVisible.value = true;
  }
  if (type.value === '1') {
    middleVisible.value = true;
  }
};
const onForeCancel = () => {
  foreVisible.value = false;
  visible.value = false;

  type.value = void 0;
  emit('updateStatus');
};

const onForeConfirm = async () => {
  if (!selectData.value) {
    ElNotification({ type: 'warning', message: '请选择需要分摊的外包人员' });
    return;
  }
  let empSns = selectData.value.map(item => item.empSn);
  emit('updateStatus', type.value, empSns, date.value, deptId.value);
  foreVisible.value = false;
  visible.value = false;
  type.value = void 0;
};

const onMiddleConfirm = async () => {
  if (!monthRange.value) {
    ElNotification({ type: 'warning', message: '请选择分摊核算的月份' });
    return;
  }
  emit('updateStatus', type.value, void 0, monthRange.value);
  type.value = void 0;
  middleVisible.value = false;
  visible.value = false;
};

const onMiddleCancel = () => {
  middleVisible.value = false;
  visible.value = false;
  type.value = void 0;
  emit('updateStatus');
};

const lodaData = async (page, params) => {
  deptId.value = params.deptId;
  let year = params.monthRange?.split('-')[0];
  let month = params.monthRange?.split('-')[1];
  date.value = params.monthRange;
  if (!deptId.value || (!year && !month)) return [];
  let resData = await querySalaryDetailListHttp({
    ...page,
    dicDeptMarkCode: 0,
    dicEmployeeTypeCode: 4,
    year,
    month,
    deptId: deptId.value
  });
  let uniqueResData = [...new Map(resData.investList.map(item => [item.empSn, item])).values()];
  return {
    list: uniqueResData
  };
};

const useColumns = () => {
  return [
    {
      type: 'selection',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },
    {
      label: '员工姓名',
      prop: 'empName'
    },
    {
      label: '年度',
      prop: 'year'
    },
    {
      label: '月份',
      prop: 'month'
    }
  ];
};

const useSearchConfig = () => {
  return {
    schema: [
      {
        label: '归属部门',
        component: () => (
          <TreeSelect
            propsMap={{
              value: 'id',
              label: 'deptName',
              children: 'childList'
            }}
            params={{ deptMarkCode: 0 }}
          ></TreeSelect>
        ),
        prop: 'deptId',
        colProps: {
          span: 6
        },
        props: {
          placeholder: '请选择归属部门'
        }
      },
      {
        label: '年月',
        component: ({ formModel }) => <DatePicker onChange={e => (formModel.monthRange = e)}></DatePicker>,
        prop: 'monthRange'
      }
    ],
    rules: {
      monthRange: [
        {
          required: true,
          message: '必填',
          trigger: 'change'
        }
      ],
      deptId: [
        {
          required: true,
          message: '必填',
          trigger: 'change'
        }
      ]
    }
  };
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: useColumns(),
        lodaData: lodaData,
        useSearchV2: false,
        isShowSearch: true,
        searchConfig: useSearchConfig(),
        colNumber: 3,
        fixedButtons: false,
        on: {
          selectionChange: selection => {
            selectData.value = selection;
          }
        }
      }
    }
  ];
});
</script>
<style lang="scss" scoped></style>
