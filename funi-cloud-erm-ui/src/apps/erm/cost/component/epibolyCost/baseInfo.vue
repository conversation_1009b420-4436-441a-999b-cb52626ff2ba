<template>
  <div>
    <GroupTitle title="基本信息" />
    <funi-form :schema="schema" :col="3" @get-form="setForm" />
    <GroupTitle title="投入明细">
      <el-button type="primary" v-if="dicEmployeeTypeCode == '1' && type == 'audit' && hasAuth" @click="reAllocate"
        >重新分摊</el-button
      >
    </GroupTitle>
    <FuniCurd
      ref="curd"
      row-key="uuId"
      :data="tableData.dataList"
      :columns="columns"
      :searchConfig="useSearchConfig()"
      :useSearchV2="false"
      :isShowSearch="isEdit || type == 'audit' ? false : true"
      :loading="loading"
      :pagination="true"
      :lodaData="isEdit || type == 'audit' ? undefined : loadData"
    >
      <template #nameHead>
        <div class="musterW">
          <i v-if="type == 'audit'">*</i>
          项目名称
          <el-button
            size="small"
            v-if="(isEdit == true || type == 'audit') && deptMarkCode == '0'"
            type="primary"
            @click="copy"
            >复制</el-button
          >
        </div>
      </template>
      <template #pagination_extra>
        <p v-if="dicEmployeeTypeCode == '1'">
          <span style="color: red">
            共计：{{ tableData?.total ?? '--' }}条数据，{{ tableData.projectCount ?? '--' }}个项目，投入
            {{ tableData.totalDays ?? '--' }} 天，投入{{ tableData.investAmount ?? '--' }} 元， 其中工资投入{{
              tableData.totalSalary ?? '--'
            }}
            元，公司缴纳的五险一金 {{ tableData.totalSocialSecurityHousingFund ?? '--' }} 元，研发绩效
            {{ tableData.totalMeritsSalary ?? '--' }} 元。
          </span>
        </p>
        <p v-else>
          <span v-if="deptMarkCode == '0'" style="color: red">
            共计： 投入{{ tableData?.peoples ?? '--' }}人， {{ tableData?.totalDays ?? '--' }} 天，
            {{ tableData?.investAmount ?? '--' }} 元。
          </span>
          <span v-if="deptMarkCode == '1'" style="color: red">
            共计：{{ tableData?.total ?? '--' }}条数据，{{ tableData.projectCount ?? '--' }}个项目，投入
            {{ tableData.totalDays ?? '--' }} 天，{{ tableData.investAmount ?? '--' }}
            元。
          </span>
        </p>
      </template>
    </FuniCurd>
    <!-- <handlerDialog
      title="研发绩效分摊计算"
      ref="hDialog"
      v-model="dialogVisible"
      :isShow="isShow"
      :type="dialogtype"
      @getStorageData="getStorageData"
      :costMonths="costMonths"
      :sharingId="id"
      :needClosePage="false"
    /> -->
  </div>
</template>

<script lang="jsx" setup>
import { reactive, computed, ref, onMounted, nextTick } from 'vue';
import { useAppStore } from '@/stores/useAppStore';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
// import handlerDialog from '@/apps/erm/cost/component/regularCost/handlerDialog.vue';
import { useSchema, useColumns } from '@/apps/erm/cost/hooks/epibolyCost/useEpiboyCostInfo.jsx';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import { ElNotification } from 'element-plus';
import { usePermissionStore } from '@/stores/usePermissionStore';
import {
  querySalaryDetailListHttp,
  queryCostSharingInfoHttp,
  employeeCostNewHttp,
  checkCostHttp,
  apiUrl,
  queryInvestDetailListByParameterHttp
} from '@/apps/erm/cost/hooks/epibolyCost/epibolyCostApi.jsx';
import { openBusiness } from '@/apps/erm/config/business.js';
const appStore = useAppStore();
import { useRoute } from 'vue-router';
const route = useRoute();
const hasAuth = ref(false);
const loading = ref(false);
const tableCellStatus = ref({});
const permissions = usePermissionStore().permissionsOfParentMenu || [];

console.log(permissions, 'permissionspermissionspermissions');
hasAuth.value = permissions.includes('ERM_REGULAR_COST_REALLOCATE');

const curd = ref();
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  id: {
    type: String,
    default: ''
  },
  callBackFn: {
    type: Function,
    default: () => {}
  }
});

const loadData = async (page, params) => {
  if (props.isEdit || type == 'audit') return;
  loading.value = true;
  let { list, total } = await queryInvestDetailListByParameterHttp({ ...page, ...params, sharingId: props.id });
  let tableList = list.map(item => {
    return {
      ...item,
      companyProjectObj: {
        id: item.companyProjectId,
        name: item.companyProjectName,
        sn: item.companyProjectSn
      }
    };
  });
  setTimeout(() => {
    loading.value = false;
  }, 200);
  return {
    list: tableList,
    total
  };
};

const emits = defineEmits(['updateInfo', 'updateID']);
const deptMarkCode = route.query.dicDeptMarkCode;
const isRegular = route.query.isRegular;
const deptId = route.query.deptId;
let deptName = void 0;
const dicEmployeeTypeCode = ref('');
const type = route.query.type;
const tableData = ref({
  dataList: void 0,
  devInvestList: void 0,
  peoples: void 0,
  totalDays: void 0,
  amounts: void 0,
  total: void 0,
  projectCount: void 0
});

const dialogVisible = ref(false);
const isShow = ref(false);
const dialogtype = ref('');
const costMonths = ref();
const empSns = route.query.empSns ? JSON.parse(route.query.empSns) : void 0;
const date = route.query.date;
let defaultUserInfo = {
  id: appStore.user.id,
  name: appStore.user.username
};
const shareDate = ref();
const baseInfo = ref({
  year: date ? $utils.Date(date).format('YYYY') : '--',
  month: date ? $utils.Date(date).format('MM') : '--',
  name: defaultUserInfo.name,
  id: defaultUserInfo.id
});
//研发绩效分摊计算
const costRDallocationFn = () => {
  dialogVisible.value = true;
  isShow.value = true;
  // dialogtype.value = '研发绩效';
};

const getStorageData = () => {
  getSalaryDetailInfo();
};

const reAllocate = () => {
  costRDallocationFn();
};
const data = reactive({
  formData: void 0
});
const schema = computed(() => {
  return useSchema({ deptMarkCode, dicEmployeeTypeCode: dicEmployeeTypeCode.value });
});

const useSearchConfig = () => {
  const style = {
    width: '100%'
  };
  let schema = [
    {
      label: '项目名称',
      component: 'el-input',
      prop: 'projectName',
      props: {
        placeholder: '请输入项目名称'
      }
    },
    {
      label: '员工姓名',
      component: 'el-input',
      prop: 'employeeName',
      props: {
        placeholder: '请输入员工姓名'
      }
    },
    {
      label: '投入类型',
      component: () => (
        <ErmSelect
          customerFetch={true}
          auto={true}
          code={'customer'}
          customerApi={apiUrl.queryInvestTypeCodeList}
          defaultProps={{
            name: 'name',
            code: 'code'
          }}
        ></ErmSelect>
      ),
      prop: 'dicInvestTypeCode',
      props: {
        placeholder: '请选择投入类型'
      }
    }
  ];
  if (dicEmployeeTypeCode.value !== '1') schema.pop();
  return {
    border: false,
    schema: schema
  };
};

const columns = computed(() => {
  return useColumns({
    isEdit: props.isEdit,
    deptMarkCode,
    type,
    tableCellStatus: tableCellStatus.value,
    dicEmployeeTypeCode: dicEmployeeTypeCode.value,
    tableData,
    shareDate: shareDate.value
  });
});
const setForm = e => {
  data.formData = e;
};

const getEmpList = async () => {
  let res = await querySalaryDetailListHttp({
    dicDeptMarkCode: deptMarkCode,
    dicEmployeeTypeCode: '4',
    empSnList: deptMarkCode == '0' ? empSns : void 0,
    year: baseInfo.value.year,
    month: baseInfo.value.month,
    deptId
  });
  tableData.value.devInvestList = res.devInvestList || null;
  tableData.value.peoples = res.peoples;
  tableData.value.totalDays = res.totalDays;
  tableData.value.investAmount = res.amounts;
  tableData.value.total = res.total;
  tableData.value.projectCount = res.projectCount;
  dicEmployeeTypeCode.value = res.dicEmployeeTypeCode;
  tableData.value.dataList = res.investList.map(item => {
    let { companyProjectId: id, companyProjectName: projectName, companyProjectSn: sn } = item;
    return {
      ...item,
      companyProjectObj: {
        id,
        name: projectName,
        sn
      }
    };
  });
  deptName = res?.deptName;
  data.formData.setValues({
    year: baseInfo.value.year,
    month: baseInfo.value.month,
    investAmount: res.amounts,
    deptName: res?.deptName,
    companyName: res.investList?.[0]?.companyName,
    creatorName: baseInfo.value.name
  });
  shareDate.value = `${baseInfo.value.year}-${baseInfo.value.month}-01`;
};

const getSalaryDetailInfo = async queryParams => {
  loading.value = true;
  // let res = info;
  // if (!info) {
  let res = await queryCostSharingInfoHttp({
    employeeCostSharingId: props.id,

    ...queryParams
  }).finally(() => {
    setTimeout(() => {
      loading.value = false;
    }, 500);
  });
  // }
  tableData.value.dataList = res.investList.map(item => {
    return {
      ...item,
      companyProjectObj: {
        id: item?.companyProjectId,
        name: item?.companyProjectName,
        sn: item?.companyProjectSn
      }
    };
  });

  console.log(tableData.value.dataList, ' tableData.value.dataList');
  tableData.value.amounts = res.employeeCostSharingVo?.amounts;
  dicEmployeeTypeCode.value = res.employeeCostSharingVo?.dicEmployeeTypeCode;
  tableData.value.peoples = res.employeeCostSharingVo?.peoples;
  tableData.value.totalDays = res.employeeCostSharingVo?.totalDays;
  tableData.value.totalSalary = res.employeeCostSharingVo?.totalSalary;
  tableData.value.investAmount = res.employeeCostSharingVo?.investAmount;
  tableData.value.total = res.employeeCostSharingVo?.total;
  tableData.value.projectCount = res.employeeCostSharingVo?.projectCount;
  tableData.value.totalDays = res.employeeCostSharingVo?.totalDays;
  tableData.value.totalSocialSecurityHousingFund = res.employeeCostSharingVo?.totalSocialSecurityHousingFund;
  tableData.value.totalMeritsSalary = res.employeeCostSharingVo?.totalMeritsSalary;
  data.formData.setValues({ ...res.employeeCostSharingVo });
  emits('updateID', {
    id: res.employeeCostSharingVo.id,
    businessId: res.employeeCostSharingVo.businessId
  });
  costMonths.value =
    res.employeeCostSharingVo.year && res.employeeCostSharingVo.month
      ? [res.employeeCostSharingVo.year, res.employeeCostSharingVo.month].join('-')
      : '';
  shareDate.value = `${res.employeeCostSharingVo.year}-${res.employeeCostSharingVo.month}-01`;
  emits('updateInfo', res.employeeCostSharingVo);

  return tableData.value.dataList;
};

onMounted(() => {
  if (props.id) {
    getSalaryDetailInfo();
  } else {
    getEmpList();
  }
});

const copy = () => {
  let targetProjectObj = tableData.value.dataList[0]?.companyProjectObj;

  console.log(targetProjectObj);
  if (!targetProjectObj) return;
  if (!targetProjectObj.id) {
    ElNotification({ type: 'warning', message: '第一条数据的项目名称为空，不能进行复制操作' });
    return;
  }
  let { id, name, sn } = targetProjectObj;
  tableData.value.dataList.forEach(element => {
    console.log(element, '123123');
    if (element.edit) {
      element.companyProjectObj = {
        id,
        name,
        sn
      };
      element.companyProjectSn = sn;
    }
  });

  nextTick(() => {
    getDataList();
  });
};

const checkCost = async () => {
  let obj = {
    dicDeptMarkCode: deptMarkCode,
    month: baseInfo.value.month,
    year: baseInfo.value.year,
    dicEmployeeTypeCode: dicEmployeeTypeCode.value || '4',
    deptId
  };
  let res = await checkCostHttp({ ...obj })
    .then(() => {
      return true;
    })
    .catch(() => {
      return false;
    });
  return res;
};

const getData = async () => {
  let checkPass = await checkCost();
  if (checkPass) {
    let formData = await data.formData.getValues();

    let investList = tableData.value.dataList.map(item => {
      return {
        ...item,
        companyProjectId: item.companyProjectObj.id,
        companyProjectName: item.companyProjectObj.name,
        companyProjectSn: item.companyProjectObj.sn
      };
    });
    if (!investList || !investList.length) {
      ElNotification({ type: 'warning', message: '明细不能为空' });
      return false;
    }
    let obj = {
      ...formData,
      dicEmployeeTypeCode: '4',
      dicDeptMarkCode: deptMarkCode,
      comProjectInvestList: investList,
      devProjectInvestList: tableData.value.devInvestList
    };
    return obj;
  }
};

const saveData = async type => {
  let res = await getData();
  if (!res) return Promise.reject();

  // console.log('deptId', deptId);
  if (!props.id) {
    let resData = await openBusiness(
      'ERM_OUTSOURCE_EMPLOYEES_SHARING_ADD',
      {
        ...res,
        deptName,
        deptId
      },
      '',
      '外包员工成本分摊新增'
    );
    let { id, businessId } = resData.businessData.employeeCostSharingVo;
    emits('updateID', {
      id,
      businessId: businessId || props.businessId
    });
    emits('updateInfo', resData.businessData.employeeCostSharingVo);
  } else if (props.id && type == 'ts') {
    await employeeCostNewHttp({
      ...res,
      deptName,
      deptId,
      id: props.id,
      isSubmit: false
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject();
  if (props.id && type == 'submit') {
    await employeeCostNewHttp({
      ...res,
      id: props.id,
      deptName,
      deptId,
      isSubmit: true
    });
    await props.callBackFn();
  }
  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  return Promise.resolve({});
};
//保存
const ts = () => {
  return saveData('ts');
};

//保存
const submit = () => {
  return saveData('submit');
};

const getDataList = async isRegular => {
  let flag = true;
  tableCellStatus.value = {};
  if (!isRegular) {
    tableData.value.dataList.forEach(item => {
      if (!item.companyProjectObj || !item.companyProjectObj?.id) {
        flag = false;
        tableCellStatus.value[`companyProjectObj_${item.id}`] = {
          err: true,
          msg: '必填'
        };
      }
    });
  }

  if (!flag) return Promise.reject();

  let data = tableData.value.dataList.map(item => {
    return {
      ...item,
      companyProjectId: item.companyProjectObj.id,
      companyProjectName: item.companyProjectObj.name,
      companyProjectSn: item.companyProjectObj.sn
    };
  });
  return data;
};

const resetData = async data => {
  await getSalaryDetailInfo(data);
};
defineExpose({
  submit,
  ts,
  getDataList,
  resetData
});
</script>
<style lang="scss" scoped>
:deep(.v_group) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  position: relative;
}
:deep(.v_err) {
  font-size: 12px;
  color: red;
  line-height: 1;
  padding-top: 2px;
  /* transform:scale(0.8) */
}
:deep(.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}
:deep(.el-input__inner) {
  text-align: left;
}
.musterW {
  display: flex;
  gap: 5px;
}

.musterW > i {
  color: red;
}
</style>
