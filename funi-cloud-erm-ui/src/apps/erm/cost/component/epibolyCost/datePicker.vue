<template>
  <div style="width: 100%">
    <el-date-picker v-model="modelVal" value-format="YYYY-MM" type="month" placeholder="请选择年月" @change="change" />
  </div>
</template>

<script lang="jsx" setup>
import { computed, watch, ref, onMounted } from 'vue';
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
});
const emits = defineEmits(['update:modelValue', 'change']);
const modelVal = ref();
console.log(modelVal.value);

const change = e => {
  emits('change', e);
};

watch(
  () => modelVal.value,
  newVal => {
    emits('change', newVal);
  },
  {
    immediate: true
  }
);
watch(
  () => props.modelValue,
  newVal => {
    console.log(props.modelValue);
    modelVal.value = newVal;
  },
  {
    immediate: true
  }
);

onMounted(() => {
  modelVal.value = $utils.Date().subtract(1, 'months').format('YYYY-MM');
});
</script>
<style lang="scss" scoped></style>
