<template>
  <funi-dialog
    class="loading-class"
    v-model="visible"
    :onConfirm="onConfirm"
    :onCancel="onCancel"
    @close="onCancel"
    title="生成绩效"
  >
    <funi-form :schema="scheme" :col="2" @get-form="setForm" :rules="rules" />
    <p style="color: #f59a23; font-size: 14px; padding-left: 12px" v-if="checkStatus">
      注：当前选择月份已生成绩效数据，不允许再次发起业务！
    </p>
  </funi-dialog>
</template>

<script lang="jsx" setup>
import { computed, ref, reactive } from 'vue';
import { ElLoading, ElNotification } from 'element-plus';
import { openBusiness } from '@/apps/erm/config/business.js';
import {
  getPerformanceRequestListHttp,
  isHaveRepeatAccrualHttp
} from '@/apps/erm/cost/hooks/performance_provision/perforProvisionApi.jsx';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['close', 'confirm']);
const visible = computed(() => props.modelValue);

let checkStatus = ref(false);

const data = reactive({
  formData: null
});
const setForm = e => {
  data.formData = e;
};

const scheme = [
  {
    label: '请选择生成绩效类型',
    component: () => {
      return (
        <el-radio-group>
          <el-radio label={'1'}>平时绩效</el-radio>
          <el-radio label={'2'}>年终绩效</el-radio>
        </el-radio-group>
      );
    },
    prop: 'dicPerformanceTypeCode',
    colProps: {
      span: 24
    }
  },
  {
    label: '请选择生成绩效月份',
    component: 'el-date-picker',
    prop: 'makeTime',
    colProps: {
      span: 24
    },
    props: {
      type: 'monthrange',
      'range-separator': '-',
      'start-placeholder': '请选择开始时间',
      'end-placeholder': '请选择结束时间',
      'value-format': 'YYYY-MM-DD',
      style: {
        width: '100%',
        'box-sizing': 'border-box'
      }
    }
  }
];

const rules = {
  dicPerformanceTypeCode: [{ required: true, message: '请选择生成绩效类型', trigger: 'change' }],
  makeTime: [{ required: true, message: '请选择生成绩效月份', trigger: 'change' }]
};

const onConfirm = async () => {
  let { isValid } = await data.formData.validate();
  if (isValid) {
    const loading = ElLoading.service({
      target: '.loading-class'
    });

    let res = data.formData.getValues();
    let months = [];
    let dateEnd = $utils.Date(res.makeTime[1]);
    let dateStart = $utils.Date(res.makeTime[0]);
    while (dateEnd > dateStart || dateStart.format('M') === dateEnd.format('M')) {
      months.push(dateStart.format('YYYY-MM'));
      dateStart.add(1, 'month');
    }
    let obj = {
      dicPerformanceTypeCode: res.dicPerformanceTypeCode,
      startTime: res.makeTime[0],
      endTime: res.makeTime[1],
      months: months
    };
    let dataList = await getPerformanceRequestListHttp(obj).catch(() => {
      loading.close();
      return;
    });
    let promiseList = await Promise.allSettled(
      dataList.list.map(item => {
        Reflect.deleteProperty(item, 'performanceSpecificsList');

        console.log(item, '232323232');
        return openBusiness('ERM_PERFORMANCE_ACCRUAL_ADD', item, void 0, '绩效自动计提-新增');
      })
    );
    let fulfilledList = promiseList.filter(item => item.status === 'fulfilled').map(item => item.value);
    ElNotification({
      title: `成功${fulfilledList.length}条,失败${dataList.list.length - fulfilledList.length}`,
      type: 'success'
    });
    loading.close();
    visible.value = false;
    emits('confirm');
  }
};

const onCancel = () => {
  visible.value = false;
  emits('close');
};
</script>
<style lang="scss" scoped></style>
