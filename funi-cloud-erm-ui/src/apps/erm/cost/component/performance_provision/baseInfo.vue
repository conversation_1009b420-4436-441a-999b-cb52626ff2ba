<template>
  <div class="base_info">
    <GroupTitle title="基本信息" />
    <funi-form :schema="schema" :col="3" @get-form="setForm" :rules="rules" />
    <GroupTitle title="绩效明细" />
    <div style="display: flex; margin-top: 20px; padding-left: 10px; height: 34px">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="项目编号:">
          <el-input v-model="searchForm.projectSn" placeholder="请输入项目编号" clearable style="width: 160px" />
        </el-form-item>
        <el-form-item label="合同编号:">
          <el-input v-model="searchForm.contractSn" placeholder="请输入合同编号" clearable style="width: 160px" />
        </el-form-item>
        <el-form-item label="确收类型:">
          <ErmSelect :code="dicCode.confirmModeCode" v-model="searchForm.dicConfirmModeCode" />
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="exportFn">导出</el-button>
        <Upload
          v-if="isEdit"
          :callbackFun="reset"
          title="导入绩效明细"
          url="/erm/performanceAccrualNewList/uploadPerformanceSpecificsExcel"
          type="success"
          onlyUpload
        >
          导入
        </Upload>
      </div>
    </div>
    <div class="table_wrapper">
      <!-- <funi-curd row-key="uuId" :data="tableList" :columns="column" :pagination="false" :max-height="600" /> -->
      <funi-curd
        ref="pageList"
        row-key="uuId"
        :lodaData="lodaData"
        :columns="column"
        :pagination="true"
        :max-height="600"
      />
    </div>
    <GroupTitle title="要件信息" v-if="bizName === '详情' || bizName === '审核'" />
    <funi-file-table v-if="(bizName === '详情' || bizName === '审核') && businessId" :params="fileParams" onlyShow />
  </div>
</template>

<script lang="jsx" setup>
import { ref, nextTick, inject } from 'vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { scheme } from '@/apps/erm/cost/hooks/performance_provision/usePerforProvisionInfo';
import { computed, onMounted, reactive } from 'vue';
import { dicCode } from '@/apps/erm/config/config.jsx';
import Upload from '@/apps/erm/component/upload/upload.vue';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';

import {
  performanceAccrualInfoHttp,
  querySpecificsListForAccrualHttp,
  apiUrl,
  performanceAccrualNewHttp
} from '@/apps/erm/cost/hooks/performance_provision/perforProvisionApi.jsx';
import { useColumns, useRules } from '@/apps/erm/cost/hooks/performance_provision/usePerforProvisionInfo.jsx';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
import { ElNotification } from 'element-plus';

const props = defineProps({
  ...baseInfoProps
});

const fileParams = reactive({
  businessId: props.businessId
});
const pageList = ref();
const emits = defineEmits(['updateInfo']);

const lodaData = async params => {
  loadingStatus.value.status = true;
  editList.value = [];
  let res = await querySpecificsListForAccrualHttp({
    ...searchForm.value,
    ...params,
    performanceAccrualId: props.id,
    isPeacetime: dicPerformanceTypeCode.value == '1' ? true : false
  });
  loadingStatus.value.status = false;
  return res;
};

const searchForm = ref({
  projectSn: void 0,
  contractSn: void 0,
  dicConfirmModeCode: void 0
});

let tableList = ref([]);
const contactRef = ref();
let deptName = ref();
let dicPerformanceTypeCode = ref();
let editList = ref([]);
let tableListTotal = ref(0);
const data = reactive({
  formData: null
});
const setForm = e => {
  data.formData = e;
};
const loadingStatus = inject('loadingStatus');
// const search = async () => {
//   loadingStatus.value.status = true;
//   await querySpecificsListForAccrualHttp({ ...searchForm.value, performanceAccrualId: props.id })
//     .then(res => {
//       tableList.value = res.list;
//       tableListTotal.value = res.list.length;
//     })
//     .finally(() => {
//       loadingStatus.value.status = false;
//     });
// };

const search = async () => {
  pageList.value.doRequest({ ...searchForm.value });
  // console.log(, 'pageList.value');
  // pageList.value.
  // await lodaData();
  // loadingStatus.value.status = true;
  // let res = await querySpecificsListForAccrualHttp({ ...searchForm.value, performanceAccrualId: props.id });
  // return res;
  // .then(res => {
  //   tableList.value = res.list;
  //   tableListTotal.value = res.list.length;
  // })
  // .finally(() => {
  //   loadingStatus.value.status = false;
  // });
};
const reset = async () => {
  loadingStatus.value.status = true;
  searchForm.value = {
    projectSn: void 0,
    contractSn: void 0,
    dicConfirmModeCode: void 0
  };
  pageList.value.doRequest({ ...searchForm.value });
  // await lodaData();
  // await querySpecificsListForAccrualHttp({ ...searchForm.value, performanceAccrualId: props.id })
  //   .then(res => {
  //     tableList.value = res.list;
  //   })
  //   .finally(() => {
  //     loadingStatus.value.status = false;
  //   });
};

//导出
const exportFn = () => {
  expotrFunction({
    url: apiUrl.queryPerformanceAccrualListExport,
    params: { performanceAccrualId: props.id, ...searchForm.value },
    FileName: `绩效自动计提-绩效明细`
  });
};
const validateTotal = tag => {
  console.log(tag, 'qqqq');
  if (tag == 'Ps') {
    let total = 0;
    tableList.value.forEach(item => {
      total += item.busActualKpiTotalPs || 0;
    });
    //有值的length
    let l = tableList.value.filter(item => {
      if (item.busActualKpiTotalPs) return item;
    }).length;
    let busActualPerformance = data.formData.getValues()?.busActualPerformancePs;
    if (l < tableList.value.length) {
      if (busActualPerformance <= total) {
        ElNotification({
          type: 'warning',
          message: '业务实发绩效(平时)金额大于总业务实发绩效(平时)，请核对！',
          duration: 0
        });
        return false;
      } else {
        return true;
      }
    } else {
      if (busActualPerformance < total) {
        ElNotification({
          type: 'warning',
          message: '业务实发绩效(平时)金额大于总业务实发绩效(平时)，请核对！',
          duration: 0
        });
        return false;
      } else {
        return true;
      }
    }
  } else {
    let total = 0;
    tableList.value.forEach(item => {
      total += item.busActualKpiTotalNz || 0;
    });
    //有值的length
    let l = tableList.value.filter(item => {
      if (item.busActualKpiTotalNz) return item;
    }).length;
    let busActualPerformance = data.formData.getValues()?.busActualPerformanceNz;

    if (l < tableList.value.length) {
      console.log(11);
      if (busActualPerformance <= total) {
        ElNotification({
          type: 'warning',
          message: '业务实发绩效(年终)金额大于总业务实发绩效(年终)，请核对！',
          duration: 0
        });
        return false;
      } else {
        return true;
      }
    } else {
      console.log(22);
      if (busActualPerformance < total) {
        ElNotification({
          type: 'warning',
          message: '业务实发绩效(年终)金额大于总业务实发绩效(年终)，请核对！',
          duration: 0
        });
        return false;
      } else {
        return true;
      }
    }
  }
};

const iptChange = (index, name, e) => {
  pageList.value.tableData[index][name] = e;

  // tableData
  // console.log(232332);
  // tableList.value[index][name] = e;
  let curId = pageList.value.tableData[index].id;
  let idx = editList.value.findIndex(item => item.id == curId);
  if (idx > -1) {
    editList.value[idx][name] = e;
  } else {
    editList.value.push(pageList.value.tableData[index]);
  }
};

const schema = computed(() => {
  return scheme({
    isEdit: props.isEdit,
    iptBlurChange: validateTotal,
    dicPerformanceTypeCode: dicPerformanceTypeCode.value
  });
});
const rules = computed(() => {
  return useRules({
    isEdit: props.isEdit,
    iptBlurChange: validateTotal
  });
});

const column = computed(() => {
  return useColumns({
    isEdit: props.isEdit,
    iptChange,
    iptBlurChange: validateTotal,
    contactRef,
    deptName,
    dicPerformanceTypeCode
  });
});

const getPerformanceInfo = async () => {
  loadingStatus.value.status = true;
  let res = await performanceAccrualInfoHttp({ performanceAccrualId: props.id }).finally(() => {
    loadingStatus.value.status = false;
  });
  data.formData.setValues(res.performanceAccrualNewVo);
  deptName.value = res.performanceAccrualNewVo.performanceDeptName;

  dicPerformanceTypeCode.value = res.performanceAccrualNewVo.dicPerformanceTypeCode;

  tableList.value = res.performanceSpecificsList;
  emits('updateInfo', res.performanceAccrualNewVo);
};

const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await data.formData.validate();
  if (isValid) {
    let formData = data.formData.getValues();
    let obj = {
      ...formData,
      performanceSpecificsList: editList.value.map(item => {
        return {
          id: item.id,
          busActualKpiTotalPs: item.busActualKpiTotalPs,
          busActualKpiTotalNz: item.busActualKpiTotalNz
        };
      })
    };
    return obj;
  } else {
    return false;
  }
};

const saveData = async type => {
  console.log(type);
  let data = await getData(type);
  if (!data) return Promise.reject();
  await performanceAccrualNewHttp({
    ...data,
    id: props.id,
    isSubmit: !type ? true : false
  });
  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  pageList.value.doRequest({ ...searchForm.value });
  return Promise.resolve({});
};

//暂存
const ts = () => {
  return saveData('ts');
};
//保存
const nextStep = () => {
  return saveData();
};

defineExpose({
  ts,
  nextStep
});

onMounted(async () => {
  await nextTick();
  if (props.id) {
    getPerformanceInfo();
  }
});
</script>
<style lang="scss" scoped>
.base_info {
  // padding: 20px;
  box-sizing: border-box;
}

.table_wrapper :deep(.el-table__body-wrapper .el-scrollbar__bar.is-horizontal) {
  display: inline-block !important;
}
.table_wrapper :deep(.el-scrollbar__bar.is-vertical > div) {
  display: none !important;
}
</style>
