import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import { apiUrl } from '@/apps/erm/cost/hooks/epibolyCost/epibolyCostApi.jsx';
import { erm_intl } from '@/apps/erm/hooks/intl';

export const useSchema = ({ deptMarkCode, dicEmployeeTypeCode }) => {
  return [
    {
      label: '年度',
      component: null,
      prop: 'year'
    },
    {
      label: '月份',
      component: null,
      prop: 'month'
    },
    {
      label: '分摊前投入(元)',
      component: null,
      prop: 'investAmount'
    },
    {
      label: '归属部门',
      component: null,
      prop: 'deptName',
      hidden: () => {
        let flag = false;
        if (deptMarkCode == '1' && dicEmployeeTypeCode !== '1') flag = true;
        return flag;
      }
    },
    {
      label: '归属公司',
      component: null,
      prop: 'companyName',
      hidden: () => {
        let flag = false;
        if (deptMarkCode == '1' && dicEmployeeTypeCode !== '1') flag = true;
        return flag;
      }
    },
    {
      label: '创建时间',
      component: null,
      prop: 'createTime'
    },
    {
      label: '创建人',
      component: null,
      prop: 'creatorName'
    },
    {
      label: '状态',
      component: null,
      prop: 'dicSharingStatusName'
    },
    {
      label: '研发绩效(元)',
      component: null,
      prop: 'devMeritsSalary',
      hidden: () => {
        let flag = false;
        if (dicEmployeeTypeCode == '4') flag = true;
        return flag;
      }
    },
    {
      label: '基础工资合计(元)',
      component: null,
      prop: 'basicSalaryAmount',
      hidden: () => {
        let flag = false;
        if (dicEmployeeTypeCode == '4') flag = true;
        return flag;
      }
    },
    {
      label: '津贴合计(元)',
      component: null,
      prop: 'allowanceAmount',
      hidden: () => {
        let flag = false;
        if (dicEmployeeTypeCode == '4') flag = true;
        return flag;
      }
    },
    {
      label: '住房公积金合计(元)',
      component: null,
      prop: 'compHousingFund',
      hidden: () => {
        let flag = false;
        if (dicEmployeeTypeCode == '4') flag = true;
        return flag;
      }
    },
    {
      label: '社会保险合计(元)',
      component: null,
      prop: 'compSocialSecurity',
      hidden: () => {
        let flag = false;
        if (dicEmployeeTypeCode == '4') flag = true;
        return flag;
      }
    }
  ];
};

export const useColumns = ({
  isEdit,
  deptMarkCode,
  type,
  tableCellStatus = {},
  dicEmployeeTypeCode,
  tableData,
  shareDate
}) => {
  let ary = [
    {
      label: '员工编号',
      prop: 'empSn'
    },
    {
      label: '员工姓名',
      prop: 'empName'
    },
    {
      label: '项目编号',
      prop: 'companyProjectSn'
    },
    {
      label: '项目名称',
      // prop: (isEdit || type === 'audit') && deptMarkCode == '0' ? 'companyProjectObj' : 'companyProjectName',
      width: 400,
      slots: {
        header: 'nameHead'
      },
      render: ({ row, index }) => {
        return (isEdit || type === 'audit') && deptMarkCode == '0' ? (
          <div class={tableCellStatus[`companyProjectObj_${row.id}`]?.err ? 'v_group is-error' : 'v_group'}>
            <InfiniteSelect
              api={apiUrl.queryValidProjectListForCostSharing}
              defaultProps={{
                keyWord: 'projectName',
                name: 'projectName',
                id: 'id'
              }}
              otherParams={{ dicEmployeeTypeCode: '4', shareDate }}
              multiple={false}
              disabled={!row.edit}
              modelValue={row.companyProjectObj}
              isUseTable={true}
              onChange={(e, obj) => {
                row.companyProjectObj = {
                  id: obj.id,
                  name: obj.projectName,
                  sn: obj.projectSn
                };

                row.companyProjectSn = obj.projectSn;
                // let idx = tableData.value.dataList.findIndex(item => item.empSn == row.empSn);
                // tableData.value.dataList[idx].companyProjectObj = {
                //   id: obj.id,
                //   name: obj.projectName,
                //   sn: obj.projectSn
                // };
                console.log(tableData, 'tableData');

                setTableCellStatus(`companyProjectObj`, type, tableCellStatus, row);
              }}
            ></InfiniteSelect>
            <div class="v_err">{tableCellStatus[`companyProjectObj_${row.id}`]?.msg}</div>
          </div>
        ) : (
          <span> {row.companyProjectName}</span>
        );
      }
    },
    {
      label: '年度',
      prop: 'year'
    },
    {
      label: '月份',
      prop: 'month'
    },
    {
      label: '投入天数(元)',
      prop: 'investDay',
      align: 'right'
    },
    {
      label: '投入(元)',
      prop: 'investAmount',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.investAmount);
      }
    },
    ...(dicEmployeeTypeCode == '1'
      ? //  [
        //     {
        //       label: '工资(元)',
        //       prop: 'salary',
        //       align: 'right',
        //       render: ({ row }) => {
        //         return erm_intl(row.salary);
        //       }
        //     },
        //     {
        //       label: '五险一金(元)',
        //       prop: 'socialSecurityHousingFund',
        //       align: 'right',
        //       render: ({ row }) => {
        //         return erm_intl(row.socialSecurityHousingFund);
        //       }
        //     },
        //     {
        //       label: '研发绩效(元)',
        //       prop: 'meritsSalary',
        //       align: 'right',
        //       render: ({ row }) => {
        //         return erm_intl(row.meritsSalary);
        //       }
        //     }
        //   ]
        [
          {
            label: '投入类型',
            prop: 'dicInvestTypeName'
          },
          {
            label: '工资(元)',
            prop: 'salary'
          },
          {
            label: '基础工资(元)',
            prop: 'basicSalary'
          },
          {
            label: '津贴(元)',
            prop: 'allowance'
          },
          {
            label: '住房公积金代扣额(元)',
            prop: 'compHousingFund'
          },
          {
            label: '养老保险代扣额(元)',
            prop: 'compEndowInsurance'
          },
          {
            label: '失业保险代扣额(元)',
            prop: 'compUnempInsurance'
          },
          {
            label: '工伤保险代扣额(元)',
            prop: 'compEmpInjuryInsurance'
          },
          {
            label: '生育保险代扣额(元)',
            prop: 'compMaternityInsurance'
          },
          {
            label: '大病保险代扣额(元)',
            prop: 'compCriticalIllnessInsurance'
          },
          {
            label: '医疗保险代扣额(元)',
            prop: 'compMedicalInsurance'
          },
          {
            label: '研发绩效(元)',
            prop: 'meritsSalary',
            render: ({ row }) => {
              return erm_intl(row.meritsSalary);
            }
          },
          {
            label: '社会保险合计(元)',
            prop: 'compSocialSecurity',
            render: ({ row }) => {
              return erm_intl(row.compSocialSecurity);
            }
          }
        ]
      : [])
  ];
  if (deptMarkCode == '1' && dicEmployeeTypeCode !== '1') {
    ary.push(
      {
        label: '归属公司',
        prop: 'companyName'
      },
      {
        label: '归属部门',
        prop: 'deptName'
      }
    );
  }

  return ary;
};

const setTableCellStatus = (name, type, tableCellStatus, row) => {
  if (type === 'audit') {
    if (!row[name] || !row[name].id) {
      tableCellStatus[`${name}_${row.id}`] = {
        err: true,
        msg: '必填'
      };
    } else {
      tableCellStatus[`${name}_${row.id}`] = void 0;
    }
  }
};
