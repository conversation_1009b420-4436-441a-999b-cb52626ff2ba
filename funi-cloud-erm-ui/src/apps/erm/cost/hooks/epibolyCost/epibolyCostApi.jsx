export const apiUrl = {
  queryEmployeeCostSharingList: 'erm/employeeCostSharingList/queryEmployeeCostSharingList',
  querySalaryDetailList: 'erm/employeeCostSharing/querySalaryDetailList',
  queryCostSharingInfo: 'erm/employeeCostSharing/info',
  queryValidProjectListForCostSharing: 'erm/projectManagementList/queryValidProjectListForCostSharing',
  employeeCostNew: 'erm/employeeCostSharing/new',
  cheackCost: 'erm/employeeCostSharing/checkCostSharingByMonth',
  employeeCostSharingListExport: 'erm/employeeCostSharingList/queryEmployeeCostSharingListExport',
  queryInvestDetailListByParameter: 'erm/investmentCompanyProjectDetailList/queryInvestDetailListByParameter',
  queryInvestTypeCodeList: 'erm/investmentCompanyProjectDetailList/queryInvestTypeCodeList',
  queryCurrentMarkDeptTree: 'erm/dept/queryCurrentMarkDeptTree'
};

export const queryEmployeeCostSharingListHttp = params => {
  return $http.post(apiUrl.queryEmployeeCostSharingList, params);
};
export const querySalaryDetailListHttp = params => {
  return $http.post(apiUrl.querySalaryDetailList, params);
};
export const queryCostSharingInfoHttp = params => {
  return $http.fetch(apiUrl.queryCostSharingInfo, params);
};
export const employeeCostNewHttp = params => {
  return $http.post(apiUrl.employeeCostNew, params);
};
export const checkCostHttp = params => {
  return $http.post(apiUrl.cheackCost, params);
};
export const queryInvestDetailListByParameterHttp = params => {
  return $http.post(apiUrl.queryInvestDetailListByParameter, params);
};
export const queryCurrentMarkDeptTreeHttp = params => {
  return $http.fetch(apiUrl.queryCurrentMarkDeptTree, params);
};
