import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';

export const useColumns = (seeDetails, editFn, examineFn, deleFn, seeBusinessInfo) => {
  return [
    {
      type: 'selection',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '分摊核算编号',
      prop: 'sharingSn',
      fixed: 'left',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => seeDetails(row)}
            auth={'ERM_EPIBOLY_COST_INFO'}
            text={row.sharingSn}
          ></HyperlinkInfo>
        );
      }
    },
    {
      label: '年度',
      prop: 'year'
    },
    {
      label: '月份',
      prop: 'month'
    },
    {
      label: '状态',
      prop: 'dicSharingStatusName'
    },
    {
      label: '分摊前投入(元)',
      prop: 'investAmount',
      align: 'right'
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '类型',
      prop: 'deptEmployeeType'
    },
    {
      label: '操作',
      prop: 'operationBtn',
      align: 'center',

      fixed: 'right',
      render: ({ row, index }) => {
        let operationBtn = {
          DEL: (
            <el-popconfirm
              title="确定删除？"
              width="220"
              onConfirm={() => {
                deleFn(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_EPIBOLY_COST_DEL'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => editFn(row)}
              auth={'ERM_EPIBOLY_COST_EDIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => examineFn(row)}
              auth={'ERM_EPIBOLY_COST_AUDIT'}
              text={'审核'}
            ></Hyperlink>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={() => seeBusinessInfo(row)}
              auth={'ERM_EPIBOLY_COST_INFO'}
              text={'业务详情'}
            ></Hyperlink>
          )
        };
        let btnList = [];
        btnList = $utils.clone(row.buttonList, true);
        if (!$utils.isNil(row.dicApportDoingBusCode) && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO');
        }
        return (
          <div style="display:flex;justify-content: space-around;align-items: center;">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

export const useBtnsConfig = (addFn, exportFn) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_EPIBOLY_COST_ADD" onClick={addFn} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_EPIBOLY_COST_EXPORT" onClick={exportFn} type="primary">
          导出
        </el-button>
      )
    }
  ];
};
