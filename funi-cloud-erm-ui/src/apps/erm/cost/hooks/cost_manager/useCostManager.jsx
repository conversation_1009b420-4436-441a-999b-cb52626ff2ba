/**
 * @description 人事管理-员工信息  表格Columns配置
 */
import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import { erm_intl } from '@/apps/erm/hooks/intl';
export const useColumns = seeDetails => {
  return [
    {
      label: '编号',
      prop: 'costSn',
      fixed: 'left',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => seeDetails(row)}
            auth={'ERM_COST_MANAGER_INFO'}
            text={row.costSn}
          ></HyperlinkInfo>
        );
      }
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },

    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '项目名称',
      prop: 'projectName'
    },
    {
      label: '期数',
      prop: 'issue'
    },
    {
      label: '应计金额(元)',
      prop: 'accruedPerformAmount',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.accruedPerformAmount);
      }
    },
    {
      label: '应发金额(元)',
      prop: 'shouldPerformAmount',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.shouldPerformAmount);
      }
    },
    //todo
    // {
    //   label: '实发金额(元)',
    //   prop: 'preCapAmount',
    //   align: 'right',
    //   render: ({ row }) => {
    //     return erm_intl(row.preCapAmount);
    //   }
    // },
    {
      label: '成本金额(元)',
      prop: 'costAmount',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.costAmount);
      }
    },

    {
      label: '成本类型',
      prop: 'dicCostTypeName'
    },
    // {
    //   label: '是否进损益',
    //   prop: 'isProfitLoss'
    // },
    {
      label: '业务编号',
      prop: 'businessSn'
    },
    {
      label: '状态',
      prop: 'dicCostStatusName'
    }
  ];
};

/**
 * @description 项目管理 按钮配置
 */
export const useBtnsConfig = (exportFn, setYearEnd) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_COST_MANAGER_EXPORT" onClick={exportFn} type="primary">
          导出
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_COST_TYING_ ACCOUNT" onClick={setYearEnd} type="primary">
          年底扎帐
        </el-button>
      )
    }
  ];
};
