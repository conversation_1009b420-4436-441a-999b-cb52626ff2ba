export const apiUrl = {
  queryCostApportionInfoList: 'erm/costInfoList/queryCostInfoList',
  costInfo: 'erm/costInfo/info',
  queryCostInfoListExport: 'erm/costInfoList/queryCostInfoListExport',
  setYearEndDay: 'erm/config/setYearEndDay'
};
// 成本管理列表接口
export const queryCostApportionInfoListHttp = params => {
  return $http.post(apiUrl.queryCostApportionInfoList, params);
};
// 成本管理详情接口
export const costInfoHttp = params => {
  return $http.fetch(apiUrl.costInfo, params);
};
// 年底扎帐
export const setYearEndDay = params => {
  return $http.fetch(apiUrl.setYearEndDay, params);
};

// // 成本管理导出
// export const queryCostInfoListExportHttp = params => {
//   return $http.post(apiUrl.queryCostInfoListExport, params);
// };
