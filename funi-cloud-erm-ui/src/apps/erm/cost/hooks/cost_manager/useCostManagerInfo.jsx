import MoneyInput from '@/apps/erm/component/inputMoneyNumber/index.vue';

const scheme = record => {
  return [
    {
      label: '编号',
      component: null,
      prop: 'costSn'
    },
    {
      label: '状态',
      component: null,
      prop: 'dicCostStatusName'
    },
    {
      label: '产生时间',
      component: null,
      prop: 'createTime'
    },
    {
      label: '成本类型',
      component: null,
      prop: 'dicCostTypeName'
    },
    {
      label: '归属公司',
      component: null,
      prop: 'companyName'
    },
    {
      label: '归属部门',
      component: null,
      prop: 'deptName'
    },
    {
      label: '项目名称',
      component: ({ formModel }) => (
        <el-button
          link
          type="primary"
          onClick={() => {
            record.seeProjectInfo?.();
          }}
        >
          {formModel.projectName}
        </el-button>
      ),
      prop: 'projectName'
    },

    {
      label: '是否进损益',
      component: null,
      prop: 'isProfitLoss'
    },
    {
      label: '成本金额(元)',
      component: null,
      prop: 'costAmount'
    },
    {
      label: '应计金额(元)',
      component: () => <MoneyInput />,
      props: {
        isEdit: false,
        moneyCapitalShow: true,
        unit: '元'
      },
      prop: 'accruedPerformAmount'
    },
    {
      label: '应发金额(元)',
      component: () => <MoneyInput />,
      props: {
        isEdit: false,
        moneyCapitalShow: true,
        unit: '元'
      },
      prop: 'shouldPerformAmount'
    },
    {
      label: '含税金额(元)',
      component: () => <MoneyInput />,
      props: {
        isEdit: false,
        moneyCapitalShow: true,
        unit: '元'
      },
      prop: 'includeTaxAmount'
    },
    {
      label: '不含税金额(元)',
      component: () => <MoneyInput />,
      props: {
        isEdit: false,
        moneyCapitalShow: true,
        unit: '元'
      },
      prop: 'excludeTaxAmount'
    },
    {
      label: '税额(元)',
      component: () => <MoneyInput />,
      props: {
        isEdit: false,
        moneyCapitalShow: true,
        unit: '元'
      },
      prop: 'taxAmount'
    },
    //TODO:
    {
      label: '业务编号',
      component: ({ formModel }) => (
        <el-button
          link
          type="primary"
          onClick={() => {
            record.seeBusinessInfo?.(formModel);
          }}
        >
          {formModel.businessSn}
        </el-button>
      ),
      prop: 'businessSn'
    },
    {
      label: '来源',
      component: null,
      prop: 'costSourceDesc'
    },
    {
      label: '期数',
      component: null,
      prop: 'issue'
    }
  ];
};

export { scheme };
