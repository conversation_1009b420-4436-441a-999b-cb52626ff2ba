export const apiUrl = {
  queryCapProjectCostApportionList: 'erm/capProjectCostApportionList/queryCapProjectCostApportionList',
  querycapProjectCostInfo: 'erm/capProjectCostApportion/info',
  queryProjectForBudgetPlans: 'erm/projectManagementList/queryProjectForBudgetPlans',
  queryCostApportionInfoList: 'erm/costApportionInfoList/queryCostApportionInfoList',
  capProjectCostApportionNew: 'erm/capProjectCostApportion/new',
  queryProjectForCap: 'erm/projectManagementList/queryProjectForCap',
  costApportionDele: 'erm/capProjectCostApportion/delete',
  creatBus: 'erm/capProjectCostApportion/creatBus',
  queryCostApportionInfoListExport: 'erm/costApportionInfoList/queryCostApportionInfoListExport'
};
//资本化成本分摊明细列表
export const queryCapProjectCostApportionListHttp = params => {
  return $http.post(apiUrl.queryCapProjectCostApportionList, params);
};
//资本化成本分摊明细详情
export const querycapProjectCostInfoHttp = params => {
  return $http.fetch(apiUrl.querycapProjectCostInfo, params);
};
//资本化成本分摊关联项目列表
export const queryProjectForBudgetPlansHttp = params => {
  return $http.post(apiUrl.queryProjectForBudgetPlans, params);
};
//资本化成本分摊明细列表
export const queryCostApportionInfoListHttp = params => {
  return $http.post(apiUrl.queryCostApportionInfoList, params);
};
//资本化成本分摊明细 新增
export const capProjectCostApportionNewHttp = params => {
  return $http.post(apiUrl.capProjectCostApportionNew, params);
};
//资本化成本分摊添加经营性项目列表接口
export const queryProjectForCapHttp = params => {
  return $http.post(apiUrl.queryProjectForCap, params);
};
//资本化成本分摊删除
export const costApportionDeleHttp = params => {
  return $http.fetch(apiUrl.costApportionDele, params);
};
//资本化成本分摊删除
export const creatBusHttp = params => {
  return $http.post(apiUrl.creatBus, params);
};
