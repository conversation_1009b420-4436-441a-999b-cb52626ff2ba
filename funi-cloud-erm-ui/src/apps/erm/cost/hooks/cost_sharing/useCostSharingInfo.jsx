import Verifics from '@/apps/erm/component/verifica/index.vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import MoneyInput from '@/apps/erm/component/inputMoneyNumber/index.vue';
import { erm_intl } from '@/apps/erm/hooks/intl';
import ermHooks from '@/apps/erm/hooks/index.js';
// import { ElNotification } from 'element-plus';

/**
 * @description 资本化项目成本分摊-基本信息 表单scheme
 */

//新增表单scheme
const useAddScheme = record => {
  return [
    {
      label: '分摊编号',
      component: null,
      prop: 'apportSn'
    },
    {
      label: '状态',
      component: null,
      prop: 'dicApportStatusName'
    },
    {
      label: '项目名称',
      component: record.isEdit
        ? () => (
            <InfiniteSelect
              api={ermGlobalApi.queryProjectListByName}
              defaultProps={{
                keyWord: 'projectName',
                name: 'projectName',
                id: 'id'
              }}
              maxWidth={500}
              onChange={(e, obj, o) => {
                record.formInstance.setValues({
                  projectCostTotal: o.projectTotalCostAmount,
                  companyId: o.companyId,
                  companyName: o.companyName,
                  deptId: o.deptId,
                  deptName: o.deptName,
                  projectName: o.projectName,
                  projectId: o.id,
                  projectSn: o.projectSn,
                  apportCycleAry: [],
                  preApportionAmount: void 0
                });
              }}
            ></InfiniteSelect>
          )
        : null,
      prop: record.isEdit ? 'projectObj' : 'projectName',
      props: {
        style: { width: '100%' }
      },
      hidden: () => {
        if (record.tab == '变更' || record.isEdit) return false;
      }
    },
    {
      label: '项目成本共计(元)',
      component: () => <MoneyInput />,
      props: {
        isEdit: false,
        moneyCapitalShow: true,
        unit: '元'
      },
      prop: 'projectCostTotal'
    },
    {
      label: '分摊周期',
      component: record.isEdit ? () => 'el-date-picker' : null,
      prop: record.isEdit ? 'apportCycleAry' : 'apportCycle',
      props: {
        style: { width: '100%' },
        type: 'monthrange',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      on: {
        change: e => {
          let startStamp = new Date(e[0]).getTime();
          let endStamp = new Date(e[1]).getTime();
          let end = $utils.Date(endStamp);
          let start = $utils.Date(startStamp);
          let subMonth = end.diff(start, 'month');
          let month = Math.ceil(subMonth) + 1 || 1;
          let data = record.formInstance.getValues();
          let preApportionAmount =
            Math.round(((data.projectCostTotal ? data.projectCostTotal : 0) / month) * 100).toFixed(2) / 100;
          record.formInstance.setValues({ preApportionAmount });
        }
      }
    },
    {
      label: '每期分摊金额(元)',
      component: () => <MoneyInput />,
      props: {
        isEdit: false,
        moneyCapitalShow: true,
        unit: '元'
      },
      prop: 'preApportionAmount'
    },
    {
      label: '备注',
      component: record.isEdit ? 'el-input' : null,
      prop: 'remark',
      props: {
        style: { width: '100%' },
        type: 'textarea',
        maxlength: 1000
      }
    }
  ];
};

const useChangeScheme = record => {
  return [
    {
      label: '分摊编号',
      component: null,
      prop: 'apportSn'
    },
    {
      label: '状态',
      component: null,
      prop: 'dicApportStatusName'
    },
    {
      label: '创建时间',
      component: null,
      prop: 'createTime',
      props: {
        style: { width: '100%' }
      },
      hidden: () => {
        if (record.tab == '变更') return true;
      }
    },
    {
      label: '生效时间',
      component: null,
      prop: 'takeEffectTime',
      props: {
        style: { width: '100%' }
      },
      hidden: () => {
        if (record.tab == '变更') return true;
      }
    },
    {
      label: '分摊项目',
      component:
        record.isEdit && record.tab != '变更'
          ? () => 'el-input'
          : ({ formModel }) => (
              <el-button
                link
                type="primary"
                onClick={() => {
                  record.seeProjectInfo?.();
                }}
              >
                {formModel.projectName}
              </el-button>
            ),
      prop: 'projectName'
    },
    {
      label: '项目成本共计(元)',
      component: () => <MoneyInput />,
      props: {
        isEdit: false,
        moneyCapitalShow: true,
        unit: '元'
      },
      prop: 'projectCostTotal'
    },
    {
      label: '分摊周期',
      component: record.isEdit && record.tab != '变更' ? () => 'el-date-picker' : null,
      prop: 'apportCycle',
      props: {
        style: { width: '100%' },
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      }
    },
    {
      label: '每期分摊金额(元)',
      component: () => <MoneyInput />,
      props: {
        isEdit: false,
        moneyCapitalShow: true,
        unit: '元'
      },
      prop: 'preApportionAmount'
    },
    {
      label: '已分摊金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'apportionedAmount',
      props: {
        isEdit: record.isEdit && record.tab != '变更',
        moneyCapitalShow: true,
        unit: '元'
      }
    },
    {
      label: '未分摊金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'unApportionedAmount',
      props: {
        isEdit: record.isEdit && record.tab != '变更',
        moneyCapitalShow: true,
        unit: '元'
      }
    },
    {
      label: '备注',
      component:
        record.isEdit || record.tab == '变更'
          ? 'el-input'
          : ({ formModel }) => {
              return (
                <span
                  style={{
                    'white-space': 'pre-wrap'
                  }}
                >
                  {formModel.remark || '--'}
                </span>
              );
            },
      prop: 'remark',
      props: {
        style: { width: '100%' },
        type: 'textarea',
        maxlength: 1000
      },
      extra: ({ formModel }) => {
        return ermHooks.compare(record.changeData, formModel, 'remark');
      }
    }
  ];
};

/**
 * @description 表单验证规则
 * @param {Boolean} isEdit
 * **/
const useRules = isEdit => {
  return {
    projectObj: [{ required: true, message: '必填', trigger: 'change' }],
    apportCycleAry: [{ required: true, message: '必填', trigger: 'blur' }]
  };
};

/**
 *
 * @desciption 资本化项目成本分摊-参与分摊项目
 *
 */
const useColumns = ({ isEdit, contactRef, iptChange, delDicItem }) => {
  const reset_v = (key, el) => {
    contactRef[key] = el;
  };
  return [
    {
      label: '项目名称',
      prop: 'projectName',
      align: 'center',
      render: ({ row, index }) => {
        let c = (
          <HyperlinkInfo
            row={row}
            index={index}
            // func={seeDetails}
            // auth={'ERM_COST_SHARING_DETAIL'}
            text={row.projectName}
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_projectName`, el);
            }}
            value={row.projectName}
            c={c}
            key={`${row.uuId}_projectName`}
          ></Verifics>
        ) : (
          <span>{row.projectName}</span>
        );
      }
    },
    {
      label: '累计已分摊金额(元)',
      prop: 'apportionTotalAmount',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.apportionTotalAmount);
      }
    },
    {
      label: '累计已确收金额(元)',
      prop: 'confirmTotalAmount',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.confirmTotalAmount);
      }
    },
    {
      label: '累计已收款金额(元)',
      prop: 'receiptTotalAmount',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.receiptTotalAmount);
      }
    },

    {
      label: '加入分摊时间',
      prop: 'addTime',
      align: 'center'
      // render: ({ row, index }) => {
      //   return row.addTime ? <span>{row?.addTime.split(' ')[0]}</span> : '--';
      // }
    },
    {
      label: '开始分摊时间',
      prop: 'startTime',
      align: 'center',
      render: ({ row, index }) => {
        return row.startTime ? <span>{row.startTime}</span> : '--';
      }
    },
    {
      label: '归属公司',
      prop: 'companyShortName',
      align: 'center',
      render: ({ row, index }) => {
        return row.companyShortName ? <span>{row.companyShortName}</span> : '--';
      }
    },
    {
      label: '归属部门',
      prop: 'deptName',
      align: 'center',
      render: ({ row, index }) => {
        return row.deptName ? <span>{row.deptName}</span> : '--';
      }
    },
    {
      label: '备注',
      prop: 'remark',
      align: 'center',
      width: '240px',
      render: ({ row, index }) => {
        let c = (
          <el-input
            onInput={e => {
              iptChange(index, 'remark', e);
            }}
            maxlength={200}
            placeholder="备注"
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_remark`, el);
            }}
            value={row.remark}
            c={c}
            key={`${row.uuId}_remark`}
            // rule={[{ required: true, message: '必填', trigger: 'blur' }]}
          ></Verifics>
        ) : (
          <span>{row.remark}</span>
        );
      }
    },
    {
      label: '操作',
      prop: '',
      align: 'center',
      fixed: 'right',
      render: ({ row, index }) => {
        return isEdit ? (
          <div>
            <el-popconfirm
              title="确定删除当前联系人？"
              width="220"
              onConfirm={() => {
                delDicItem(row, index);
              }}
            >
              {{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            </el-popconfirm>
          </div>
        ) : null;
      }
    }
  ];
};

//添加项目列表
const useProjectShipColumns = data => {
  return [
    {
      type: 'selection',
      width: '55px',
      fixed: 'left'
      // selectable: row => {
      //   if (data.findIndex(item => item.projectId == row.id) < 0) {
      //     return true;
      //   } else {
      //     return false;
      //   }
      // }
    },
    {
      label: '项目编号',
      prop: 'projectSn'
    },
    {
      label: '项目名称',
      prop: 'projectName'
    },
    {
      label: '归属公司',
      prop: 'companyShortName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '负责人',
      prop: 'employeeName'
    }
  ];
};

const useSearchConfig = () => {
  const style = {
    width: '100%'
  };
  return {
    border: false,

    schema: [
      {
        label: '项目名称',
        component: 'el-input',
        prop: 'projectName',
        props: {
          placeholder: '请输入项目名称'
        }
      },
      {
        label: '项目编号',
        component: 'el-input',
        prop: 'projectSn',
        props: {
          placeholder: '请输入项目编号'
        }
      }
    ]
  };
};

//分摊明细列表
const useCostApportColumns = () => {
  return [
    {
      label: '参与分摊项目名称',
      prop: 'projectName'
    },
    {
      label: '分摊月份',
      prop: 'issue'
    },
    {
      label: '分摊金额(元)',
      prop: 'apportAmount',
      align: 'right'
    },
    {
      label: '归属公司',
      prop: 'companyShortName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '分摊时间',
      prop: 'apportionTime'
    }
  ];
};

//分摊明细导出
const useBtnsConfig = exportFn => {
  return [
    //编辑
    {
      component: () => (
        <el-button v-auth={'ERM_COST_SHARING_DETAIL_EXPORT'} onClick={exportFn} type="primary">
          导出
        </el-button>
      )
    },
    //详情
    {
      component: () => (
        <el-button v-auth={'ERM_COST_SHARING_DETAIL_INFO_EXPORT'} onClick={exportFn} type="primary">
          导出
        </el-button>
      )
    }
  ];
};

//分摊明细查询
const useSharingDetailSearch = () => {
  return {
    border: false,
    schema: [
      {
        label: '关键字',
        component: 'el-input',
        prop: 'keyword',
        props: {
          placeholder: '请输入项目名称、归属部门、归属公司'
        }
      }
    ]
  };
};

export {
  useAddScheme,
  useChangeScheme,
  useColumns,
  useRules,
  useSearchConfig,
  useBtnsConfig,
  useProjectShipColumns,
  useCostApportColumns,
  useSharingDetailSearch
};
