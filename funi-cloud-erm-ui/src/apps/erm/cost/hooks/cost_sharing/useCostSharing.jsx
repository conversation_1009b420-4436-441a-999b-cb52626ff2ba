/**
 * @description 成本分摊 表格Columns配置
 */
import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import { erm_intl } from '@/apps/erm/hooks/intl';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';
import ermHooks from '@/apps/erm/hooks/index.js';
export const useColumns = (seeDetails, editFn, examineFn, deleFn, seeBusinessInfo) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '分摊编号',
      prop: 'dataTitle',
      fixed: 'left',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => seeDetails(row)}
            auth={'ERM_COST_SHARING_DETAIL'}
            text={row.dataTitle}
          ></HyperlinkInfo>
        );
      }
    },
    {
      label: '分摊项目',
      prop: 'projectName'
    },
    {
      label: '参与分摊项目',
      prop: 'joinCapProjectNameList'
    },
    {
      label: '总分摊金额(元)',
      prop: 'projectCostTotal',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.projectCostTotal);
      }
    },
    {
      label: '已分摊金额(元)',
      prop: 'capedAmount',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.capedAmount);
      }
    },
    {
      label: '每期分摊金额(元)',
      prop: 'preCapAmount',
      align: 'right',
      render: ({ row }) => {
        return erm_intl(row.preCapAmount);
      }
    },
    {
      label: '分摊周期',
      prop: 'apportCycle'
    },
    {
      label: '流程节点',
      prop: 'businessNode'
    },
    {
      label: '状态',
      prop: 'dicApportStatusName'
    },
    {
      label: '在办业务',
      prop: 'dicApportDoingBusName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '生效时间',
      prop: 'takeEffectTime'
    },
    {
      label: '操作',
      prop: 'operationBtn',
      align: 'center',
      fixed: 'right',
      render: ({ row, index }) => {
        let operationBtn = {
          DEL: (
            <el-popconfirm
              title="确定删除？"
              width="220"
              onConfirm={() => {
                deleFn(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_COST_SHARING_DELE'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => editFn(row)}
              auth={'ERM_COST_SHARING_EDIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => examineFn(row)}
              auth={'ERM_COST_SHARING_EXAMINE'}
              text={'审核'}
            ></Hyperlink>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={() => seeBusinessInfo(row)}
              auth={'ERM_COST_SHARING_DETAIL'}
              text={'业务详情'}
            ></Hyperlink>
          )
        };
        let btnList = [];
        btnList = $utils.clone(row.buttonList, true);
        if (!$utils.isNil(row.dicApportDoingBusCode) && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO');
        }
        return (
          <div style="display:flex;justify-content: space-around;align-items: center;width: 120px">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

/**
 * @description 项目管理 按钮配置
 */
export const useBtnsConfig = (addFn = () => {}, changeFn = () => {}, isDisabled = false) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_COST_SHARING_ADD" onClick={addFn} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_COST_SHARING_BODIFY" onClick={changeFn} type="primary" disabled={isDisabled}>
          变更
        </el-button>
      )
    }
  ];
};

/**
 * @description 项目管理 搜索配置
 */
export const useSearchConfig = () => {};
