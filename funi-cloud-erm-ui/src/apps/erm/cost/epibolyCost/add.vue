<template>
  <div>
    <funi-detail :bizName="bizName" :showHead="false" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
    <SubmitSuccess ref="submitSuccess" />
  </div>
</template>

<script lang="jsx" setup>
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from '@/apps/erm/cost/component/epibolyCost/baseInfo.vue';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
import { submitBusiness } from '@/apps/erm/config/business.js';

const route = useRoute();
const bizName = route.query.bizName;
const type = route.query.type;
const dicDeptMarkCode = route.query.dicDeptMarkCode;
const submitSuccess = ref();
const infoData = ref();
let id = ref(route.query.id);
let businessId = ref(route.query.businessId);
const detailHeadOption = computed(() => {
  return {
    title: route.query.title,
    hideStatusBar: true
  };
});

const steps = computed(() => {
  return [
    {
      title: '基本信息',
      type: BaseInfo,
      preservable: true,
      props: {
        isEdit: !['audit', 'info'].includes(route.query.type),
        id: id.value,
        dicDeptMarkCode,
        callBackFn: submitBusinessFunc
      },
      on: {
        updateID: data => {
          console.log(data);
          id.value = data.id;
          businessId.value = data.businessId;
        },
        updateInfo: data => {
          infoData.value = data;
        }
      }
    }
  ];
});
const submitBusinessFunc = async () => {
  let businessName = `外包人员成本分摊${infoData.value.year}-${infoData.value.month}`;
  await submitBusiness('ERM_OUTSOURCE_EMPLOYEES_SHARING_ADD', businessId.value, 'SUBMIT', businessName);
  submitSuccess.value.show();
};
</script>
