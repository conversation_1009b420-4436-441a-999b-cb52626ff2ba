<!-- 外包成本分摊确认 -->
<template>
  <div>
    <funi-list-page-v2 ref="pageList" :cardTab="useCardTab" />
    <personSelect :visible="visible" @updateStatus="updateStatus" />
  </div>
</template>

<script lang="jsx" setup>
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { submitBusiness } from '@/apps/erm/config/business.js';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
import { ElNotification } from 'element-plus';
import { useColumns, useBtnsConfig } from '@/apps/erm/cost/hooks/epibolyCost/useEpibolyCost.jsx';
import { queryEmployeeCostSharingListHttp, apiUrl } from '@/apps/erm/cost/hooks/epibolyCost/epibolyCostApi.jsx';
import personSelect from '@/apps/erm/cost/component/epibolyCost/personSelect.vue';
const router = useRouter();
const pageList = ref();
const visible = ref(false);
const selectData = ref();

const updateStatus = (dicDeptMarkCode, empSns, date, deptId) => {
  visible.value = false;
  if (dicDeptMarkCode === '0') {
    router.push({
      name: 'erm_epiboly_cost_add',
      query: {
        title: '外包成本分摊确认',
        bizName: '新建',
        type: 'add',
        tab: ['前台外包', '新增'].join('-'),
        empSns: JSON.stringify(empSns),
        date,
        dicDeptMarkCode: 0,
        deptId
      }
    });
  }
  if (dicDeptMarkCode === '1') {
    router.push({
      name: 'erm_epiboly_cost_add',
      query: {
        title: '外包成本分摊确认',
        bizName: '新建',
        type: 'add',
        tab: ['中台外包', '新增'].join('-'),
        date,
        dicDeptMarkCode
      }
    });
  }
};

//详情
const seeDetails = row => {
  router.push({
    name: 'erm_epiboly_cost_info',
    query: {
      title: `${row.dicDeptMarkName}${row.year}${row.month}` || '外包成本分摊确认',
      bizName: '详情',
      type: 'info',
      id: row.id,
      tab: ['外包成本分摊确认', row.dicDeptMarkName, `${row.year}${row.month}`, '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicDeptMarkCode: row.dicDeptMarkCode
    }
  });
};
//编辑
const editFn = row => {
  router.push({
    name: 'erm_epiboly_cost_add',
    query: {
      title: `${row.dicDeptMarkName}${row.year}${row.month}` || '外包成本分摊确认',
      bizName: '编辑',
      type: 'edit',
      id: row.id,
      tab: ['外包成本分摊确认', row.dicDeptMarkName, `${row.year}${row.month}`, '编辑'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicDeptMarkCode: row.dicDeptMarkCode
    }
  });
};
//审核
const examineFn = row => {
  console.log(row);
  router.push({
    name: 'erm_epiboly_cost_info',
    query: {
      title: `${row.dicDeptMarkName}${row.year}${row.month}` || '外包成本分摊确认',
      bizName: '审核',
      type: 'audit',
      id: row.id,
      tab: ['外包成本分摊确认', row.dicDeptMarkName, `${row.year}${row.month}`, '审核'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicDeptMarkCode: row.dicDeptMarkCode
    }
  });
};
//删除
const deleFn = async row => {
  await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  pageList.value.reload({ resetPage: false });
};
//业务详情
const seeBusinessInfo = row => {
  router.push({
    name: 'erm_epiboly_cost_info',
    query: {
      title: `${row.dicDeptMarkName}${row.year}${row.month}` || '外包成本分摊确认',
      bizName: '审核',
      type: 'audit',
      id: row.id,
      tab: ['外包成本分摊确认', row.dicDeptMarkName, `${row.year}${row.month}`, '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicDeptMarkCode: row.dicDeptMarkCode
    }
  });
};
//新建
const addFn = () => {
  visible.value = true;
};
//导出
const exportFn = async () => {
  if (!selectData.value) {
    ElNotification({ title: '请选择需要导出的数据', type: 'info' });
    return;
  }
  let params = {
    sharingIdList: selectData.value.map(item => item.id)
  };
  await expotrFunction({ url: apiUrl.employeeCostSharingListExport, params: params, FileName: '员工成本分摊' });
  // pageList.value.activeCurd.ref.clearSelection();
  // selectData.value = void 0;
};

const loadData = async (params, queryParams) => {
  let dicEmployeeTypeCode = '4';
  let resData = await queryEmployeeCostSharingListHttp({ ...params, ...queryParams, dicEmployeeTypeCode });
  return resData;
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: useColumns(seeDetails, editFn, examineFn, deleFn, seeBusinessInfo),
        btns: useBtnsConfig(addFn, exportFn),
        lodaData: loadData,
        fixedButtons: true,
        checkOnRowClick: true,
        reloadOnActive: true,
        on: {
          selectionChange: selection => {
            selectData.value = selection;
          }
        }
      }
    }
  ];
});
</script>
