<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-10-09 14:49:34
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-30 12:47:00
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/collection/print.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div v-loading="loading">
    <div class="print" :id="uuId">
      <div style="text-align: center">
        <h2>{{ contractName }}</h2>
      </div>
      <GroupTitle title="基本信息" />
      <funi-form-print :inline="false" :schema="schema" @get-form="setForm" :col="3" :border="true" />
      <PrintAuditLog ref="printAuditLog" :businessId="businessId" />
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onMounted, unref, nextTick, watch } from 'vue';
import { useSchema, useColumns } from '@/apps/erm/cost/hooks/epibolyCost/useEpiboyCostInfo.jsx';

import { Print } from '@/apps/erm/hooks/Print.js';
import funiFormPrint from '@/apps/erm/component/printForm/index.vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import PrintAuditLog from '@/apps/erm/component/printAuditLog/index.vue';

import { useMultiTab } from '@/utils/hooks/useMultiTab';
import { useRoute, useRouter } from 'vue-router';
import { queryCostSharingInfoHttp } from '@/apps/erm/cost/hooks/epibolyCost/epibolyCostApi.jsx';

const route = useRoute();
const router = useRouter();
const businessId = ref();
const form = ref();

const printAuditLog = ref();
const loading = ref(false);
const contractName = ref('');
const multiTab = useMultiTab();
const setForm = e => {
  form.value = e;
};
const id = route.query.id || '';
const uuId = 'print_' + $utils.guid();

const deptMarkCode = ref();
const dicEmployeeTypeCode = ref();

const schema = computed(() => {
  return useSchema({
    deptMarkCode: deptMarkCode.value,
    dicEmployeeTypeCode: dicEmployeeTypeCode.value
  });
});

const printCallback = () => {
  multiTab.closeCurrentPage();
};

const printFunc = () => {
  Print(document.querySelector('#' + uuId), {
    type: 'html',
    targetStyles: ['*'],
    scanStyle: false,
    maxWidth: 5000,
    callback: printCallback
  });
};

onMounted(() => {
  if (id) {
    getInfo();
  }
});
const getInfo = async () => {
  loading.value = true;
  let res = await queryCostSharingInfoHttp({ employeeCostSharingId: id });
  dicEmployeeTypeCode.value = res.employeeCostSharingVo.dicEmployeeTypeCode;
  deptMarkCode.value = res.employeeCostSharingVo.dicDeptMarkCode;
  businessId.value = res.employeeCostSharingVo.businessId;
  form.value.setValues({ ...res.employeeCostSharingVo });
  await nextTick();
  await unref(printAuditLog)
    .getInfo()
    .finally(() => {
      loading.value = false;
    });

  startPrint();
};
const startPrint = () => {
  setTimeout(() => {
    printFunc();
  }, 500);
};
</script>
<style scoped>
* {
  box-sizing: border-box;
}
.print {
  padding: 10px;
}
:deep(.group-title .title:after) {
  width: 0;
}
:deep(.group-title .title) {
  color: var(--el-text-color);
  padding-left: 0;
}
</style>
