<template>
  <div>
    <div>
      <funi-detail
        :bizName="bizName"
        :auditButtons="buttons"
        :steps="steps"
        :showWorkflow="true"
        @auditEvent="auditEvent"
        :detailHeadOption="detailHeadOption || {}"
        :businessId="['info', 'audit'].includes(type) ? businessId : void 0"
        :beforeAuditFn="beforeAuditFn"
      />
      <SubmitSuccess ref="submitSuccess" />
    </div>
  </div>
</template>

<script lang="jsx" setup>
import { computed, ref, unref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import BaseInfo from '@/apps/erm/cost/component/epibolyCost/baseInfo.vue';
import { auditEndFunc } from '@/apps/erm/config/business.js';
import { useMultiTab } from '@/utils/hooks/useMultiTab';

const multiTab = useMultiTab();

const router = useRouter();
const route = useRoute();
const bizName = route.query.bizName;
const infoData = ref();
let id = ref(route.query.id);
const type = route.query.type;
let businessId = ref(route.query.businessId);
const dicDeptMarkCode = route.query.dicDeptMarkCode;
const dicEmployeeTypeCode = route.query.dicEmployeeTypeCode;
const buttons = ref([]);
const baseDom = ref();
const detailHeadOption = computed(() => {
  // console.log('type', type);
  return {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点',
    btns:
      type === 'info'
        ? [
            {
              name: '打印',
              props: { type: 'text' },
              on: { click: print }
            }
          ]
        : []
  };
});

const steps = computed(() => {
  return [
    {
      title: '基本信息',
      type: BaseInfo,
      props: {
        isEdit: !['audit', 'info'].includes(route.query.type),
        deptMarkCode: dicDeptMarkCode,
        id: id.value,
        ref: refFun
      },
      on: {
        updateID: data => {
          id.value = data.id;
          businessId.value = data.businessId;
        },
        updateInfo: data => {
          infoData.value = data;
        }
      }
    },
    {}
  ];
});

const print = () => {
  router.push({
    name: 'erm_cost_epiboly_cost_print',
    query: {
      id: id.value,
      dicEmployeeTypeCode: '4'
    }
  });
};

const refFun = e => {
  baseDom.value = e;
};

const beforeAuditFn = async ({ businessExecutionType }) => {
  if (['AGREE', 'SUBMIT'].includes(businessExecutionType) && dicDeptMarkCode == '0') {
    try {
      return {
        ...infoData.value,
        comProjectInvestList: await unref(baseDom).getDataList()
      };
    } catch {
      return Promise.reject();
    }
  }
  return Promise.resolve({});
};
const auditEvent = () => {
  auditEndFunc(() => {
    multiTab.closeCurrentPage();
  });
};
</script>
