<!-- 资本化成本分摊 -->
<template>
  <div>
    <funi-list-page-v2 ref="pageList" :cardTab="useCardTab" />
    <AuditDialog v-model="dialogVisible" @getData="getReason" />
  </div>
</template>

<script lang="jsx" setup>
import { useColumns, useBtnsConfig } from '@/apps/erm/cost/hooks/cost_sharing/useCostSharing.jsx';
import { computed, ref } from 'vue';
import AuditDialog from '@/apps/erm/component/auditDialog/index.vue';
import {
  queryCapProjectCostApportionListHttp,
  costApportionDeleHttp,
  creatBusHttp
} from '@/apps/erm/cost/hooks/cost_sharing/costharingApi.jsx';
import router from '@/router';
import { ElNotification } from 'element-plus';
import { submitBusiness } from '@/apps/erm/config/business.js';
const pageList = ref(null);
const dialogVisible = ref(false);
const rowData = ref(void 0);
const reason = ref(void 0);

const type = ref(void 0);
const code = ref(void 0);
const loadData = async (params, queryParams) => {
  rowData.value = void 0;
  pageList.value.activeCurd.resetCurrentRow();
  let resData = await queryCapProjectCostApportionListHttp({ ...params, ...queryParams });
  return resData;
};

const businessCode = {
  add: 'ERM_CAP_PROJECT_COST_APPORTION_ADD',
  change: 'ERM_CAP_PROJECT_COST_APPORTION_CHANGE'
};

//详情
const seeDetails = row => {
  let id;
  if (row.dicApportStatusCode == '1') id = row.id || row.lastId;
  else id = row.lastId || row.id;
  router.push({
    name: 'erm_cost_sharing_two',
    query: {
      title: row.projectName || '资本化项目成本分摊',
      bizName: '详情',
      type: 'info',
      id: id,
      tab: ['资本化项目成本分摊', row.projectName, '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const seeBusinessInfo = row => {
  router.push({
    name: 'erm_cost_sharing_two',
    query: {
      title: row.projectName || '资本化项目成本分摊',
      bizName: '审核',
      type: 'audit',
      id: row.id,
      tab: ['资本化项目成本分摊', row.projectName, '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

//新建
const addFn = () => {
  router.push({
    name: 'erm_cost_sharing_add',
    query: {
      title: '资本化成本分摊-新建',
      bizName: '新建',
      type: 'add',
      businessName: `资本化项目成本分摊新增`,
      tab: '资本化项目成本分摊-新增',
      code: businessCode.add
    }
  });
};
//变更
const changeFn = async () => {
  let datas = await creatBusHttp({ id: rowData.value.id, dicBusinessTypeCode: businessCode.change });
  code.value = businessCode.change;
  if (!datas) return Promise.reject({});
  dialogVisible.value = true;
};

//获取审批原因
const getReason = async e => {
  reason.value = e?.reason;
  let id;
  if (rowData.value.dicApportStatusCode == '1') id = rowData.value.id || rowData.value.lastId;
  else id = rowData.value.id;
  if (reason.value) {
    router.push({
      name: 'erm_cost_sharing_add',
      query: {
        title: rowData.value.projectName || '资本化项目成本分摊',
        bizName: '编辑',
        type: 'edit',
        id: id,
        businessName: `资本化项目成本分摊变更`,
        flag: '变更',
        tab: ['资本化项目成本分摊', rowData.value.projectName, '变更'].join('-'),
        code: code.value,
        reason: reason.value
      }
    });
  } else {
    ElNotification({
      title: '提示',
      message: '请输入审核原因',
      type: 'warning'
    });
  }
};
//编辑
const editFn = row => {
  let id;
  if (row.dicApportStatusCode == '1') id = row.id || row.lastId;
  else id = row.id;
  router.push({
    name: 'erm_cost_sharing_add',
    query: {
      title: row.projectName || '资本化项目成本分摊',
      bizName: '编辑',
      type: 'edit',
      tab: ['资本化项目成本分摊', row.projectName, '编辑'].join('-'),
      id: id
    }
  });
};

//审核
const examineFn = row => {
  let id;
  if (row.dicApportStatusCode == '1') id = row.id || row.lastId;
  else id = row.id;
  router.push({
    name: 'erm_cost_sharing_two',
    query: {
      title: row.projectName || '资本化项目成本分摊',
      bizName: '审核',
      type: 'audit',
      id: id,
      lastId: row.lastId,
      tab: ['资本化项目成本分摊', row.projectName, '审核'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

//删除
const deleFn = async row => {
  await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  pageList.value.reload({ resetPage: false });
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: useColumns(seeDetails, editFn, examineFn, deleFn, seeBusinessInfo),
        btns: useBtnsConfig(addFn, changeFn, !rowData.value),
        lodaData: loadData,
        fixedButtons: true,
        checkOnRowClick: true,
        reloadOnActive: true,
        on: {
          rowClick: selection => {
            rowData.value = selection.row;
          }
        }
      }
    }
  ];
});
</script>
<style lang="scss" scoped></style>
