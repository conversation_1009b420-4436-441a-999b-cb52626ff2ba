<template>
  <div>
    <funi-detail
      :bizName="bizName"
      :auditButtons="buttons"
      :steps="steps"
      :showWorkflow="true"
      :detailHeadOption="detailHeadOption || {}"
      :businessId="['info', 'audit'].includes(type) ? businessId : void 0"
    />
    <SubmitSuccess ref="submitSuccess" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, inject } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from '@/apps/erm/cost/component/cost_sharing/baseInfo.vue';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
import { submitBusiness } from '@/apps/erm/config/business.js';
import sharingDetail from '@/apps/erm/cost/component/cost_sharing/sharingDetail.vue';

const route = useRoute();
const id = ref(route.query.id);
const bizName = route.query.bizName;
const type = route.query.type;
const tab = route.query.tab;
const flag = route.query.flag;
const reason = route.query.reason;
const code = route.query.code;
const isCopy = route.query.isCopy;
const businessName = route.query.businessName;
const infoData = ref();
const businessId = ref();
const lastId = ref();
const submitSuccess = ref();
const businessType = ref();

const detailHeadOption = computed(() => {
  return {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点'
  };
});

let showHead = ref(false);

const showPage =
  bizName != '新建'
    ? [
        {
          title: '分摊明细',
          preservable: true,
          type: sharingDetail,
          props: {
            id: id.value
          }
        }
      ]
    : [];
const steps = computed(() => {
  return [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit: !['audit', 'info'].includes(type),
        type: type,
        bizName,
        lastId: lastId.value,
        tab: flag,
        code: code,
        reason: reason,
        isCopy: isCopy,
        businessName
      },
      on: {
        updateID: data => {
          id.value = data.id;
          businessId.value = data.businessId;
          lastId.value = data?.lastId;
        },
        updateInfo: data => {
          id.value = data.id;
          businessId.value = data.businessId;
          infoData.value = data;
        }
      }
    },
    ...showPage,
    {
      title: '要件信息',
      preservable: false,
      type: 'FuniFileTable',
      props: {
        params: {
          businessId: businessId.value
        },
        callbackFun: submitBusinessFunc
      }
    }
  ];
});

const submitBusinessFunc = async () => {
  let businessName = `${infoData.value.companyName}-${infoData.value.deptName}-${infoData.value.projectName}`;
  await submitBusiness(businessType.value, infoData.value.businessId, 'SUBMIT', businessName).finally(() => {});
  submitSuccess.value.show();
};
</script>
<style lang="scss" scoped></style>
