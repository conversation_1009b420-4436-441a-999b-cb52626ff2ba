<template>
  <div>
    <funi-detail :bizName="bizName"
      :auditButtons="buttons"
      :steps="steps"
      :showWorkflow="true"
      @auditEvent="auditEvent"
      :detailHeadOption="detailHeadOption || {}"
      :businessId="['info', 'audit'].includes(type) ? businessId : void 0" />
    <!-- <funi-bus-audit-drawer ref="auditDrawer" :onlyShow="type !== 'audit'" :businessId="businessId" /> -->
  </div>
</template>

<script lang="jsx" setup>
import { computed, ref, unref } from 'vue';
import BaseInfo from '@/apps/erm/cost/component/cost_sharing/baseInfo.vue';
import sharingDetail from '@/apps/erm/cost/component/cost_sharing/sharingDetail.vue';
import { useRoute, useRouter } from 'vue-router';
import { auditEndFunc } from '@/apps/erm/config/business.js';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
const multiTab = useMultiTab();
const route = useRoute();
const router = useRouter();
const bizName = route.query.bizName;
const code = route.query.dicBusinessTypeCode;
const id = ref(route.query.id);
const lastId = ref(route.query.lastId);
const type = route.query.type;
const tab = route.query.tab;

import { ermGlobalApi } from '@/apps/erm/config/config.jsx';

const businessId = ref();
const infoData = ref(void 0);
const buttons = ref([]);
const copy = () => {
  router.push({
    name: 'erm_cost_sharing_add',
    query: {
      title: '资本化成本分摊-新建',
      bizName: '新建',
      type: 'add',
      id: id.value,
      isCopy: true,
      businessName: `资本化项目成本分摊新增`,
      tab: '资本化项目成本分摊-新增',
      code: 'ERM_CAP_PROJECT_COST_APPORTION_ADD'
    }
  });
};

const onClick = row => {
  router.push({
    name: 'erm_cost_sharing_two',
    query: {
      title: infoData.value?.projectName || '资本化项目成本分摊',
      bizName: '详情',
      type: 'info',
      id: row.id,
      tab: ['资本化项目成本分摊', infoData.value?.projectName, '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const detailHeadOption = computed(() => {
  return {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点',
    btns: [
      ...(type === 'info'
        ? [
          {
            name: '复制',
            props: { type: 'text' },
            on: { click: copy },
            menuAuth: 'ERM_COST_SHARING_COPY'
          }
        ]
        : [])
    ]
  };
});

let steps = computed(() => {
  return [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit: !['audit', 'info'].includes(type),
        type: type,
        lastId: lastId.value,
        code: code,
        bizName: bizName,
        tab: tab,
        businessId: businessId.value
      },
      on: {
        updateID: data => {
          id.value = data.id;
          businessId.value = data.businessId;
        },
        updateInfo: data => {
          infoData.value = data;
          id.value = data.id;
          businessId.value = data.businessId;
        }
      }
    },
    {
      title: '分摊明细',
      preservable: true,
      type: sharingDetail,
      props: {
        id: id.value
      }
    },
    ...(businessId.value && (route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode)
      ? [
        {
          title: '办件记录',
          type: 'FuniWorkRecord',
          props: {
            objectListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
            busListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
            params: {
              businessId: businessId.value,
              dicBusinessTypeCode: route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode
            }
          },
          on: {
            onClick
          }
        }
      ]
      : [])
  ];
});
const auditEvent = () => {
  auditEndFunc(() => {
    multiTab.closeCurrentPage();
  });
};
</script>
<style lang="scss" scoped></style>
