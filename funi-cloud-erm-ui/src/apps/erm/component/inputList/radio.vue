
<template>
  <el-radio-group v-model="selectValue"
    v-bind="$attrs">
    <el-radio v-for="item in options"
      :key="item.label"
      :label="item.label">{{ item.text }}</el-radio>
  </el-radio-group>
</template>

<script lang="jsx">
import { useVModel } from '@vueuse/core'
import { defineComponent, watch } from 'vue'

export default defineComponent({
  name: 'Radio',
  props: {
    modelValue: { type: String, default: '' },
    options: { type: Array, default: () => { return [] } },
    propLabelValue: { type: String, default: '' },
    propLabel: { type: Boolean, default: false }
  },
  setup(props, { emit }) {
    const selectValue = useVModel(props, 'modelValue', emit)
    const selectLabelValue = useVModel(props, 'propLabelValue', emit)
    // 监听 selectValue 改变事件
    watch(() => props.modelValue, (newSelectValue) => {
      if (props.propLabel) {
        selectLabelValue.value = props.options.filter(item => item.label === newSelectValue)[0]?.text ?? ''
      }
    })
    return { selectValue }
  }
})
</script>
<style scoped lang="scss">
.el-radio-group {
  display: inline-flex;
}

.el-radio {
  margin-right: 20px;
}

.el-radio:last-child {
  margin-right: 0;
}
</style>
