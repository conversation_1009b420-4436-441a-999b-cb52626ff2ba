<template>
  <el-tree-select
    style="width: 100%"
    v-model="selectValue"
    :data="option"
    :render-after-expand="false"
    :props="defaultProps"
    v-bing="$attrs"
    default-expand-all
    @node-click="nodeClick"
    @current-change="currentChange"
  />
</template>

<script setup>
import { useVModel } from '@vueuse/core';
import { ref, watch } from 'vue';
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  propLabelValue: {
    type: String,
    default: ''
  },
  defaultProps: {
    type: Object,
    default: () => {
      return {
        label: 'label',
        value: 'value',
        children: 'children'
      };
    }
  },
  options: {
    type: Array,
    default: () => {
      return [];
    }
  }
});

const emits = defineEmits(['update:modelValue', 'update:propLabelValue', 'change']);
const selectValue = useVModel(props, 'modelValue', emits);
const selectLabelValue = useVModel(props, 'propLabelValue', emits);
let option = ref([]);

const currentChange = e => {
  emits('update:modelValue', e[props.defaultProps?.value]);
  emits('change', e[props.defaultProps?.value], e[props.defaultProps?.label]);
};

const nodeClick = () => {
  emits('clear');
};

watch(
  () => props.options,
  newValue => {
    if (newValue) {
      option.value = newValue;
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => props.modelValue,
  newValue => {
    emits('change', newValue);
  }
);
</script>
<style lang="scss" scoped></style>
