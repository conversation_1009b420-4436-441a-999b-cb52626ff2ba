import { ref } from 'vue';

export const useTreeSelect = config => {
  const { isInit, queryParas, selectChange, options, curstomGetOptions, changeSelect } = config;
  const selectValue = ref('');
  const _options = ref([]);

  // 获取数据
  const getOptions = async paras => {
    if (curstomGetOptions) {
      curstomGetOptions();
    } else {
      if (options && options.length > 0) {
        return (_options.value = options);
      }
      const queryTemp = Object.assign({}, queryParas || {}, paras || {});
      const temp = await config?.api?.(queryTemp);
      _options.value = Array.isArray(temp) ? temp : temp.list;
    }
  };
  if (isInit) {
    getOptions();
  }
  const reload = paras => getOptions(paras);
  return {
    changeSelect,
    selectChange,
    selectValue,
    options: _options,
    reload
  };
};
