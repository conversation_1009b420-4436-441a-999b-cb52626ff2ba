<template>
  <div style="display: flex; flex-direction: column; align-items: flex-end; position: relative; width: 100%">
    <el-input type="textarea" @input="input" :rows="rows" v-model="textValue" />
    <span style="position: absolute; bottom: 0; right: 5px"
      ><span :style="{ color: overflow ? 'red' : '' }">{{ valueLength }}</span
      >/ {{ maxlength }}</span
    >
  </div>
</template>

<script lang="jsx" setup>
import { ref, computed, watch } from 'vue';
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  maxlength: {
    type: Number,
    default: 1000
  },
  rows: {
    type: Number,
    default: 2
  }
});
const emit = defineEmits(['update:modelValue', 'change']);

let valueLength = ref(0);
let textValue = ref();
const input = e => {
  console.log(e, '1123123');
  valueLength.value = e.length;
  textValue.value = e;
  emit('update:modelValue', e);
  emit('change', e);
};
const overflow = computed(() => {
  return valueLength.value > props.maxlength;
});

watch(
  () => props.modelValue,
  () => {
    textValue.value = props.modelValue;
    valueLength.value = props.modelValue.length;
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped></style>
