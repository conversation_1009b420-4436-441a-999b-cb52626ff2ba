/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-05-08 20:23:33
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-05-26 16:10:17
 * @FilePath: /funi-cloud-web-gsbms/src/apps/component/upload/uploadUse.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { ElNotification } from 'element-plus';

/**
 * @description 统一下载模板接口
 * @param {String} downloadTemplate 下载函数
 * @param {String} templateName 模板名称
 * **/
const downloadTemplateUse = async (downloadTemplate, templateName) => {
  let resData = await $http.post(downloadTemplate, {}, { responseType: 'blob' });
  var downloadElement = document.createElement('a');
  var href = window.URL.createObjectURL(resData); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = templateName + '.xls'; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
};

export { downloadTemplateUse };
