<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-18 13:33:31
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-05-16 16:07:10
 * @FilePath: /funi-cloud-web-gsbms/src/apps/component/upload/upload.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="el-button uploadBefore">
    <div class="btn_upload_coutinho" @click="btnClick">
      <slot>上传</slot>
    </div>
    <funi-dialog v-model="dialogVisible" :title="title" size="small" :append-to-body="appendToBody"  :onCancel="onCancel"
    @close="onCancel">
      <div class="uploadBox">
        <p class="download-link" v-if="!onlyUpload">
          一、
        <div class="download-link download-link__text" @click="downloadTemplateUse(dc, templateName)">
          <span>下载导入模板</span>
          <funi-icon icon="material-symbols:cloud-download-outline-rounded"/>
        </div>
        </p>
        <p v-if="!onlyUpload">二、上传文件</p>
        <div class="uploadBox__func">
          <el-upload class="upload-demo" drag action="#" :accept="accept" :auto-upload="true"
                     :http-request="httpRequest">
            <el-icon class="el-icon--upload">
              <upload-filled/>
            </el-icon>
            <div class="el-upload__text">
              <div class="upload__text">点击选择或拖动文件到此区域上传</div>
              <div class="tips">
                <div>1.请上传 {{ accept }} 文件</div>
                <div>2.请上传小于10M文件</div>
              </div>
            </div>
          </el-upload>
          <div style="color: red" v-if="showMessage && message">{{ message}}</div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel">关闭</el-button>
        </span>
      </template>
    </funi-dialog>

  </div>
</template>
<script setup lang="jsx">
import {ref, onMounted, watch,nextTick} from 'vue';
import {ElNotification} from 'element-plus';
import {UploadFilled} from '@element-plus/icons-vue';
import {downloadTemplateUse} from './uploadUse.js'

const emit = defineEmits(['updateCurd'])
const props = defineProps({
  url: {
    type: String,
    default: ''
  },
  formData: {
    type: Object,
    default: () => {
    }
  },
  showMessage: {
    type: Boolean,
    default: false
  },
  appendToBody:{
    type: Boolean,
    default: false
  },
  callbackFun: {
    type: Function,
    default: () => {
    }
  },
  dc: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '导入',
  },
  templateName: {
    type: String,
    default: '模板',
  },
  onlyUpload: {
    type: Boolean,
    default: false
  },
  accept: {
    type: String,
    default: '.xlsx,.xls',
  }
});


let dialogVisible = ref(false);
let message = ref('');

const onCancel = ()=>{
    dialogVisible.value = false;
    message.value = void 0;
}
const httpRequest = async options => {
  const formData = new FormData();
  formData.append('file', options.file);
  if(props.formData){
    for (const [key, value] of Object.entries(props.formData)) {
    formData.append(key, value);
  }
  }
  if (props.url) {
    let res = await $http.upload(props.url, formData)
    if (res && res.fileName) {
      ElNotification({
        title: '成功',
        message: `${res.fileName}${props.onlyUpload ? '上传' : '导入'}成功`,
        type: 'success'
      });
    } else {
        if(res && typeof res === 'string' &&  props.showMessage){
            message.value = res;
            return Promise.reject()
        }else{
            ElNotification({
                title: '成功',
                message: '导入成功',
                type: 'success'
            });
            message.value=void 0

        }
    }
    props.callbackFun(res)
    dialogVisible.value = false;
    return Promise.resolve({})
  }
};

const btnClick = () => {
  // dom && dom.click();
  dialogVisible.value = true;
};
</script>
<style scoped>
@import url(./upload.css);
</style>
