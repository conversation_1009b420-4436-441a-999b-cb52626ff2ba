<template>
  <div style="width: 100%; display: flex; gap: 4px">
    <div style="flex: 1" ref="infinte">
      <el-popover placement="bottom" :popper-style="{ padding: '0' }" :visible="visible" trigger="focus"
        :width="maxWidth || infinte?.offsetWidth">
        <template #reference>
          <div class="valueBox">
            <el-input ref="input_search" v-model="showValue" @input="iptInput" :disabled="disabled"
              @click.stop="iptFocus" @blur.stop="iptBlur" :suffix-icon="ArrowDown" clearable @clear="clear"
              :placeholder="valueName !== '' && valueName !== void 0 && valueName !== null ? valueName : placeholder" />
          </div>
        </template>
        <div class="el-select-dropdown" style="max-height: 200px">
          <el-scrollbar max-height="200px">
            <div class="el-select-dropdown__wrap" v-infinite-scroll="load" :infinite-scroll-distance="10">
              <ul v-if="listShow" class="el-scrollbar__view el-select-dropdown__list" :style="{
                '--funi-erm-li-width': elSelectItemWidth - 52
              }">
                <template v-for="item in data_list" :key="item.id">
                  <li v-if="!item.isHideOption" class="el-select-dropdown__item funi-erm-el-select-dropdown__item"
                    :class="{
                      hover: item.id == mk,
                      'is-hovering': item.id == mk,
                      selected: item.id == ck && !item.isHideOption,
                      'is-selected': item.id == ck && !item.isHideOption,
                      hideOption: item.isHideOption
                    }" @mousemove="mk = item.id" @click="clickFun(item)">
                    <span :style="{
                      display: 'inline-block',
                      whiteSpace: maxWidth ? 'wrap' : 'nowrap'
                    }">
                      {{ item.name }}
                    </span>
                  </li>
                </template>
              </ul>

              <div v-else class="nodata">暂无数据</div>
            </div>
          </el-scrollbar>
        </div>
      </el-popover>
    </div>
    <slot></slot>
  </div>
</template>
<script setup lang="jsx">
import { ref, onMounted, nextTick, watch, computed, unref } from 'vue';
import { ArrowDown } from '@element-plus/icons-vue';
import { useFormItem } from 'element-plus';

const formTiem = useFormItem();
const input_search = ref(null);
const hasSet = ref(false);
const mk = ref('');
const ck = ref('');
const keyWord = ref('');
const valueName = ref('');
const visible = ref(false);
const data_list = ref([]);
const total = ref(0);
const fina = ref(true);
const timer = ref(null);
const pageOption = ref({
  pageNo: 1
});
let initialize = [];
let initializeTotal = 0;
const infinte = ref();
const first = ref(true);
const infinteWidth = ref(0);
const elSelectItemWidth = ref(0);
const props = defineProps({
  modelValue: {
    type: Object,
    default: {
      name: '',
      id: ''
    }
  },
  api: {
    type: String,
    default: ''
  },
  isUseTable: {
    type: Boolean,
    default: false
  },
  defaultProps: {
    type: Object,
    default: {
      keyWord: 'name',
      name: 'name',
      id: 'id'
    }
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  otherParams: {
    type: Object,
    default: {}
  },
  defaultList: {
    type: Array,
    default: []
  },
  maxWidth: {
    type: Number,
    default: 0
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const uuIdClass = `infinteBox-${$utils.guid()}`;

const showValue = ref('');

const defaultProps = computed(() => {
  return Object.assign(
    {
      keyWord: 'name',
      name: 'name',
      id: 'id'
    },
    props.defaultProps
  );
});
const iptBlur = () => {
  // visible.value = false;
};


const iptFocus = async () => {
  if (hasSet.value) {
    data_list.value = JSON.parse(JSON.stringify(initialize));
    total.value = initializeTotal;
    pageOption.value.pageNo = 1;
    setModelValueObject();
  }
  await nextTick();
  keyWord.value = '';
  visible.value = true;
};
const emit = defineEmits(['update:modelValue', 'blur', 'getData', 'change']);
onMounted(() => {
  console.log('onMounted')
  getDataList({});
  document.addEventListener('click', () => {
    visible.value = false;
  });
});
const iptInput = e => {
  keyWord.value = e;
  showValue.value = e;
  clearTimeout(timer.value);
  timer.value = setTimeout(
    f => {
      if (f) {
        clearTimeout(timer.value);
        pageOption.value.pageNo = 1;
        getDataList({
          name: e,
          pageNo: 1
        });
      } else {
        iptInput(e);
      }
    },
    500,
    fina.value
  );
};
const load = () => {
  if (total.value === 0 || Math.ceil(total.value / 10) == pageOption.value.pageNo) {
    return false;
  }
  pageOption.value.pageNo++;
  getDataList({
    name: keyWord.value,
    pageNo: pageOption.value.pageNo
  });
};
const getDataList = async ({ name = void 0, pageNo = 1 } = {}) => {
  fina.value = false;
  let { list, total: t } = await $http
    .post(props.api, {
      ...props.otherParams,
      [defaultProps.value.keyWord]: name,
      pageSize: 10,
      pageNo
    })
    .finally(() => {
      fina.value = true;
    });
  total.value = t;
  if (pageNo == 1) {
    data_list.value = [...props.defaultList];
    setTimeout(() => {
      setModelValueObject('search');
    });
  }
  if (pageNo === 1 && (name === '' || name === void 0 || name === null)) {
    initialize = list.map(item => {
      const name = defaultProps.value.nameEval ? new Function('item', 'defaultProps', `return ${defaultProps.value.nameEval}`)(item, defaultProps) : item[defaultProps.value.name];
      return {
        ...item,
        id: item[defaultProps.value.id],
        name,
        defaultName: item[defaultProps.value.name]
      };
    });
    if (props.defaultList && props.defaultList.length) {
      initialize = [...props.defaultList, ...initialize];
    }
    initializeTotal = t;
  }
  data_list.value.push(
    ...list.map(item => {
      const name = defaultProps.value.nameEval ? new Function('item', 'defaultProps', `return ${defaultProps.value.nameEval}`)(item, defaultProps) : item[defaultProps.value.name]
      return {
        ...item,
        id: item[defaultProps.value.id],
        name,
        defaultName: item[defaultProps.value.name]
      };
    })
  );

  if (first.value && props.modelValue.id) setDefault();
};

const setModelValueObject = type => {
  if (props.modelValue && props.modelValue.id && props.modelValue.name) {
    let index = data_list.value.findIndex(item => item.id === props.modelValue.id);
    if (index < 0) {
      data_list.value.push({
        id: props.modelValue.id,
        name: props.modelValue.name,
        isHideOption: type ? true : false
      });

      console.log(data_list.value, 'data_list.value');
    }
  }
};
const setDefault = () => {
  let item = data_list.value.filter(el => el.id === props.modelValue.id);
  item && item.length && clickFun(item[0], false);
  valueName.value = item[0] ? item[0].name : props.modelValue.id;
  showValue.value = valueName.value;
};
const clickFun = (obj, bool = true) => {
  first.value = false;
  ck.value = obj?.id;
  valueName.value = obj?.name;
  showValue.value = valueName?.value;
  if (bool === true) {
    emit(
      'update:modelValue',
      obj
        ? {
          name: obj?.name,
          id: obj?.id,
          defaultName: obj.defaultName
        }
        : void 0
    );
    emit(
      'change',
      obj?.id,
      {
        name: obj?.name,
        id: obj?.id,
        defaultName: obj?.defaultName,
        ...(obj ? obj : {})
      },
      obj
    );
    _validate();
  }
  visible.value = false;
  hasSet.value = true;
  emit('getData', data_list.value);
};

const listShow = computed(() => {
  let list = data_list.value.filter(item => !item.isHideOption);
  return !!list.length;
});
const clear = () => {
  clickFun(void 0);
};

watch(
  () => props.modelValue,
  (newVal, oldVal) => {
    showValue.value = newVal.name;
    setModelValueObject();
    setDefault();
  },
  { immediate: true, deep: true }
);

watch(visible, newVal => {
  if (!visible.value) {
    let a = data_list.value.find(item => item.id == props.modelValue?.id);
    valueName.value = a ? a.name : props.modelValue.id;
    input_search.value.blur();
  }
  showValue.value = visible.value ? keyWord.value : valueName.value;
});

watch(() => props.otherParams, () => {
  getDataList()
})
const _validate = () => {
  if (!props.isUseTable) {
    formTiem.formItem.validate();
  }
};
</script>
<style scoped>
.valueBox {
  display: flex;
  align-items: center;
}

.hideOption {
  display: none;
}

.funi-erm-el-select-dropdown__item {
  height: auto;
  min-height: 34px;
  line-height: unset !important;
  display: flex;
  align-items: center;
  padding: 5px 20px;
}

:deep(.el-input__prefix) {
  height: 30px;
}

:deep(.el-input__prefix) {
  max-width: 50%;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 8px 0 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-input__prefix-inner),
:deep(.el-input__wrapper) {
  width: 100%;
  box-sizing: border-box;
}

.nodata {
  text-align: center;
  line-height: 50px;
}
</style>
