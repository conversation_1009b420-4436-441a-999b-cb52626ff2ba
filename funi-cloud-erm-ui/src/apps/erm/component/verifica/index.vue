<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-05-21 23:36:55
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-15 20:36:36
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/verifica/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="v_group">
    <component :class="{ 'is-error': vs === 'err' }" :is="c" :modelValue="mv" @update:modelValue="u_m_v" />
    <div class="v_err">{{ msg }}</div>
  </div>
</template>
<script setup>
import { ref, watch, unref, onRenderTracked } from 'vue';
const props = defineProps({
  c: {
    type: Object,
    default: () => ''
  },
  rule: {
    tyep: Array,
    default: () => []
  },
  value: {
    tyep: String | Number | Array | Object,
    default: void 0
  },
  cn: {
    tyep: String,
    default: ''
  }
});
const mv = ref(void 0);
const msg = ref(void 0);
const vs = ref('normal');
const ved = ref(false);
const emits = defineEmits(['change']);

watch(
  () => props.value,
  newVal => {
    mv.value = newVal;
  },
  { deep: true, immediate: true }
);

const u_m_v = e => {
  emits('change', e);
  v(e);
};
const v = e => {
  (vs.value = 'normal'), (msg.value = '');
  for (let i = 0; i < props.rule.length; i++) {
    v_r(i, e);
    v_v(i, e);
    if (vs.value == 'err') break;
  }
  ved.value = true;
  return { vs: unref(vs), msg: unref(msg), v: e };
};
const v_r = (i, e) => {
  if (props.rule[i].required && v_m(e)) {
    msg.value = props.rule[i].message;
    vs.value = 'err';
  } else {
    msg.value = '';
    vs.value = 'normal';
  }
};
const v_v = (i, e) => {
  if (props.rule[i].validator) {
    let cb = m => {
      if (m && typeof m == 'string') {
        msg.value = m || props.rule[i].message;
        vs.value = 'err';
      } else {
        msg.value = '';
        vs.value = 'normal';
      }
    };
    props.rule[i].validator(e, cb);
  }
};
const v_m = e => {
  return e === null || e === void 0 || e === '';
};
const f_v = () => {
  return v(props.value);
};
const ved_f = () => {
  return ved.value;
};

const vs_f = () => {
  return vs.value;
};
const msg_f = () => {
  return msg.value;
};

defineExpose({ f_v, ved_f, vs_f, msg_f });
</script>
<style scoped>
.v_group {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  position: relative;
}
.v_err {
  font-size: 12px;
  color: red;
  line-height: 1;
  padding-top: 2px;
  /* transform:scale(0.8) */
}
:deep(.is-error > .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}
:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
