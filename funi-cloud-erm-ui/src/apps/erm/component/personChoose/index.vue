<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-26 16:01:58
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-26 10:59:33
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/personChoose/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div style="width: 100%">
    <div ref="funiRuocLc" class="el-select funi-ruoc-lc" @click="ruocClick">
      <div class="ruoc-value">
        <div v-for="(item, index) in valueList" :key="item.id" class="itemBlock">
          <span style="font-size: 15px"> <funi-icon :icon="iconClass[item.type]" /> </span>
          <span style="line-height: 14px">{{ item.name }}</span>
          <span v-if="!readonly && !disabled" class="delItem" @click.stop="delItem(item, index)">
            <funi-icon icon="iconamoon:close-thin" />
          </span>
        </div>
      </div>
      <span class="el-input__suffix" v-if="!readonly && !disabled">
        <span class="el-input__suffix-inner">
          <i class="el-icon el-select__icon icon_clear clear_ruoc" style="margin-left: 0" @click.stop="clearValue">
            <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-ea893728="">
              <path
                fill="currentColor"
                d="m466.752 512-90.496-90.496a32 32 0 0 1 45.248-45.248L512 466.752l90.496-90.496a32 32 0 1 1 45.248 45.248L557.248 512l90.496 90.496a32 32 0 1 1-45.248 45.248L512 557.248l-90.496 90.496a32 32 0 0 1-45.248-45.248L466.752 512z"
              ></path>
              <path
                fill="currentColor"
                d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"
              ></path>
            </svg>
          </i>
          <i class="el-icon el-select__caret el-select__icon icon_default">
            <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
              <path
                fill="currentColor"
                d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"
              ></path>
            </svg>
          </i>
        </span>
      </span>
    </div>

    <funi-dialog v-model="showDialog" append-to-body title="选择人员" @confirm="handleConfirm">
      <FuniRUOC
        ref="funiruoc"
        tabPaneHeight="300px"
        :hide="{
          role: true,
          org: true
        }"
        :modelValue="nowValue"
        activeName="user"
      ></FuniRUOC>
    </funi-dialog>
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import { useFormItem } from 'element-plus';
const formTiem = useFormItem();

const valueList = ref([]);
const iconClass = {
  user: 'humbleicons:user',
  role: 'mdi:user-box-outline',
  org: 'fluent-mdl2:org'
};
const funiruoc = ref();
const showDialog = ref(false);
const props = defineProps({
  modelValue: {
    type: Array,
    default: []
  },
  readonly: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
});
const emits = defineEmits(['update:modelValue']);
const nowValue = ref([]);
const handleConfirm = () => {
  emits('update:modelValue', nowValue.value);
  showDialog.value = false;
  _validate();
};
const delItem = (e, i) => {
  nowValue.value.splice(i, 1);
  emits('update:modelValue', nowValue.value);
  _validate();
};
const ruocClick = () => {
  if (props.readonly || props.disabled) return;
  showDialog.value = true;
  nowValue.value = JSON.parse(JSON.stringify(props.modelValue));
};

const clearValue = () => {
  emits('update:modelValue', []);
  _validate();
};

const _validate = () => {
  formTiem?.formItem?.validate();
};
watch(
  () => props.modelValue,
  val => {
    nowValue.value = JSON.parse(JSON.stringify(val));
    valueList.value = val;
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<style scoped>
.itemBlock {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  color: var(--el-text-color-regular);
  background-color: var(--el-fill-color);
  padding: 5px;
  border-radius: 3px;
  gap: 5px;
}

.itemBlock > span {
  line-height: 0;
}

.delItem {
  cursor: pointer;
  transition: all 0.2s;
}

.delItem:hover {
  transform: scale(1.3);
}
.funi-ruoc-lc {
  width: max(240px, 100%);
  min-height: 32px;
  max-height: 100px;
  padding: 0 0 0 11px;
  box-sizing: border-box;
  color: var(--el-text-color-regular);
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.clear_ruoc {
  display: none;
}
.funi-ruoc-lc:hover .clear_ruoc {
  display: inline-block;
}
.funi-ruoc-lc:hover .icon_default {
  display: none;
}
/* 
.funi-ruoc-lc > div {
  width: calc(100% - 30px);
} */

.ruoc-value {
  min-height: 32px;
  max-height: 100px;
  width: 100%;
  display: flex;
  box-sizing: border-box;
  padding: 3px;
  justify-content: flex-start;
  align-content: flex-start;
  flex-wrap: wrap;
  gap: 5px;
  overflow-y: auto;
  padding-right: 30px;
}

.funi-ruoc-lc > .el-input__suffix {
  position: absolute;
  right: 11px;
}
</style>
