<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-09 20:11:27
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-01 15:14:13
 * @FilePath: /funi-paas-csccs-ui/src/apps/ccs/comm/components/hyperlinkTable/infoBtn.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->

<template>
  <span
    v-if="hasAuth"
    ref="linkSpan"
    style="color: var(--el-color-primary); cursor: pointer"
    @click.stop="func(row, index, name)"
    >{{ text }}</span
  >
  <span v-else>{{ text }} </span>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue';
import { usePermissionStore } from '@/stores/usePermissionStore';
const linkSpan = ref(null);

let hasAuth = ref(false);
const props = defineProps({
  row: { type: Object, default: () => {} },
  index: { type: Number, default: 0 },
  name: { type: String, default: '' },
  func: { type: Function, default: () => {} },
  text: { type: String, default: '' },
  auth: { type: String, default: '' }
});
const permissions = usePermissionStore().permissionsInCurrentPage || [];

hasAuth.value = permissions.includes(props.auth);
const setEllipsisColor = () => {
  let fatherDemo = linkSpan.value.parentNode;
  let classList = fatherDemo.getAttribute('class').split(' ');
  if (classList.indexOf('el-tooltip') > -1) {
    fatherDemo.style.color = 'var(--el-color-primary)';
  }
};
watch(
  hasAuth,
  async () => {
    if (hasAuth.value) {
      await nextTick();
      setEllipsisColor();
    }
  },
  {
    immediate: true
  }
);
</script>
