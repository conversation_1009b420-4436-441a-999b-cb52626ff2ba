/* * @Author: coutinho <EMAIL> * @Date: 2023-02-21 10:09:26 *
@LastEditors: coutinho <EMAIL> * @LastEditTime: 2023-03-05 21:50:15 *
@FilePath:
/funi-paas-csccs-ui/src/apps/ccs/comm/components/hyperlinkTable/index.jsx *
@Description: * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. */

<template>
  <span v-auth="auth" style="color: var(--el-color-primary); cursor: pointer" @click="func(row, index, name)">{{ text
  }}</span>
</template>

<script setup>
const props = defineProps({
  row: { type: Object, default: () => { } },
  index: { type: Number, default: 0 },
  name: { type: String, default: '' },
  func: { type: Function, default: () => { } },
  text: { type: String, default: '' },
  auth: { type: String, default: '' }
});
</script>
