<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-05 19:40:52
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-08 15:36:15
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/table/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <table :style="tableStyle">
    <thead>
      <th
        v-for="(item, index) in column"
        :style="{
          width: item.width,
          ...item.headStyle
        }"
      >
        {{ item.title }}
      </th>
    </thead>
    <tbody>
      <tr v-for="(item, index) in data">
        <td
          v-for="(itemc, indexc) in column"
          :style="{
            'text-align': 'center',
            ...(typeof itemc.style === 'function' ? itemc.style(index, item) : itemc.style)
          }"
        >
          {{ item[itemc['dataIndex']] }}
        </td>
      </tr>
    </tbody>
  </table>
</template>
<script setup>
import { ref, computed } from 'vue';
const props = defineProps({
  column: {
    type: Array,
    default: []
  },
  data: {
    type: Array,
    default: []
  },
  padding: {
    type: Object,
    default: {}
  }
});

const tableStyle = computed(() => {
  return {
    '--ppt-table-th-padding': props.padding?.th || '10px 0',
    '--ppt-table-td-padding': props.padding?.td || '10px 0'
  };
});
</script>
<style scoped lang="scss">
table {
  border-collapse: collapse; /* 合并边框，使其更紧凑 */
  width: 100%;
  border-bottom: 2px solid black;
  table-layout: fixed;
}

thead {
  background-color: #ffa100;
  color: #fff;
  border-bottom: 2px solid black;
  border-top: 2px solid black;
  box-sizing: border-box;
  font-size: min(calc(var(--erm-ppt-default-size) * 1.4), 20px);
  position: sticky;
  top: -1px;
  th {
    padding: var(--ppt-table-th-padding);
    border-right: 1px solid #fff;
    overflow-wrap: break-word;
  }
  th:last-child {
    border-right: none;
  }
}
tbody {
  td {
    padding: var(--ppt-table-td-padding);
    border-right: 1px solid #fff;
    font-size: min(calc(var(--erm-ppt-default-size) * 1.4), 20px);
    color: black;
  }
  td:last-child {
    border-right: none;
    overflow-wrap: break-word;
  }
  tr:nth-child(2n-1) {
    background-color: #e7e7e7;
  }
  tr:hover {
    background-color: #e5f2ff;
  }
}
</style>
