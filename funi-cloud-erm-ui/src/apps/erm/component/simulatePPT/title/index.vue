<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-04 18:31:32
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-06 10:42:37
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/title/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="ppt-title" ref="ppt_title" :style="titleRef">
    <div class="ppt-title__position">
      <div class="ppt-title-left__box"></div>
      <div class="ppt-title__text">{{ title }}</div>
      <div class="ppt-title-right__box"></div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue';
import { useZIndex } from 'element-plus';
const props = defineProps({
  title: {
    type: String,
    default: 'title'
  }
});
const zIndex = ref(0);
const ppt_title = ref();
const titleRef = computed(() => {
  return {
    '--title-padding-bottom': '5%',
    '--title-z-index': zIndex.value
  };
});

onMounted(() => {
  zIndex.value = useZIndex().currentZIndex.value;
});
const getHeight = () => ppt_title.value.offsetHeight;
defineExpose({
  getHeight
});
</script>
<style scoped>
.ppt-title {
  width: 100%;
  box-sizing: border-box;
  color: var(--erm-ppt-purple);
  font-size: 20px;
  position: relative;
  padding-bottom: var(--title-padding-bottom);
  margin-bottom: 40px;
}
.ppt-title__position {
  position: absolute;
  box-sizing: border-box;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  align-items: flex-end;
}
.ppt-title__text {
  margin-left: 10px;
  font-size: min(calc(var(--erm-ppt-default-size) * 3), 50px);
}
.ppt-title-left__box,
.ppt-title-right__box {
  width: var(--title-padding-bottom);
  height: 100%;
  position: relative;
}

.ppt-title-left__box::before,
.ppt-title-left__box::after {
  content: '';
  display: inline-block;
  width: 70%;
  height: 70%;
  position: absolute;
}
.ppt-title-left__box::before {
  top: 0;
  left: 0;
  background-color: var(--erm-ppt-purple);
  z-index: calc(var(--title-z-index) + 20);
}
.ppt-title-left__box::after {
  top: 15px;
  left: 15px;
  background-color: var(--erm-ppt-purple-light);
  z-index: calc(var(--title-z-index) + 10);
}

.ppt-title-right__box::before,
.ppt-title-right__box::after {
  content: '';
  display: inline-block;
  width: 30%;
  height: 15%;
  position: absolute;
}

.ppt-title-right__box::before {
  bottom: 7%;
  right: 15%;
  background-color: var(--erm-ppt-purple);
  z-index: calc(var(--title-z-index) + 20);
}
.ppt-title-right__box::after {
  bottom: 0;
  right: 0;
  background-color: var(--erm-ppt-purple-light);
  z-index: calc(var(--title-z-index) + 10);
}
</style>
