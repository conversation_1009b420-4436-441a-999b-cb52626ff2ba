<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-05 19:37:53
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-08 18:08:28
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/sign/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <Title title="四、部门签单完成情况分析"></Title>
    <Table :column="column" :data="dataList" :padding="padding"></Table>
    <Textarea :text="deptSigning.desc"></Textarea>
  </div>
</template>
<script setup>
import Title from './../title/index.vue';
import Table from './../table/index.vue';
import Textarea from './../textarea/index.vue';
import { ref, computed, inject, onMounted } from 'vue';

const isFullscreen = inject('isFullscreen');

const padding = computed(() => {
  return {
    th: `${!isFullscreen.value ? '5' : '14'}px 10px`,
    td: `${!isFullscreen.value ? '5' : '14'}px 10px`
  };
});

const props = defineProps({
  deptSigning: {
    type: Object,
    default: {}
  }
});
const dataList = computed(() => {
  return props.deptSigning?.list?.map(item => {
    return {
      ...item,
      completionProgress: (Number(item.completionProgress) || 0).toFixed(2),
      incrDecreaseRate: (Number(item.incrDecreaseRate) || 0).toFixed(2)
    };
  });
});
const column = computed(() => {
  return [
    {
      title: '部门',
      dataIndex: 'dept',
      width: '22%',
      headStyle: {
        'text-align': 'left'
      },
      style: (index, row) => {
        if ([4, 7, 8].includes(index)) {
          return {
            'text-align': 'left',
            'text-indent': '3em'
          };
        }
        return { 'text-align': 'left' };
      }
    },
    {
      title: '实际签单',
      dataIndex: 'actualSigning',
      width: '10%',
      headStyle: {
        backgroundColor: 'var(--erm-ppt-purple)'
      },
      style: {
        // color: 'red'
      }
    },
    {
      title: '计划签单',
      dataIndex: 'planSigning',
      headStyle: {
        backgroundColor: 'var(--erm-ppt-purple)'
      },
      width: '13%'
    },
    {
      title: '完成进度(%)',
      dataIndex: 'completionProgress',
      headStyle: {
        backgroundColor: 'var(--erm-ppt-purple)'
      },
      width: '13%'
    },
    {
      title: '去年同期',
      dataIndex: 'synchronismLastYear',
      width: '13%'
    },
    {
      title: '增减额',
      dataIndex: 'incrDecrease',
      width: '13%',
      style: {
        // 'font-weight': '700'
      }
    },
    {
      title: '增减率(%)',
      dataIndex: 'incrDecreaseRate',
      width: '13%',
      style: {
        // 'font-weight': '700'
      }
    }
  ];
});
</script>
