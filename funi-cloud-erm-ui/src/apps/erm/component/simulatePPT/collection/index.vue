<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-05 11:02:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-08 18:14:05
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/collection/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="complete" ref="complete">
    <Title ref="titleRef" title="八、应收款项管理情况"></Title>
    <Textarea :text="receivableFileDesc"></Textarea>
    <div class="table-part"><Table :column="column" :data="accountReceivable" :padding="padding"></Table></div>
  </div>
</template>

<script setup>
import Title from './../title/index.vue';
import Table from './../table/index.vue';
import Textarea from './../textarea/index.vue';
import { ref, computed, onMounted, watch, unref, inject, nextTick } from 'vue';

const { default_size } = inject('ermPPTOption');
const sequence = inject('sequence');

const props = defineProps({
  accountReceivable: {
    type: Array,
    default: []
  },
  receivableFileDesc: {
    type: String,
    default: ''
  }
});
const isFullscreen = inject('isFullscreen');
const titleRef = ref();
const padding = computed(() => {
  return {
    th: `${!isFullscreen.value ? '5' : '7'}px 0`,
    td: `${!isFullscreen.value ? '5' : '7'}px 5px`
  };
});

const column = computed(() => {
  return [
    {
      title: '部门',
      dataIndex: 'deptName',
      width: '13%'
    },
    {
      title: '项目',
      dataIndex: 'projectName',
      width: '14%'
    },
    {
      title: '甲方',
      dataIndex: 'partyA',
      width: '13%'
    },
    {
      title: '年初余额',
      dataIndex: 'receiptAmount',
      width: '10%'
    },
    {
      title: '本期新增',
      dataIndex: 'currentIssueAdd',
      width: '10%'
    },
    {
      title: '本期回收',
      dataIndex: 'receivableAmount',
      width: '10%'
    },
    {
      title: '期末余额',
      dataIndex: 'overdueAmount',
      width: '10%',
      style: {
        color: 'red'
      }
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDays',
      width: '10%'
    },
    {
      title: '备注',
      dataIndex: 'notes',
      width: '10%'
    }
  ];
});
</script>
<style scoped lang="scss">
.complete {
  display: flex;
  flex-direction: column;
  height: 100%;
  .table-part {
    height: calc(100% - 98px);
    overflow-y: scroll;
  }
}
.erm-ppt_banner {
  padding: 0 50px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  overflow: auto;
}
</style>
