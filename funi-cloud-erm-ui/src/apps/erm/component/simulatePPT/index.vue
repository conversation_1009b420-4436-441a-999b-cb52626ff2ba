<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-04 18:30:10
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-08 15:21:14
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div :style="variableStyle" ref="box">
    <Complete v-show="sequence == 0" :targetBudget="targetBudget"></Complete>
    <Indicator v-show="sequence == 1" :targetCompare="targetCompare"></Indicator>
    <Cost v-show="sequence == 2" :costInput="costInput"></Cost>
    <Sign v-show="sequence == 3" :deptSigning="deptSigning"></Sign>
    <Refund v-show="sequence == 4" :deptReceipt="deptReceipt"></Refund>
    <Income v-show="sequence == 5" :deptIncome="deptIncome"></Income>
    <Profit v-show="sequence == 6" :companyProfits="companyProfits"></Profit>
    <Collection
      v-show="sequence == 7"
      :accountReceivable="accountReceivable"
      :receivableFileDesc="receivableFileDesc"
    ></Collection>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, provide, onBeforeUnmount } from 'vue';
import Complete from './complete/index.vue';
import Indicator from './indicator/index.vue';
import Cost from './cost/index.vue';
import Sign from './sign/index.vue';
import Refund from './refund/index.vue';
import Income from './income/index.vue';
import Profit from './profit/index.vue';
import Collection from './collection/index.vue';

const variableStyle = computed(() => {
  return {
    height: '100%',
    '--erm-ppt-purple': '#7578ec',
    '--erm-ppt-purple-dark': '#1419fc',
    '--erm-ppt-purple-light': '#dcddfa',
    '--erm-ppt-orange': '#f7b802',
    '--erm-ppt-default-size': default_size.value
  };
});
const box = ref();
const default_size = ref('14px');
const resizeObserver = ref();

const targetBudget = ref([]);
const targetCompare = ref([]);
const costInput = ref([]);
const deptSigning = ref({});
const deptReceipt = ref({});
const deptIncome = ref({});
const companyProfits = ref([]);
const receivableFileDesc = ref('');
const accountReceivable = ref([]);

const props = defineProps({
  sequence: {
    type: Number,
    default: 0
  },
  rowParams: {
    type: Object,
    default: {}
  }
});
const emits = defineEmits(['changeSequence']);

provide('ermPPTOption', {
  default_size
});
onMounted(() => {
  setWitdth();
  initResizeObserver();
  window.addEventListener('keydown', keydownFn);
});
const initResizeObserver = () => {
  resizeObserver.value = new ResizeObserver(handleResize);
  resizeObserver.value.observe(box.value);
};
const handleResize = entries => {
  setWitdth();
};

const setWitdth = () => {
  // 基数是 1200 / 14px
  let w = box.value?.offsetWidth;
  let coefficient = Number((14 / 1200).toFixed(2));
  let s = Number((w * coefficient).toFixed(2));
  default_size.value = `${s < 12 ? 12 : s}px`;
};

const getInfoData = async () => {
  let { id } = props.rowParams;
  if (!id) return;
  let {
    targetBudget: budget,
    targetCompare: compare,
    deptSigning: signing,
    deptReceipt: receipt,
    deptIncome: income,
    companyProfits: profits,
    receivableFileDesc: desc,
    accountReceivable: receivable,
    costInput: cost
  } = await $http.fetch('/erm/financialReport/info', {
    financialReportId: id
  });

  targetBudget.value = budget ? JSON.parse(budget) : [];
  targetCompare.value = compare ? JSON.parse(compare) : [];
  deptSigning.value = signing ? JSON.parse(signing) : {};
  deptReceipt.value = receipt ? JSON.parse(receipt) : {};
  deptIncome.value = income ? JSON.parse(income) : {};
  companyProfits.value = profits ? JSON.parse(profits) : {};
  accountReceivable.value = receivable ? JSON.parse(receivable) : [];
  receivableFileDesc.value = desc;
  costInput.value = cost ? JSON.parse(cost) : [];
  // console.log('targetBudget', deptReceipt.value);
};

const keydownFn = event => {
  if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
    let type = '';
    switch (event.key) {
      case 'ArrowUp':
        type = 'prev';
        break;
      case 'ArrowDown':
        type = 'next';
        break;
      case 'ArrowLeft':
        type = 'prev';
        break;
      case 'ArrowRight':
        type = 'next';
        break;
    }
    emits('changeSequence', type);
  }
};

onBeforeUnmount(() => {
  window.removeEventListener('keydown', keydownFn);
});

watch(
  () => props.rowParams,
  () => {
    // console.log('rowParams', props.rowParams);
    getInfoData();
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
