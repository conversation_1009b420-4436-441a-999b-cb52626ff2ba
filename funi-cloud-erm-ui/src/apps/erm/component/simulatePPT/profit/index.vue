<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-05 11:02:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-08 15:28:24
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/profit/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="complete" ref="complete">
    <Title ref="titleRef" title="七、各公司利润贡献情况"></Title>
    <div
      class="erm-ppt_banner"
      id="erm-complete-bar__profit"
      :style="{
        height: ppt_banner_height
      }"
    ></div>
  </div>
</template>

<script setup>
import Title from './../title/index.vue';
import { ref, computed, onMounted, watch, unref, inject, nextTick } from 'vue';
import * as echarts from 'echarts';
const { default_size } = inject('ermPPTOption');
const sequence = inject('sequence');
const props = defineProps({
  companyProfits: {
    type: Array,
    default: []
  }
});
const isFullscreen = inject('isFullscreen');
const titleRef = ref();
let chartBar = void 0;
const enum_obj = {
  zhjc: '智慧锦城',
  funi: '房联云码',
  cddz: '成都电子',
  ncdz: '南充电子',
  flty: '房联天用',
  jzb: '精坐标',
  mergeOffset: '合并抵消'
};
const ppt_banner_height = ref(`calc(100% - ${titleRef.value?.getHeight()}px - 40px)`);
const chartBarOption = computed(() => {
  let data = props.companyProfits[0] || {};
  Reflect.deleteProperty(data, 'profit');
  let dataName = ['合并利润', ...Object.keys(data).map(item => enum_obj[item])];
  let values = Object.keys(data).map(item => Number(data[item]));
  let dataValue = [$utils.fodash.sum(values), ...values];
  return {
    xAxis: {
      type: 'category',
      data: dataName,
      axisLabel: {
        fontSize: parseInt(default_size.value)
      }
    },
    yAxis: {
      type: 'value'
    },
    title: {
      text: '利润构成情况',
      left: 'center',
      textStyle: {
        fontSize: parseInt(default_size.value) * 2,
        fontWeight: '500'
      }
    },
    series: [
      {
        data: dataValue.map((item, idnex) => {
          return { value: item, itemStyle: { color: Number(item) > 0 ? '#f7b802' : '#7578ec' } };
        }),
        type: 'bar',
        label: {
          show: true, // 显示标签
          position: 'top', // 标签显示在柱子的顶部
          formatter: '{c}', // 标签内容格式，'{c}' 表示显示数据值
          textStyle: {
            fontSize: parseInt(default_size.value),
            color: '#1419fc'
          }
        }
      }
    ]
  };
});
const setChart = () => {
  let profitDom = document.querySelector(`#erm-complete-bar__profit`);
  if (profitDom) {
    chartBar = echarts.init(profitDom);
    chartBar.setOption(chartBarOption.value);
  }

  setTimeout(() => {
    chartBar.resize();
  });
};
watch(
  [default_size, isFullscreen, sequence],
  async () => {
    if (sequence?.value == 6) {
      await nextTick();
      ppt_banner_height.value = `calc(100% - ${titleRef.value?.getHeight()}px - 40px)`;
      await nextTick();
      setChart();
    }
  },
  { deep: true, immediate: true }
);
</script>
<style scoped>
.complete {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.erm-ppt_banner {
  padding: 0 50px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}
</style>
