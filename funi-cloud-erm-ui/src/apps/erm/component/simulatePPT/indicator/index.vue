<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-05 19:37:53
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-08 18:07:51
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/indicator/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="indicator">
    <Title title="二、主要指标同比情况分析"></Title>
    <div class="table-part">
      <Table :column="column" :data="data" :padding="padding"></Table>
    </div>
  </div>
</template>
<script setup>
import Title from './../title/index.vue';
import Table from './../table/index.vue';
import { ref, computed, inject } from 'vue';
const isFullscreen = inject('isFullscreen');

const padding = computed(() => {
  return {
    th: `${!isFullscreen.value ? '10' : '18'}px 0`,
    td: `${!isFullscreen.value ? '10' : '18'}px 10px`
  };
});

const props = defineProps({
  targetCompare: {
    type: Array,
    default: []
  }
});
const column = computed(() => {
  return [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: '20%'
    },
    {
      title: '当月累计',
      dataIndex: 'accumulatedInJanuary',
      width: '10%',
      style: {
        // color: 'red'
      }
    },
    {
      title: '去年同期',
      dataIndex: 'synchronismLastYear',
      width: '10%'
    },
    {
      title: '同比增减额',
      dataIndex: 'incrDecrease',
      width: '15%'
    },
    {
      title: '同比增减率(%)',
      dataIndex: 'incrDecreaseRate',
      width: '15%'
    },
    {
      title: '差异说明',
      dataIndex: 'desc',
      width: '30%',

      style: {
        // 'font-weight': '700',
        'text-align': 'left'
      }
    }
  ];
});

const data = computed(() => {
  return props.targetCompare.map(item => {
    return {
      ...item,
      incrDecrease: (Number(item.incrDecrease) || 0).toFixed(2),
      incrDecreaseRate: (Number(item.incrDecreaseRate) || 0).toFixed(2)
    };
  });
});
</script>
<style scoped lang="scss">
.indicator {
  height: 100%;
  .table-part {
    height: calc(100% - 98px);
    overflow-y: scroll;
  }
}
</style>
