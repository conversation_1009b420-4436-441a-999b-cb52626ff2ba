<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-05 11:02:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-07 17:35:50
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/complete/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="complete" ref="complete">
    <Title ref="titleRef" title="一、主要指标预算完成情况"></Title>
    <div
      class="erm-ppt_banner"
      :style="{
        '--erm-ppt-complete-len': data.length,
        height: ppt_banner_height
      }"
    >
      <div v-for="(item, index) in data" class="complete_box">
        <div class="erm-complete-name">
          <funi-icon :icon="item.icon"></funi-icon>
          <div class="erm-complete-name__text">{{ item.name }}</div>
        </div>
        <div class="erm-complete-bar">
          <div :id="`erm-complete-bar-${index}`"></div>
        </div>
        <div class="erm-complete-pie">
          <div :id="`erm-complete-pie-${index}`"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Title from './../title/index.vue';
import { ref, computed, onMounted, watch, unref, inject, nextTick } from 'vue';
import * as echarts from 'echarts';
const { default_size } = inject('ermPPTOption');
const props = defineProps({
  targetBudget: {
    type: Array,
    default: []
  }
});
const data = computed(() => {
  let arr = [
    {
      icon: 'heroicons:currency-dollar-20-solid',
      name: '销售回款',
      month: '',
      year: '',
      total: ''
    },
    {
      icon: 'prime:chart-line',
      name: '销售收入',
      month: '',
      year: '',
      total: ''
    },
    {
      icon: 'bxs:data',
      name: '利润总额',
      month: '',
      year: '',
      total: ''
    },
    {
      icon: 'tabler:settings',
      name: '研发投入',
      month: '',
      year: '',
      total: ''
    }
  ].map(item => {
    if (props.targetBudget && props.targetBudget.length) {
      let obj = props.targetBudget.find(items => items.type === item.name);
      item.month = obj.currentMonthCompleted;
      item.total = obj.accumulatedComplet;
      item.year = obj.annualTarget;
    }

    return item;
  });
  return arr;
});
const chartBarList = [];
const chartPieList = [];

const colors = ['#f7b802', '#7578ec', '#dcddfa'];
const complete = ref();
const resizeObserver = ref();
const titleRef = ref();
const ppt_banner_height = ref('auto');
onMounted(() => {
  initResizeObserver();
});

const setAllChart = () => {
  data.value.forEach((element, index) => {
    chartBarList[index] = echarts.init(document.querySelector(`#erm-complete-bar-${index}`));
    chartPieList[index] = echarts.init(document.querySelector(`#erm-complete-pie-${index}`));
    let { month, year, total } = element;
    let optionValues = [year, total, month];
    chartBarList[index].setOption(getBarOption(optionValues));
    chartPieList[index].setOption(getPieOption({ year, total }));
  });
};
const initResizeObserver = () => {
  resizeObserver.value = new ResizeObserver(handleResize);
  resizeObserver.value.observe(complete.value);
};
const handleResize = entries => {
  if (titleRef.value?.getHeight()) {
    ppt_banner_height.value = `calc(100% - ${titleRef.value?.getHeight()}px - 40px)`;
    resetChat();
  }
};

const resetChat = () => {
  setTimeout(() => {
    [...chartBarList, ...chartPieList].forEach(element => {
      element.resize();
    });
  });
};

watch(
  data,
  () => {
    setAllChart();
    resetChat();
  },
  {
    deep: true
  }
);

watch(
  default_size,
  async () => {
    setAllChart();
    resetChat();
  },
  {
    deep: true
  }
);

const getBarOption = value => {
  return {
    grid: {
      left: '3%',
      right: '10%',
      bottom: '1%',
      top: '1%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        show: false // 隐藏 x 轴上的数值标签
      },
      splitLine: {
        show: false // 隐藏 x 轴上的刻度线
      }
    },
    //
    yAxis: {
      type: 'category',
      data: ['年度目标', '累计完成', '当月完成'],
      axisLabel: {
        fontSize: parseInt(default_size.value) * 1.1,
        color: '#1419fc'
      }
    },
    series: [
      {
        type: 'bar',
        data: value.map((item, idnex) => {
          return { value: item, itemStyle: { color: colors[idnex] } };
        }),
        label: {
          show: true, // 显示标签
          position: 'right', // 标签显示在柱子的顶部
          formatter: '{c}', // 标签内容格式，'{c}' 表示显示数据值
          textStyle: {
            fontSize: parseInt(default_size.value),
            color: '#1419fc'
          }
        }
      }
    ]
  };
};

const getPieOption = param => {
  return {
    grid: {
      left: '3%',
      right: '10%',
      bottom: '0.1%',
      top: '0.1%',
      containLabel: true
    },
    title: {
      text: '预算完成率',
      left: 'center',
      top: 'bottom',
      textStyle: {
        fontSize: parseInt(default_size.value),
        fontWeight: 'bold',
        color: '#7578ec'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },

        label: {
          show: true,
          position: 'center',
          formatter: function (object) {
            let f = param.total < 0;
            let t = Math.abs(param.total);
            let p = Number(((t / param.year) * 100).toFixed(2));
            // percent = percent == 100 ? percent : Number((100 - percent).toFixed(2));
            return `${f ? '-' : ''}${!$utils.isNil(p) ? p : 0}%`;
          }
        },
        data: [
          { value: param.total, itemStyle: { color: '#7578ec' } },
          { value: param.year - param.total, itemStyle: { color: '#f7b802' } }
        ]
      }
    ]
  };
};
</script>
<style scoped>
.complete {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.erm-ppt_banner {
  padding: 0 50px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  /* flex-grow: 1; */
  /* height: 100%; */
  /* gap: 30px; */
}
.complete_box {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  height: calc((100% / var(--erm-ppt-complete-len)) - 20px);
}
.erm-complete-name {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--erm-ppt-orange);
  font-size: calc(var(--erm-ppt-default-size) * 1.5);
  flex: 0 0 auto;
  .erm-complete-name__text {
    padding: 10px;
    background-color: var(--erm-ppt-purple-light);
    color: var(--erm-ppt-purple-dark);
    margin-left: 30px;
  }
}
.erm-complete-bar {
  width: 100%;
  height: 100%;
  margin-left: 40px;
}
.erm-complete-pie {
  width: 100%;
}
.erm-complete-bar > div,
.erm-complete-pie > div {
  height: 100%;
  width: 100%;
}
</style>
