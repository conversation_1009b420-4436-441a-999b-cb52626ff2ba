<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-06 10:18:09
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-06 15:01:16
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/textarea/index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="textBox" v-if="text">{{ text }}</div>
</template>
<script setup>
const props = defineProps({
  text: {
    type: String,
    default: ''
  }
});
</script>
<style scoped>
.textBox {
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 5px;
  margin: 10px 0;
  white-space: pre-wrap;
  background: linear-gradient(to right, #dbdbdb, #ffffff);
  line-height: calc(var(--erm-ppt-default-size) * 2.2);
  font-size: calc(var(--erm-ppt-default-size) * 1.2);
  font-weight: 700;
  color: black;
  word-break: break-all;
}
</style>
