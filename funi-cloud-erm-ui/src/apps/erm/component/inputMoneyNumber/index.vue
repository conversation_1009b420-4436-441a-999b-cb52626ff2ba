<template>
  <div>
    <el-input-number
      v-if="props.isEdit"
      :controls="false"
      :maxlength="20"
      :precision="precision"
      v-bind="$attrs"
      v-model="localVal"
      style="width: 100%"
      @input="onInput"
      @blur="onBlur"
      @change="onChange"
    />
    <div v-else>{{ erm_intl(localVal) }}</div>
    <div class="chinese-amount" style="font-size: 12px" v-if="props.moneyCapitalShow && localVal">
      {{ moneyCapital(localVal) }}
    </div>
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import { erm_intl } from '@/apps/erm/hooks/intl';
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: true
  },
  unit: {
    type: String,
    default: '元'
  },
  moneyCapitalShow: {
    type: Boolean,
    default: false
  },
  precision: {
    type: Number,
    default: 2
  },
  showDefault: {
    type: Boolean,
    default: true
  }
});
const localVal = ref('');

const emit = defineEmits(['update:modelValue', 'change']);

watch(localVal, () => {
  emit('update:modelValue', Number(localVal.value));
});

watch(
  () => props.modelValue,
  () => {
    localVal.value = !props.modelValue && props.showDefault ? 0 : props.modelValue ? props.modelValue : void 0;
  },
  {
    deep: true,
    immediate: true
  }
);

const onInput = e => {
  localVal.value = e;
  emit('change', localVal.value);
};
const onBlur = e => {
  localVal.value = e.target.value;
  if (localVal.value || props.showDefault) {
    emit('change', Number(localVal.value));
    emit('blurChange', Number(localVal.value));
  }
};
const onChange = e => {
  localVal.value = e;
  emit('changes', Number(localVal.value));
};

const moneyCapital = money => {
  if (!money) {
    return '';
  } else {
    let isNegativeNumber = money * 1 < 0;
    money = Math.abs(money);
    if (props.unit === '万元') {
      money = money * 10000;
    }
    let cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'); //汉字的数字
    let cnIntRadice = new Array('', '拾', '佰', '仟'); //基本单位
    let cnIntUnits = new Array('', '万', '亿', '兆'); //对应整数部分扩展单位
    let cnDecUnits = new Array('角', '分', '毫', '厘'); //对应小数部分单位
    //var cnInteger = "整"; //整数金额时后面跟的字符
    let cnIntLast = '元'; //整型完以后的单位
    let maxNum = 999999999999999.9999; //最大处理的数字

    let IntegerNum; //金额整数部分
    let DecimalNum; //金额小数部分
    let ChineseStr = ''; //输出的中文金额字符串
    let parts; //分离金额后用的数组，预定义

    money = parseFloat(money);

    if (money >= maxNum) {
      alert('超出最大处理数字');
      return '';
    }
    if (money == 0) {
      ChineseStr = cnNums[0] + cnIntLast;
      return ChineseStr;
    }
    money = money.toString(); //转换为字符串
    if (money.indexOf('.') == -1) {
      IntegerNum = money;
      DecimalNum = '';
    } else {
      parts = money.split('.');
      IntegerNum = parts[0];
      DecimalNum = parts[1].substr(0, 4);
    }
    if (parseInt(IntegerNum, 10) > 0) {
      //获取整型部分转换
      let zeroCount = 0;
      let IntLen = IntegerNum.length;
      for (let i = 0; i < IntLen; i++) {
        let n = IntegerNum.substr(i, 1);
        let p = IntLen - i - 1;
        let q = p / 4;
        let m = p % 4;
        if (n == '0') {
          zeroCount++;
        } else {
          if (zeroCount > 0) {
            ChineseStr += cnNums[0];
          }
          zeroCount = 0; //归零
          ChineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
        }
        if (m == 0 && zeroCount < 4) {
          ChineseStr += cnIntUnits[q];
        }
      }
      ChineseStr += cnIntLast;
      //整型部分处理完毕
    }
    if (DecimalNum != '') {
      //小数部分
      let decLen = DecimalNum.length;
      for (let i = 0; i < decLen; i++) {
        let n = DecimalNum.substr(i, 1);
        if (n != '0') {
          ChineseStr += cnNums[Number(n)] + cnDecUnits[i];
        }
      }
    }
    if (ChineseStr == '') {
      ChineseStr += cnNums[0] + cnIntLast;
    }

    return `${isNegativeNumber ? '负' : ''}${ChineseStr}`;
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-input__inner) {
  text-align: left;
}
.chinese-amount {
  font-size: 12px;
  line-height: 13px;
  color: var(--el-text-color-regular);
}
</style>
