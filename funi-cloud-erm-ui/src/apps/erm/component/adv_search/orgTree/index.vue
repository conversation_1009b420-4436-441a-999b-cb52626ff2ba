<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-11-02 17:51:17
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-30 17:46:16
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/adv_search/orgTree/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <!-- el-select__tags -->

  <el-tree-select
    ref="elTreeSelectDom"
    class="funi-erm-adv-search__orgtree"
    v-model="valueStrictly"
    :data="orgList"
    multiple
    :render-after-expand="false"
    show-checkbox
    node-key="id"
    check-on-click-node
    style="width: 240px"
    @check="check"
    @removeTag="removeTag"
    :props="{
      value: 'id',
      label: 'orgName',
      children: 'children'
    }"
  />
</template>
<script setup>
import { onMounted, ref, watch, unref, nextTick } from 'vue';
defineOptions({
  typeName: 'SLOT_ORG'
});
const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  modelValue: {
    type: Array,
    default: []
  }
});

const orgList = ref([]);
const valueStrictly = ref(void 0);
const elTreeSelectDom = ref();
onMounted(() => {
  _init();
});

const _init = async () => {
  let { list } = await $http.fetch('/csccs/orgList/orgTree');
  orgList.value = list;
};

const check = async (obj, { checkedKeys }) => {
  valueStrictly.value = checkedKeys;
  emit('update:modelValue', valueStrictly.value);
};
const removeTag = e => {
  valueStrictly.value = valueStrictly.value.filter(item => !e.includes(item));
  emit('update:modelValue', valueStrictly.value);
};

watch(
  () => props.modelValue,
  newVal => {
    valueStrictly.value = props.modelValue;
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style scoped>
.funi-erm-adv-search__orgtree :deep(.el-select__tags) {
  max-height: 100px;
  overflow-y: auto;
}
</style>
