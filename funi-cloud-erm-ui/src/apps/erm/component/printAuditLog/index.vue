<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-10-09 19:54:08
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-04 10:37:15
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/printAuditLog/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="">
    <GroupTitle title="流程日志" />
    <PrintTable :columns="columns" :dataList="logList"></PrintTable>
  </div>
</template>
<script setup lang="jsx">
import { ref, watch, reactive } from 'vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import PrintTable from './../printTable/index.vue';
const api = '/bpmn/businessManage/getBusinessProcessInstanceInfoByBusinessId';
const props = defineProps({
  businessId: String
});

console.log(props, '23232323');
const logList = ref([]);
const columns = ref([
  {
    title: '操作人',
    dataIndex: 'operatorName',
    width: '20',
    customRender: ({ row, index }) => {
      return row.operator.operatorName;
    }
  },
  {
    title: '节点名称',
    dataIndex: 'activityName',
    width: '20'
  },
  { title: '操作时间', width: '25', dataIndex: 'executeTime' },
  { title: '内容', width: '35', dataIndex: 'content' }
]);

const rowStyle = () => {
  return {};
};
const getInfo = async () => {
  if (!props.businessId) {
    return;
  }
  let data = await $http.post(api, {
    businessId: props.businessId
  });
  let list = [];
  data.activityInfos.forEach(item => {
    list.push(...item.businessUserRecordInfos);
  });

  logList.value = list || [];
};
defineExpose({
  getInfo
});
</script>
<style scoped>
:deep(.funi-curd__table .activityName),
:deep(.funi-curd__table .executeTime) {
  width: 30%;
}
:deep(.funi-curd__table .content) {
  width: 40%;
}
</style>
