<template>
  <el-tree-select
    v-model="modelVal"
    :data="isDetp ? deptTreeData : companyTreeData"
    check-strictly
    v-bind="$attrs"
    :props="propsMap"
    filterable
    expand-on-click-node
    default-expand-all
    @node-click="nodeClick"
  />
</template>

<script setup>
import { onMounted, ref, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { findCurrentUserCompanyHttp, findCurrentUserDeptHttp } from '@/apps/erm/config/config.jsx';
import { useAppStore } from '@/stores/useAppStore';
const route = useRoute();

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  params: {
    type: Object,
    default: () => {}
  },
  propsMap: {
    type: Object,
    default: () => {}
  },
  isDetp: {
    type: Boolean,
    default: false
  },
  api: {
    type: String,
    default: ''
  },
  init: {
    type: <PERSON>olean,
    default: false
  },
  isWatchAll: {
    type: Boolean,
    default: false
  },
  paramsMap: {
    type: Object,
    default: () => {
      return {
        id: 'id'
      };
    }
  }
});

const emits = defineEmits(['update:modelValue', 'change']);

const propsMap = {
  value: props.propsMap?.value,
  label: props.propsMap?.label,
  children: props.propsMap?.children
};

const appStore = useAppStore();
let user = appStore.user;

let modelVal = ref(void 0);
let _init = true;
let deptTreeData = ref([]);
let companyTreeData = ref([]);
let defaultUserInfo = {
  id: user.id,
  name: user.username
};

const setModelValue = async () => {
  if (props.init) {
    let res = await $http.fetch('erm/employee/info', { employeeId: defaultUserInfo.id });
    if (!modelVal.value) {
      if (!props.isDetp) {
        modelVal.value = res.employeeVo?.contractCompanyId;
        emits('update:modelValue', res.employeeVo?.contractCompanyId);
        emits('change', res.employeeVo?.contractCompanyId, res.employeeVo?.companyName);
      } else {
        modelVal.value = res.employeeVo?.deptId;
        emits('update:modelValue', res.employeeVo?.deptId);
        emits('change', res.employeeVo?.deptId, res.employeeVo?.deptName);
      }
    }
  }
};

const nodeClick = e => {
  emits('change', e[props.propsMap?.value], e[props.propsMap?.label]);
  emits('update:modelValue', e[props.propsMap?.value]);
};

const getTreeData = async () => {
  let res;
  if (props.isDetp) {
    if (props.api) {
      res = await $http.fetch(props.api, props.params);
    } else {
      res = await findCurrentUserDeptHttp(Object.assign({ isWatchAll: props.isWatchAll }, props.params));
    }
    deptTreeData.value = res.list;
  } else {
    res = await findCurrentUserCompanyHttp({ isWatchAll: props.isWatchAll });
    companyTreeData.value = res.list;
  }
};

watch(
  () => props.params?.[props.paramsMap.id],
  newVal => {
    if (newVal) {
      console.log(newVal, 'paramsMapparamsMapparamsMap');
      if (!_init || (props.init && route.query.type !== 'add')) {
        deptTreeData.value = [];
        // modelVal.value = '';
      }

      getTreeData();
      _init = false;
    }
  },
  { immediate: true }
);

watch(
  () => props.modelValue,
  newVal => {
    if (newVal) {
      modelVal.value = newVal;
    } else {
      modelVal.value = '';
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (!props.isDetp) {
    getTreeData();
  }
  setModelValue();
});
</script>
