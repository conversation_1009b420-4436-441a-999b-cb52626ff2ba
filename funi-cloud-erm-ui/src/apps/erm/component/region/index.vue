<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-14 12:34:14
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-07 20:38:05
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/region/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    v-if="isEdit"
    class="cascadeGroup"
    :style="{
      '--lvl': Math.min(lvl, 3),
      '--showAddressFull': showAddressFull ? addressFullWidth : '0px'
    }"
  >
    <div class="cascadeItem" v-for="(item, index) in regionList" :key="item.key">
      <el-select
        v-model="data.value[item.key].value"
        teleported
        style="width: 100%"
        @change="
          e => {
            changeSelect(true, index, e);
          }
        "
        :teleported="false"
        :placeholder="item.name"
      >
        <el-option
          v-for="(itemOption, indexOption) in item.list"
          :label="itemOption.label"
          :value="itemOption.value"
          :key="itemOption.value"
        />
      </el-select>
    </div>
    <div class="addressFull" v-if="showAddressFull">
      <el-input v-model="data.addressFull" @change="changeInput" placeholder="详细地址" />
    </div>
  </div>
  <div v-else>
    <span v-for="item in regionList">
      {{ valueEnd[item.key] ? valueEnd[item.key]?.label : '' }}
    </span>
    <span v-if="valueEnd.address"> - {{ valueEnd.address }} </span>
  </div>
</template>
<script setup>
import { reactive, computed, watchEffect, watch, unref, nextTick, onMounted } from 'vue';
import { useFormItem } from 'element-plus';
const formTiem = useFormItem();

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {
        province: { value: void 0, label: void 0 },
        city: { value: void 0, label: void 0 },
        district: { value: void 0, label: void 0 },
        address: void 0
      };
    }
  },
  lvl: {
    type: Number,
    default: 3
  },
  provinceHttp: {
    type: Function,
    default: undefined
  },
  cityHttp: {
    type: Function,
    default: undefined
  },
  districtHttp: {
    type: Function,
    default: undefined
  },
  isEdit: {
    type: Boolean,
    default: true
  },
  showAddressFull: {
    type: Boolean,
    default: true
  },
  addressFullWidth: {
    type: String,
    default: '190px'
  }
});
const emit = defineEmits(['update:modelValue']);

const data = reactive({
  value: {},
  provinceList: [],
  addressFull: undefined,
  cityList: [],
  districtList: [],
  timer: null,
  time: 1
});
const valueEnd = computed(() => {
  return {
    ...data.value,
    address: data.addressFull
  };
});

onMounted(() => {
  getprovinceList();
  getcityList();
  getdistrictList();
});

const regionList = computed(() => {
  let arr = [
    {
      key: 'province',
      name: '省',
      list: data.provinceList
    },
    {
      key: 'city',
      name: '市',
      list: data.cityList
    },
    {
      key: 'district',
      name: '区/县',
      list: data.districtList
    }
  ];
  let lvl = Math.min(props.lvl, 3);
  let list = arr.slice(0, lvl);
  list.splice();
  list.forEach((item, index) => {
    if (!data.value.hasOwnProperty(item.key)) {
      data.value[item.key] = { label: '', value: undefined };
    }
  });
  return list;
});

const initModelValue = async bool => {
  if (props.modelValue && Object.keys(data.value).length > 0)
    for (let key in props.modelValue) {
      if (data.value.hasOwnProperty(key)) {
        data.value[key] = props.modelValue[key];
      }
      if (data.value.hasOwnProperty(key) && props.modelValue[key].value && props[`${key}Http`]) {
        let index = regionList.value.findIndex(item => item['key'] == key);
        await changeSelect(false, index, props.modelValue[key].value);
      }
    }
  if (props.modelValue?.address) {
    data.addressFull = props.modelValue.address;
  }
};

const changeSelect = async (bool, index, e) => {
  console.log(bool, index, e);
  for (let i = 0; i < regionList.value.length; i++) {
    if (i == index) {
      let option = data[`${regionList.value[i].key}List`].filter(item => item.value == e);
      data.value[regionList.value[i].key].label = option[0]?.label;
      data.value[regionList.value[i].key].value = option[0]?.value;
    }
    if (i > index && e) {
      data[`${regionList.value[i].key}List`] = [];
      data.value.hasOwnProperty(regionList.value[i].key)
        ? (data.value[regionList.value[i].key] = {
            label: '',
            value: undefined
          })
        : '';
      console.log(regionList.value[i].key, i - index == 1);
      if (regionList.value[i].key && i - index == 1) {
        await eval(`get${regionList.value[i].key}List(e)`);
      }
    }
  }
  await nextTick();
  if (bool && e) {
    emit('update:modelValue', unref(valueEnd));
    _validate();
  }
};

const getprovinceList = async () => {
  if (data.provinceList.length == 0 && props.provinceHttp) {
    let resData = await props.provinceHttp();
    data.provinceList = resData.map(item => {
      return {
        value: item.code,
        label: item.name
      };
    });
  }
};
const getcityList = async e => {
  if (e && props.cityHttp) {
    let resData = await props.cityHttp({ businessConfigCode: e });
    data.cityList = resData.map(item => {
      return {
        value: item.code,
        label: item.name
      };
    });
  }
};
const getdistrictList = async e => {
  if (e && props.districtHttp) {
    let resData = await props.districtHttp({ businessConfigCode: e });
    data.districtList = resData.map(item => {
      return {
        value: item.code,
        label: item.name
      };
    });
  }
};

const changeInput = async () => {
  await nextTick();
  emit('update:modelValue', unref(valueEnd));
  _validate();
};

watch(
  () => props.modelValue,
  async newval => {
    if (props.modelValue == undefined) {
      regionList.value.forEach((item, index) => {
        changeSelect(false, index, undefined);
      });
    }
    if (props.modelValue && props.modelValue?.province?.value && data.time == 1) {
      data.time = 99;
      // clearTimeout(data.timer);
      // data.timer = setTimeout(async () => {
      // clearTimeout(data.timer);
      await getprovinceList();
      initModelValue();
      // }, 800);
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const _validate = () => {
  formTiem.formItem.validate();
};
</script>
<style scoped>
.cascadeGroup {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cascadeItem {
  width: calc((100% - var(--showAddressFull)) / var(--lvl) - 10px);
  margin-right: 10px;
}

.cascadeItem:last-child {
  margin-right: 0px;
}

.addressFull {
  width: calc(var(--showAddressFull) + 10px);
  margin-left: 10px;
}
</style>
