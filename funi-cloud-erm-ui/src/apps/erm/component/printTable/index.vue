<template>
  <div
    class="print-tableBox scolleBox"
    :style="{
      '--thBgc': thBgc,
      '--tbBgc': tbBgc,
      '--tdPadding': tdPadding,
      '--thTdFS': thTd.fontSize ? thTd.fontSize : '14px',
      '--thTdFW': thTd.fontWeight ? thTd.fontWeight : '500',
      '--thTdFC': thTd.color ? thTd.color : '#000000',
      '--tbTdFS': tbTd.fontSize ? tbTd.fontSize : '14px',
      '--tbTdFW': tbTd.fontWeight ? tbTd.fontWeight : 'auto',
      '--tdTdFC': tbTd.color ? tbTd.color : '#000000',
      '--thTdP': thTd.padding ? thTd.padding : '5px 10px',
      '--tdTdP': tbTd.padding ? tbTd.padding : '5px 10px',
      '--borderColor': borderColor
    }"
  >
    <table :border="border" style="table-layout: fixed; width: 100%" :borderColor="borderColor" cellspacing="0">
      <thead>
        <tr>
          <td
            v-for="(item, index) in columns"
            :style="{
              width: item.width ? item.width + '%' : 'auto',
              'text-align': item.align ? item.align : 'left',
              'word-break': 'break-word'
            }"
            :key="item.dataIndex"
          >
            {{ item.title }}
          </td>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in tableList" :key="item[rowKey] || index">
          <td
            v-for="(item_c, index_c) in columns"
            :key="item[rowKey] + item_c.dataIndex"
            :style="{
              'text-align': item_c.align ? item_c.align : 'left',
              width: item_c.width ? item_c.width + '%' : 'left',
              'word-break': 'break-word'
            }"
          >
            <component :is="contentRender(item_c, tableList[index], index)" />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
<script setup lang="jsx">
import { isVNode, ref, watch, computed, nextTick } from 'vue';
const uuid = ref('');

const props = defineProps({
  columns: {
    type: Array,
    default: () => []
  },
  rowKey: {
    type: String,
    default: 'id'
  },
  dataList: {
    type: Array,
    default: () => []
  },
  thBgc: {
    // thead背景色
    type: String,
    default: '#fff'
  },
  tbBgc: {
    // tbody背景色
    type: String,
    default: '#fff'
  },
  tdPadding: {
    type: String,
    default: '5px 10px'
  },
  borderColor: {
    // 表格颜色
    type: String,
    default: '#ebeef5'
  },
  thTd: {
    type: Object,
    default: () => {
      return {
        fontSize: '14px',
        fontWeight: 'auto',
        color: '#000000',
        padding: '8px'
      };
    }
  },
  tbTd: {
    type: Object,
    default: () => {
      return {
        fontSize: '12px',
        fontWeight: 'auto',
        color: 'var(--el-text-color)',
        padding: '8px'
      };
    }
  },
  border: {
    type: String,
    default: '1'
  }
});
const tableList = computed(() => {
  return props.dataList || [];
});
function contentRender(item, row, index) {
  const content = item.customRender ? item.customRender({ row, index }) : row[item.dataIndex];
  return isVNode(content) ? content : <span>{content || '--'}</span>;
}
</script>
<style scoped>
::-webkit-scrollbar {
  width: 0 !important;
}

.print-tableBox {
  width: 100%;

  border: 1px solid var(--borderColor);
}

.scolleTable {
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  margin-top: -1px;
  margin-left: -1px;
  /* overflow-y: auto; */
}

table {
  width: 100%;
  border-collapse: collapse;
}

thead tr {
  background-color: var(--thBgc);
}

thead > tr > td {
  font-size: var(--thTdFS);
  font-weight: var(--thTdFW);
  color: var(--thTdFC);
  padding: var(--thTdP);
  box-sizing: border-box;
}

tbody {
  background-color: var(--tbBgc);
}

tbody > tr > td {
  font-size: var(--tbTdFS);
  font-weight: var(--tbTdFW);
  color: var(--tdTdFC);
  padding: var(--tdTdP);
  box-sizing: border-box;
  overflow: hidden;
}
</style>
