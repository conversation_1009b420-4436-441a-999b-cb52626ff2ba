<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-16 09:54:17
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-11 15:14:53
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/ermSelect/labelIndex.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div style="width: 100%">
    {{ getLabel }}
  </div>
</template>
<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { useFormItem } from 'element-plus';

const formTiem = useFormItem();
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  code: {
    type: String,
    default: ''
  },
  auto: {
    type: Boolean,
    default: true
  },
  options: {
    type: Array,
    default: () => []
  }
});
const emit = defineEmits(['update:modelValue']);
const list = ref([]);

const uuid = ref($utils.guid());
const getLabel = computed(() => {
  if (uuid.value && props.modelValue && list.value && list.value.length) {
    let data = list.value.filter(item => props.modelValue === item.value);
    if (data[0]) {
      return data[0].name;
    } else {
      return '--';
    }
  }
  return '--';
});
watch(
  () => props.modelValue,
  newVal => {
    uuid.value = $utils.guid();
    emit('update:modelValue', newVal);
    _validate();
  }
);

watch(list, () => {
  uuid.value = $utils.guid();
});
watch(
  () => props.options,
  () => {
    list.value = props.options;
    uuid.value = $utils.guid();
  }
);

onMounted(() => {
  if (props.code && props.auto) {
    getList();
  } else {
    list.value = props.options;
  }
});
const getList = () => {
  $http.fetch(ermGlobalApi.dictList, { typeCode: props.code }).then(res => {
    list.value = res.list;
  });
};
const _validate = () => {
  formTiem.formItem.validate();
};
</script>
