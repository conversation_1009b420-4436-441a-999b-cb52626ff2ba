<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-16 09:54:17
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-26 17:06:01
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/ermSelect/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div style="width: 100%">
    <el-select clearable style="width: 100%" v-bind="$attrs" placeholder="请选择">
      <el-option
        v-for="item in list"
        :key="item.code"
        :disabled="props.disabledList.includes(item.code)"
        @click="() => optionClick(item)"
        :label="item.name"
        :value="item.code"
      />
    </el-select>
  </div>
</template>
<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { useAppStore } from '@/stores/useAppStore';

const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  auto: {
    type: Boolean,
    default: true
  },
  options: {
    type: Array,
    default: () => []
  },
  disabledList: {
    type: Array,
    default: () => []
  },
  customerFetch: {
    type: Boolean,
    default: false
  },
  customerApi: {
    type: String,
    default: ''
  },
  initOption: {
    type: Boolean,
    default: false
  },
  customrParams: {
    type: String,
    default: () => {}
  },
  defaultProps: {
    type: String,
    default: () => {
      return {
        name: '',
        code: ''
      };
    }
  },
  exclude: {
    type: Array,
    default: () => []
  }
});
console.log(props, '12312312321');

const emits = defineEmits(['optionClick', 'patchOption']);
const store = useAppStore();
const list = ref([]);
const optionClick = e => {
  emits('optionClick', e, list.value);
};

const init = async () => {
  await nextTick();
  if (props.code && props.auto) {
    getList();
  } else {
    list.value = props.options;
  }
};

onMounted(() => {
  init();
});
watch(
  () => props.options,
  async () => {
    if (props.options && props.options.length) await init();
  },
  {
    immediate: true,
    deep: true
  }
);

const getList = () => {
  $http
    .fetch(
      props.customerFetch && props.customerApi ? props.customerApi : ermGlobalApi.dictList,
      props.customerFetch && props.customrParams
        ? props.customrParams
        : { typeCode: props.code, clientId: store.system.clientId }
    )
    .then(res => {
      console.log(res, '1231231239999');
      if (props.customerFetch) {
        res.list.forEach(item => {
          item['name'] = item[props.defaultProps.name];
          item['code'] = item[props.defaultProps.code];
        });
        list.value = res.list.filter(item => props.exclude.indexOf(item.code) < 0);
        if (props.initOption) {
          if (props.options && props.options.length > 0) {
            list.value = props.options;
          } else {
            emits('patchOption', list.value);
          }
        }
      } else {
        list.value = res.filter(item => props.exclude.indexOf(item.code) < 0);
      }
    });
};
</script>
