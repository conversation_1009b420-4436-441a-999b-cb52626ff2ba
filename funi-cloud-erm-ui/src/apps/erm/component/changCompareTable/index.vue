<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-10-27 14:40:09
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-27 17:06:27
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/changCompareTable/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div style="margin: 10px 0 0">
    <div class="changCompareTable-title" @click="open = !open">
      变更前
      <el-icon
        class="changCompareTable-icon"
        :class="{
          open
        }"
        ><ArrowRight
      /></el-icon>
    </div>
    <div
      class="tableBox"
      :class="{
        open
      }"
    >
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
const open = ref(false);
</script>

<style scope>
.changCompareTable-title {
  font-weight: bolder;
  display: flex;
  align-items: center;
  margin-left: 8px;
  cursor: pointer;
}
.tableBox {
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s;
}
.tableBox.open {
  max-height: 999px;
  transition: all 0.4s;
}
.changCompareTable-icon {
  transition: all 0.2s;
  transform: rotate(0deg);
}
.changCompareTable-icon.open {
  transition: all 0.2s;
  transform: rotate(90deg);
}
</style>
