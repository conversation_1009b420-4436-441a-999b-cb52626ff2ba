<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-09-11 14:07:59
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-12 15:57:15
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/component/auditDialog/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-dialog v-bind="$attrs" v-model="visible" :onConfirm="onConfirm" :onCancel="onCancel" :title="title">
    <funi-form @get-form="setForm" :schema="schema" :col="1" :rules="rules" />
  </funi-dialog>
</template>

<script lang="jsx" setup>
import { computed, ref, watch } from 'vue';
const formValue = ref();
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: '请输入办理原因'
  },
  required: {
    type: Boolean,
    default: true
  },
  callback: {
    type: Function,
    default: () => { }
  }
});
const emits = defineEmits(['update:modelValue', 'getData']);
const visible = ref(false);
const schema = computed(() => {
  return [
    {
      label: '原因',
      component: 'el-input',
      prop: 'reason',
      props: {
        type: 'textarea',
        maxlength: 1000
      }
    }
  ];
});
const rules = computed(() => {
  return props.required
    ? {
      reason: [{ required: true, message: '必填', trigger: 'blur' }]
    }
    : void 0;
});
const setForm = e => {
  formValue.value = e;
};

const onConfirm = async () => {
  let { reason } = formValue.value.getValues();
  let { isValid } = await formValue.value.validate();
  if (!isValid) return
  emits('getData', formValue.value.getValues());
  props.callback(reason);
  visible.value = false;
};
const onCancel = () => {
  visible.value = false;
  formValue.value = void 0;
};
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
  },
  { immediate: true, deep: true }
);

watch(
  () => visible.value,
  newVal => {
    emits('update:modelValue', newVal);
  },
  { immediate: true, deep: true }
);
</script>
<style lang="scss" scoped></style>
