<template>
  <el-form
    ref="elFormRef"
    v-bind="$attrs"
    status-icon
    scroll-to-error
    label-position="right"
    require-asterisk-position="left"
    :inline="inline"
    :model="formModel"
    :class="['funi-form-print']"
    @submit.native.prevent
    :style="{
      '--column-start': columnStart
    }"
  >
    <div :class="['funi-form-print-grid', { expand }]">
      <template v-for="item in validSchema" :key="item.prop">
        <div class="funi-form-print-grid__item-label">{{ item.label }}</div>
        <el-form-item v-bind="item" class="funi-form-print-grid__item el-form-item__label-hidden">
          <!-- default -->
          <template #default>
            <slot :name="item.slots.default" v-bind="{ item, formModel }">
              <component
                :is="componentProp(item.component, { item, formModel })"
                v-model="formModel[item.prop]"
                v-bind="item.props || {}"
                v-on="item.on || {}"
              />
            </slot>
          </template>
          <!-- label -->
          <template #label>
            <slot :name="item.slots.label" v-bind="{ item, formModel }">
              {{ item.label || '' }}
            </slot>
          </template>
        </el-form-item>
      </template>
    </div>
  </el-form>
  {{ c }}
</template>

<script setup lang="jsx">
import { computed, onMounted, ref, unref } from 'vue';

defineOptions({
  name: 'FuniFormV2',
  inheritAttrs: false
});

// Props
const props = defineProps({
  /** 行内模式 */
  inline: Boolean,

  /** 可配置多列item，最多4列 */
  col: Number,

  /** 表单选项配置 */
  schema: Array,
  border: { type: Boolean, default: true },
  expand: Boolean
});

// Emits
const emit = defineEmits(['get-form']);

// Properties
const elFormRef = ref();
const formModel = ref({});

// Computed Properties
const validSchema = computed(() => {
  return (
    props.schema
      //过滤掉prop不存在的配置
      .filter(item => {
        let hidden = item?.hidden;
        if (typeof item?.hidden === 'function') {
          hidden = item?.hidden({ item, formModel: unref(formModel) });
        }
        return $utils.isString(item.prop) && !!item.prop && !hidden;
      })
      .map(item => {
        const slots = Object.assign({ default: `${item.prop}_default`, label: `${item.prop}_label` }, item.slots || {});
        return Object.assign(item, { slots });
      })
  );
});
const columnStart = computed(() => {
  let len = validSchema.value?.length || 0;
  return (len * 2) % 4;
});

const isHidden = item => {
  if ($utils.isBoolean(item.hidden)) return item.hidden;

  if ($utils.isFunction(item.hidden)) {
    return item.hidden({ item, formModel: unref(formModel) });
  }
  return false;
};

// 默认ElCol响应式布局配置
const defaultColProps = computed(() => {
  //最多3列 默认1列
  const colCount = Math.max(1, Math.min(4, props.col || 1));
  const span = 24 / colCount;
  const middleSpan = Math.max(span, 12);
  return { xs: 24, sm: middleSpan, md: middleSpan, lg: span, xl: span };
});

// Lifecycle Hooks
onMounted(() => {
  emit(
    'get-form',
    Object.assign({}, unref(elFormRef), {
      validate,
      getValues,
      setValues,
      validateField,
      resetFields,
      scrollToField,
      clearValidate
    })
  );
});

// Methods
function componentProp(component, props) {
  Object.assign(props, { value: props.formModel[props.item.prop] });
  if ($utils.isString(component)) return component;
  if ($utils.isFunction(component)) return component(props);
  const value = props.formModel[props.item.prop];
  return <div style="word-break: break-all;">{value !== 0 ? value || '--' : value}</div>;
}

function getColProps(formItem) {
  if (props.inline) return {};
  const col = formItem.colProps || {};
  if (!!col.span) return col;
  return { ...unref(defaultColProps), ...col };
}

async function validate() {
  const fields = unref(validSchema)
    .filter(s => !isHidden(s))
    .map(item => item.prop);
  return validateField(fields);
}

async function validateField(field = []) {
  try {
    const isValid = await elFormRef.value.validateField(field);
    return Promise.resolve({ isValid, error: null, values: unref(formModel) });
  } catch (error) {
    return Promise.resolve({ isValid: false, error, values: unref(formModel) });
  }
}

function resetFields(props) {
  unref(elFormRef).resetFields(props);
}

function scrollToField(prop) {
  unref(elFormRef).scrollToField(prop);
}

function clearValidate(props) {
  unref(elFormRef).clearValidate(props);
}

function getValues() {
  return unref(formModel);
}

function setValues(values) {
  formModel.value = Object.assign(unref(formModel), values || {});
}

defineExpose({
  elFormRef,
  formModel,
  validSchema,
  getValues,
  setValues,
  validate,
  validateField,
  resetFields,
  scrollToField,
  clearValidate
});
</script>

<style lang="less" scoped>
:deep(.el-form-item__label-hidden .el-form-item__label) {
  display: none;
}

:deep(.funi-curd) {
  padding: 0 !important;
}
</style>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
:deep() {
  @include el.b(form-item) {
    > .#{el.$namespace}-form-item__label:after,
    > .#{el.$namespace}-form-item__label-wrap > .#{el.$namespace}-form-item__label:after {
      content: ':';
      color: el.getCssVar('text-color', 'regular');
      margin-left: 4px;
    }

    > .#{el.$namespace}-form-item__content > *:nth-child(1) {
      width: 100% !important;
    }
  }
}
</style>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;

:deep() {
  .funi-form-print-grid {
    display: grid;
    grid-template-columns: repeat(2, auto 1fr);
    grid-template-rows: auto;
    gap: 1px;
    border: 1px solid #dcdfe6;
    box-shadow: inset 0 0 0 5000px #dcdfe6;
    box-sizing: border-box;
    &__item-label {
      display: inline-flex;
      align-items: center;
      justify-content: flex-end;
      flex: 0 0 auto;
      font-size: var(--el-form-label-font-size);
      color: var(--el-text-color-regular);
      height: auto;
      box-sizing: border-box;
      align-self: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: keep-all;
      align-self: stretch;
      box-shadow: inset 0 0 0 5000px #ebeef5;
      box-sizing: border-box;
      padding: 8px;
      &:after {
        content: ':';
        color: el.getCssVar('text-color', 'regular');
        margin-left: 4px;
      }

      &:empty:after {
        content: '';
      }
    }

    &__item {
      align-self: stretch;
      box-shadow: inset 0 0 0 5000px #fff;
      box-sizing: border-box;
      padding: 8px !important;
      margin-bottom: 0 !important;
      &:last-child {
        grid-column-start: var(--column-start);
        grid-column-end: 5;
      }
    }
  }
}
</style>
