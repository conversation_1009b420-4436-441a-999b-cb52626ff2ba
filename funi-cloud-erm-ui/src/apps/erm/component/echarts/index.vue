<template>
  <div id="echarts"
    ref="chartRef"
    :style="echartsStyle">
  </div>
</template>

<script setup name="ECharts">
import { ref, onMounted, onBeforeUnmount, watch, computed, markRaw, nextTick } from "vue";
import echarts from "./config";
import { useDebounceFn } from "@vueuse/core";


const props = defineProps({
  resize: {
    type: Boolean,
    default: true
  },
  theme: {
    type: Object,
    default: () => { }
  },
  width: {
    type: Number,
    default: 0,
  },
  height: {
    type: Number,
    default: 0
  },
  onClick: {
    type: Function,
    default: () => { }
  },
  option: {
    type: Object,
    default: () => { }
  },
  renderer: {
    type: String,
    default: 'canvas'
  },
  show: {
    type: Boolean,
    default: false
  }
})
const echartsStyle = computed(() => {
  return props.width || props.height
    ? { height: props.height + "px", width: props.width + "px" }
    : { height: "100%", width: "100%" };
});
const chartRef = ref();
const chartInstance = ref();

const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(props.option, { notMerge: true });
  }
};

watch(props, () => {
  draw();
});

const handleClick = (event) => props.onClick && props.onClick(event);

const init = () => {
  if (!chartRef.value) return;
  chartInstance.value = echarts.getInstanceByDom(chartRef.value);
  if (!chartInstance.value) {
    chartInstance.value = markRaw(
      echarts.init(chartRef.value, props.theme, {
        renderer: props.renderer
      })
    );
    chartInstance.value.on("click", handleClick);
    draw();
  }
};

const resize = () => {
  if (chartInstance.value && props.resize) {
    chartInstance.value.resize({ animation: { duration: 300 }, width: document.querySelector('#echarts').offsetWidth, height: document.querySelector('#echarts').offsetHeight });
  }
};
const debouncedResize = useDebounceFn(resize, 300, { maxWait: 800 });
onMounted(() => {
  nextTick(() => init());
  window.addEventListener("resize", debouncedResize);
});

onBeforeUnmount(() => {
  chartInstance.value?.dispose();
  window.removeEventListener("resize", debouncedResize);
});

defineExpose({
  getInstance: () => chartInstance.value,
  resize,
  draw
});
</script>
<style lang="scss" scoped></style>
