<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:52:57
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-27 17:42:19
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/other/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
    <AuditDialog v-model="showDialog" :callback="dialogCallBack" />
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref } from 'vue';
import { useOtherColumns, useBtnsConfig } from './../hooks/other/other.jsx';
import { queryOtherContractSealListHttp } from './../hooks/other/api.js';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
import AuditDialog from '@/apps/erm/component/auditDialog/index.vue';
import { deleteBusiness, submitBusiness } from '@/apps/erm/config/business.js';
import { apiUrl, creatBusHttp } from '@/apps/erm/contract/hooks/other/api.js';
import { expotrFunction } from '@/apps/erm/config/config.jsx';

const router = useRouter();
const listPage = ref();
const queryData = ref();

onMounted(() => {});
const rowData = ref(void 0);
const showDialog = ref(false);
const dialogCallBack = ref(void 0);

const lodaData = async (params, queryParams) => {
  rowData.value = void 0;
  queryData.value = queryParams;
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryOtherContractSealListHttp({ ...params, ...queryParams });
  return data;
};
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig({
          addFunc,
          cancelFunc,
          hasValid: rowData.value?.row?.dicSealStatusCode === '1',
          exportFun
        }),
        // searchConfig: useSearchConfig(),
        lodaData: lodaData,
        fixedButtons: true,
        reloadOnActive: true,
        checkOnRowClick: true,
        columns: useOtherColumns({ seeDateils, editFunc, auditFunc, delFunc, infoFuinc }),
        on: {
          rowClick: e => {
            row_click(e);
          }
        }
      }
    }
  ];
});

const seeDateils = row => {
  router.push({
    name: 'ermContractOtherInfo',
    query: {
      title: row.contractName,
      bizName: '详情',
      type: 'info',
      tab: ['其他合同用印申请', row.contractName || '', '详情'].join('-'),
      id: row.dicOtherContractDoingBusCode ? row.lastId || row.id : row.id,
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const infoFuinc = row => {
  router.push({
    name: 'ermContractOtherInfo',
    query: {
      title: row.contractName,
      bizName: '审核',
      type: 'info',
      tab: ['其他合同用印申请', row.contractName || '', '业务详情'].join('-'),
      id: row.id,
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
const exportFun = () => {
  expotrFunction({ url: apiUrl.queryExport, params: queryData.value, FileName: '其他合同用印申请' });
};

const addFunc = () => {
  router.push({
    name: 'ermContractOtherAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: '其他合同用印申请-新建'
    }
  });
};

const editFunc = row => {
  let eum = {
    ERM_OTHER_CONTRACT_SEAL_ADD: 'add_edit',
    ERM_OTHER_CONTRACT_SEAL_CANCEL: 'cancel_edit'
  };
  router.push({
    name: 'ermContractOtherAdd',
    query: {
      bizName: '编辑',
      type: eum[row.dicBusinessTypeCode],
      tab: ['其他合同用印', row.contractName || '', '编辑'].join('-'),
      id: row.id,
      title: row.contractName || '其他合同用印',
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const auditFunc = row => {
  router.push({
    name: 'ermContractOtherInfo',
    query: {
      title: row.contractName || '其他合同用印',
      bizName: '审核',
      type: 'audit',
      id: row.id,
      tab: ['其他合同用印', row.contractName, '审核'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const cancelFunc = async () => {
  let { row } = rowData.value;
  await creatBusHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_OTHER_CONTRACT_SEAL_CANCEL'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractOtherAdd',
      query: {
        bizName: '新建',
        type: 'cancel_add',
        tab: ['其他合同用印', row.contractName, '作废'].join('-'),
        id: row.id,
        title: row.contractName || '其他合同用印',
        reason
      }
    });
  };
  showDialog.value = true;
};
const delFunc = async row => {
  // await deleteBusiness(row.businessId);
  await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  listPage.value.reload({ resetPage: false });
};
const row_click = e => {
  rowData.value = e;
};
</script>
