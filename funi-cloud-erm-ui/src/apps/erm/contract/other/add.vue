<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:53:05
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-09 16:28:34
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/other/add.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <funi-detail
      :bizName="_route.bizName"
      :auditButtons="[]"
      :steps="steps"
      :detailHeadOption="detailHeadOption || {}"
    />
    <SubmitSuccess ref="submitSuccessRef" />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, unref } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from './../component/other/baseInfo.vue';
import { submitBusiness } from '@/apps/erm/config/business.js';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
const route = useRoute();
const _route = ref(route.query);
const id = ref(route.query.id);
const businessId = ref();
const infoData = ref();
const submitSuccessRef = ref();
const steps = computed(() => {
  const { bizName, type, dicBusinessTypeCode, reason } = unref(_route);
  let change_end_cancel = ['change_add', 'end_add', 'cancel_add'].includes(type);
  let isEdit = !['audit', 'info', 'end_add', 'end_edit', 'cancel_add', 'cancel_edit'].includes(type);
  let arr = [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit,
        type,
        dicBusinessTypeCode,
        bizName,
        reason,
        lastId: ['新建', 'add'].includes(bizName) && change_end_cancel ? unref(_route).id : ''
      },
      on: {
        updateID: ({ id: v, businessId: bv }) => {
          id.value = v;
          businessId.value = bv;
        },
        infoUpdate: data => {
          infoData.value = data;
          let { id: v, businessId: bv } = data;
          id.value = v;
          businessId.value = bv;
        },
        otherUpdate: object => {
          Object.keys(object).forEach(key => {
            infoData.value[key] = object[key] || infoData.value[key];
          });
        }
      }
    },
    {
      title: '要件信息',
      preservable: false,
      type: 'FuniFileTable',
      props: {
        params: {
          businessId: businessId.value
        },
        callbackFun: submitBusinessFunc
      }
    }
  ];
  return arr;
});

const businessName = computed(() => {
  let { companyName, deptName, contractName } = unref(infoData);
  let str = `${companyName}-${deptName}-${contractName}`;
  return str;
});

const submitBusinessFunc = async () => {
  let eum = {
    add: 'ERM_OTHER_CONTRACT_SEAL_ADD',
    cancel_add: 'ERM_OTHER_CONTRACT_SEAL_CANCEL'
  };
  let code = '';
  const { bizName } = route.query;
  if (bizName === '新建' || bizName === 'add') {
    code = eum[route.query.type];
  } else {
    code = route.query.dicBusinessTypeCode;
  }
  await submitBusiness(code, businessId.value, 'SUBMIT', unref(businessName));
  submitSuccessRef.value.show();
};

const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点'
  };
  return obj;
});
</script>
