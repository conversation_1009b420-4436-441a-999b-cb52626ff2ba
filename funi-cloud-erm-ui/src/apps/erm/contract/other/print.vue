<template>
  <div v-loading="loading">
    <div class="print" :id="uuId">
      <div style="text-align: center">
        <h2>{{ contractName }}</h2>
      </div>
      <GroupTitle title="基本信息" />
      <funi-form-print :inline="false" :schema="schema" @get-form="setForm" :col="3" :border="true" />
      <!-- <GroupTitle title="合同相关方" />
      <PrintTable :columns="columns" :dataList="dataList" /> -->
      <PrintAuditLog ref="printAuditLog" :businessId="businessId" />
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onMounted, unref, nextTick } from 'vue';
import { useFormSchema } from './../hooks/other/baseInfo.jsx';
import { otherContractSealInfoHttp } from './../hooks/other/api.js';
import { Print } from '@/apps/erm/hooks/Print.js';
import funiFormPrint from '@/apps/erm/component/printForm/index.vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import PrintAuditLog from '@/apps/erm/component/printAuditLog/index.vue';
import PrintTable from '@/apps/erm/component/printTable/index.vue';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const businessId = ref();
const form = ref();
const dataList = ref([]);
const printAuditLog = ref();
const loading = ref(false);
const contractName = ref('');
const multiTab = useMultiTab();
const companyList = ref([]);
const setForm = e => {
  form.value = e;
};

const id = route.query.id || '76c94843-2130-4364-a903-84362e922298';
const uuId = 'print_' + $utils.guid();
const schema = computed(() => {
  return useFormSchema({
    isEdit: false,
    type: 'info',
    companyList: companyList.value
  });
});

const printFunc = () => {
  Print(document.querySelector('#' + uuId), {
    type: 'html',
    targetStyles: ['*'],
    scanStyle: false,
    maxWidth: 5000,
    callback: printCallback
  });
};

const printCallback = () => {
  multiTab.closeCurrentPage();
  router.push({
    name: 'ermContractOtherInfo',
    query: {
      title: unref(contractName) || '收款合同',
      bizName: '详情',
      type: 'info',
      tab: ['收款合同', unref(contractName), '详情'].join('-'),
      id: unref(id)
    }
  });
};
const columns = [
  {
    title: '签约方',
    dataIndex: 'dicMultipartyTypeName',
    width: '40'
  },
  {
    title: '签约方名称',
    dataIndex: 'customerName',
    width: '60'
  }
];
onMounted(() => {
  if (id) {
    getOtherInfo();
  }
});

const getOtherInfo = async () => {
  loading.value = true;
  let data = await otherContractSealInfoHttp({ otherContractSealId: id });
  contractName.value = data.otherContractSealVo.contractName;
  businessId.value = data?.otherContractSealVo?.businessId;
  await nextTick();
  await unref(printAuditLog)
    .getInfo()
    .finally(() => {
      loading.value = false;
    });
  let obj = {};
  schema.value.forEach(item => {
    if (item.prop == 'dicSealTypeCode') {
      obj[item.prop] = data.otherContractSealVo[item.prop] ? data.otherContractSealVo[item.prop].split(',') : [];
    } else if (['employeeObj', 'employeeName'].indexOf(item.prop) > -1) {
      obj['employeeObj'] = {
        id: data.otherContractSealVo['employeeId'],
        name: data.otherContractSealVo['employeeName']
      };
      obj['employeeName'] = data.otherContractSealVo['employeeName'];
    } else if (['partA', 'partAName', 'partB', 'partBName'].indexOf(item.prop) > -1) {
      let a = data?.sealConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '1';
      });
      let b = data?.sealConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '2';
      });

      if (a && a.length) {
        obj['partA'] = {
          id: a[0].customerId,
          name: a[0].customerName
        };
        obj['partAName'] = a[0].customerName;
      }
      if (b && b.length) {
        obj['partB'] = {
          id: b[0].customerId,
          name: b[0].customerName
        };
        obj['partBName'] = b[0].customerName;
      }
    } else {
      obj[item.prop] = data.otherContractSealVo[item.prop];
    }
  });
  companyList.value = [
    {
      value: data.otherContractSealVo.companyId,
      name: data.otherContractSealVo.companyName
    }
  ];

  dataList.value = data?.sealConMultipartyInfoVoList?.filter(el => {
    return el.dicMultipartyTypeCode !== '2' && el.dicMultipartyTypeCode !== '1';
  });
  await nextTick();
  form.value.setValues(obj);
  startPrint();
};

const startPrint = () => {
  setTimeout(() => {
    printFunc();
  }, 500);
};
</script>
<style scoped>
* {
  box-sizing: border-box;
}
.print {
  padding: 10px;
}
:deep(.group-title .title:after) {
  width: 0;
}
:deep(.group-title .title) {
  color: var(--el-text-color);
  padding-left: 0;
}
</style>
