<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:53:05
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-04 11:03:20
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/receipt/info.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <funi-detail
      :bizName="_route.bizName"
      :auditButtons="buttons"
      :steps="steps"
      :showWorkflow="true"
      :detailHeadOption="detailHeadOption || {}"
      :businessId="['info', 'audit'].includes(_route.type) ? businessId : void 0"
      :beforeAuditFn="beforeAuditFn"
      @auditClick="auditClick"
      @auditEvent="auditEvent"
    />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, watch, nextTick, unref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import BaseInfo from './../component/receipt/baseInfo.vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { auditEndFunc } from '@/apps/erm/config/business.js';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
const multiTab = useMultiTab();

const route = useRoute();
const id = ref(route.query.id);
const businessId = ref('');
const infoData = ref({});
const router = useRouter();

const _route = ref(route.query);
const buttons =
  _route.type == 'audit'
    ? ref([
        {
          key: '暂存',
          action: 'ts'
        }
      ])
    : [];
const baseDom = ref();
const steps = computed(() => {
  let arr = [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: unref(id),
        isEdit: !['audit', 'info'].includes(unref(_route).type),
        businessId: unref(businessId),
        type: unref(_route).type,
        ref: refFun
      },
      on: {
        updateID: ({ id: v, businessId: bv }) => {
          id.value = v;
          businessId.value = bv;
        },
        infoUpdate: data => {
          infoData.value = data;
          let { id: v, businessId: bv } = data;
          id.value = v;
          businessId.value = bv;
        }
      }
    },
    ...(businessId.value && (route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode)
      ? [
          {
            title: '办件记录',
            type: 'FuniWorkRecord',
            props: {
              objectListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              busListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              params: {
                businessId: businessId.value,
                dicBusinessTypeCode: route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode
              }
            },
            on: {
              onClick
            }
          }
        ]
      : [])
  ];
  return arr;
});
const refFun = e => {
  baseDom.value = e;
};
const beforeAuditFn = async ({ businessExecutionType }) => {
  if (['AGREE', 'SUBMIT'].includes(businessExecutionType)) {
    try {
      return await unref(baseDom).authFun();
    } catch {
      return Promise.reject();
    }
  }
  return Promise.resolve();
};
const auditClick = e => {
  // console.log('90909090', e);
  if (e == 'ts') {
    unref(baseDom).ts();
  }
};

const copy = () => {
  router.push({
    name: 'ermContractReceiptAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: ['收款单', unref(infoData).contractName || '', '新建'].join('-'),
      id: id.value,
      title: unref(infoData).contractName || '收款单'
    }
  });
};

const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    btns:
      unref(_route).type === 'info'
        ? [
            {
              name: '复制',
              props: { type: 'text' },
              on: { click: copy },
              menuAuth: 'ERM_CONTRACTRECRIPT_COPY'
            }
          ]
        : []
  };
  return obj;
});

const auditEvent = () => {
  auditEndFunc(() => {
    multiTab.closeCurrentPage();
  });
};
const onClick = row => {
  router.push({
    name: 'ermContractReceiptInfo',
    query: {
      title: row.contractName || '收款单',
      bizName: '详情',
      type: 'info',
      id: row.id,
      tab: ['收款单', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
</script>
