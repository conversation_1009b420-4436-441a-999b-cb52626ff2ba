<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:53:05
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-09 10:43:34
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/receipt/add.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <funi-detail :bizName="bizName" :auditButtons="[]" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
    <SubmitSuccess ref="submitSuccessRef" />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, unref } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from './../component/receipt/baseInfo.vue';
import { submitBusiness } from '@/apps/erm/config/business.js';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
const route = useRoute();
const bizName = ref(route.query.bizName);
const type = ref(route.query.type);
const id = ref(route.query.id);
const businessId = ref();
const infoData = ref();
const _route = route.query;
const submitSuccessRef = ref();
const steps = computed(() => {
  let arr = [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit: !['audit', 'info','change_add','change_edit'].includes(type.value),
        type: type.value,
        lastId: ['change_add'].includes(type.value) ? unref(_route).id : '',
        reason: unref(_route).reason
      },
      on: {
        updateID: ({ id: v, businessId: bv }) => {
          id.value = v;
          businessId.value = bv;
        },
        infoUpdate: data => {
          infoData.value = data;
          let { id: v, businessId: bv } = data;
          id.value = v;
          businessId.value = bv;
        }
      }
    },
    {
      title: '要件信息',
      preservable: false,
      type: 'FuniFileTable',
      props: {
        params: {
          businessId: businessId.value
        },
        callbackFun: submitBusinessFunc
      }
    }
  ];
  return arr;
});

// const businessName = computed(() => {
//   let { companyName, deptName, contractName } = unref(infoData);
//   let str = `${companyName}-${deptName}-${contractName}`;
//   return str;
// });

const submitBusinessFunc = async () => {
  await submitBusiness('ERM_RECEIPT_ADD', businessId.value, 'SUBMIT').finally(() => {});
  submitSuccessRef.value.show();
};

const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode
  };
  return obj;
});
</script>
