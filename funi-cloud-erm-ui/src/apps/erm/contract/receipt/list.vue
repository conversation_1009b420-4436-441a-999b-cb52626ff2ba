<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:52:57
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-09 15:21:38
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/receipt/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
    <AuditDialog v-model="showDialog" :callback="dialogCallBack" />
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref } from 'vue';
import { useReceiptColumns, useBtnsConfig } from './../hooks/receipt/receipt.jsx';
import { queryReceiptListHttp, creatBusHttp } from './../hooks/receipt/api.js';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
import { deleteBusiness, submitBusiness } from '@/apps/erm/config/business.js';
import { apiUrl } from '@/apps/erm/contract/hooks/receipt/api.js';
import AuditDialog from '@/apps/erm/component/auditDialog/index.vue';
const showDialog = ref(false);
const rowData = ref();
const router = useRouter();
const listPage = ref();
const queryData = ref();
const dialogCallBack = ref(void 0);
const lodaData = async (params, queryParams) => {
  rowData.value = void 0;
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryReceiptListHttp({ ...params, ...queryParams });
  queryData.value = queryParams;
  return data;
};

onMounted(() => {});
const useCardTab = computed(() => {
  console.log('rowData.value', rowData.value);
  return [
    {
      curdOption: {
        btns: useBtnsConfig({
          addFunc,
          exportFn,
          changeFunc,
          hasValid: rowData.value?.row?.dicReceiptStatusCode === '1'
        }),
        // searchConfig: useSearchConfig(),
        lodaData: lodaData,
        fixedButtons: true,
        reloadOnActive: true,
        checkOnRowClick: true,
        columns: useReceiptColumns({ seeDateils, editFunc, auditFunc, delFunc, infoFuinc }),
        on: {
          rowClick: e => {
            row_click(e);
          }
        }
      }
    }
  ];
});

const seeDateils = row => {
  router.push({
    name: 'ermContractReceiptInfo',
    query: {
      title: row.contractName || '收款单',
      bizName: '详情',
      type: 'info',
      id: row.dicDoingBusCode ? row.lastId || row.id : row.id,
      tab: ['收款单', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
const infoFuinc = row => {
  router.push({
    name: 'ermContractReceiptInfo',
    query: {
      title: row.contractName || '收款单',
      bizName: '审核',
      type: 'info',
      id: row.id,
      tab: ['收款单', row.contractName || '', '业务详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const addFunc = () => {
  router.push({
    name: 'ermContractReceiptAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: '收款单-新建'
    }
  });
};

const editFunc = row => {
  router.push({
    name: 'ermContractReceiptAdd',
    query: {
      title: row.contractName || '收款单',
      bizName: 'edit',
      type: row.dicBusinessTypeCode == 'ERM_RECEIPT_CHANGE' ? 'change_edit' : 'edit',
      tab: ['收款单', row.contractName || '', '编辑'].join('-'),
      id: row.id
    }
  });
};
const auditFunc = row => {
  router.push({
    name: 'ermContractReceiptInfo',
    query: {
      title: row.contractName || '收款单',
      bizName: '审核',
      type: 'audit',
      id: row.id,
      tab: ['收款单', row.contractName, '审核'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const changeFunc = async () => {
  let { row } = rowData.value;
  await creatBusHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_RECEIPT_CHANGE'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractReceiptAdd',
      query: {
        bizName: '新建',
        type: 'change_add',
        tab: ['收款单', row.contractName, '变更'].join('-'),
        id: row.id,
        title: row.contractName || '收款单',
        reason
      }
    });
  };
  showDialog.value = true;
};

const delFunc = async row => {
  // await deleteBusiness(row.businessId);
  await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  listPage.value.reload({ resetPage: false });
};

const exportFn = () => {
  expotrFunction({ url: apiUrl.queryReceiptListExport, params: queryData.value, FileName: '收款单' });
};

const row_click = e => {
  rowData.value = e;
};
</script>
