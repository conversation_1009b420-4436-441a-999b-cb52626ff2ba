<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:52:57
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-09 15:20:29
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/income/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
    <AuditDialog v-model="showDialog" :callback="dialogCallBack" />
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref } from 'vue';
import { useIncomeColumns, useBtnsConfig } from './../hooks/income/income.jsx';
import { queryIncomeConfirmInfoListHttp, creatBusHttp } from './../hooks/income/api.js';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
import { apiUrl } from '@/apps/erm/contract/hooks/income/api.js';
import { deleteBusiness, submitBusiness } from '@/apps/erm/config/business.js';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
import AuditDialog from '@/apps/erm/component/auditDialog/index.vue';
const router = useRouter();
const listPage = ref();
const queryData = ref();
const rowData = ref();
const showDialog = ref(false);
const dialogCallBack = ref(void 0);
const lodaData = async (params, queryParams) => {
  rowData.value = void 0;
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryIncomeConfirmInfoListHttp({ ...params, ...queryParams });
  queryData.value = queryParams;
  return data;
};
onMounted(() => {});
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig({
          addFunc,
          exportFn,
          changeFunc,
          hasValid: rowData.value?.row?.dicIncomeStatusCode === '1'
        }),
        // searchConfig: useSearchConfig(),
        lodaData: lodaData,
        fixedButtons: true,
        reloadOnActive: true,
        checkOnRowClick: true,
        columns: useIncomeColumns({ seeDateils, editFunc, auditFunc, delFunc, infoFuinc }),
        on: {
          rowClick: e => {
            row_click(e);
          }
        }
      }
    }
  ];
});

const seeDateils = row => {
  router.push({
    name: 'ermContractIncomeInfo',
    query: {
      title: row.contractName,
      bizName: '详情',
      type: 'info',
      id: row.dicDoingBusCode ? row.lastId || row.id : row.id,
      tab: ['收入确认表', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const infoFuinc = row => {
  router.push({
    name: 'ermContractIncomeInfo',
    query: {
      title: row.contractName,
      bizName: '审核',
      type: 'info',
      id: row.id,
      tab: ['收入确认表', row.contractName || '', '业务详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const exportFn = () => {
  expotrFunction({ url: apiUrl.queryIncomeConfirmInfoListExport, params: queryData.value, FileName: '收入确认表' });
};

const changeFunc = async () => {
  let { row } = rowData.value;
  await creatBusHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_INCOME_CONFIRM_CHANGE'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractIncomeAdd',
      query: {
        bizName: '新建',
        type: 'change_add',
        tab: ['收入确认表', row.contractName, '变更'].join('-'),
        id: row.id,
        title: row.contractName || '收入确认表',
        reason
      }
    });
  };
  showDialog.value = true;
};

const auditFunc = row => {
  router.push({
    name: 'ermContractIncomeInfo',
    query: {
      title: row.contractName || '收入确认表',
      bizName: '审核',
      type: 'audit',
      id: row.id,
      tab: ['收入确认表', row.contractName || '', '审核'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const addFunc = () => {
  router.push({
    name: 'ermContractIncomeAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: '收入确认表-新建'
    }
  });
};

const delFunc = async row => {
  // await deleteBusiness(row.businessId);
  await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  listPage.value.reload({ resetPage: false });
};
const editFunc = row => {
  router.push({
    name: 'ermContractIncomeAdd',
    query: {
      title: row.contractName || '收入确认表',
      bizName: 'edit',
      type: row.dicBusinessTypeCode == 'ERM_INCOME_CONFIRM_CHANGE' ? 'change_edit' : 'edit',
      tab: ['收入确认表', row.contractName || '', '编辑'].join('-'),
      id: row.id
    }
  });
};
const row_click = e => {
  rowData.value = e;
};
</script>
