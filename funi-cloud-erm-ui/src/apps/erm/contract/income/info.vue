<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:53:05
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-07 09:59:59
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/income/info.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <funi-detail
      :bizName="bizName"
      :auditButtons="auditButtons"
      :steps="steps"
      :showWorkflow="true"
      :detailHeadOption="detailHeadOption || {}"
      @auditEvent="auditEvent"
      :businessId="['info', 'audit'].includes(type) ? businessId : void 0"
      :beforeAuditFn="beforeAuditFn"
    />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, inject, onMounted, watch, nextTick, unref, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import BaseInfo from './../component/income/baseInfo.vue';
import { auditEndFunc } from '@/apps/erm/config/business.js';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { isRequiredIncomeInfoHttp } from './../hooks/income/api';
const multiTab = useMultiTab();

const route = useRoute();
const bizName = route.query.bizName;
const type = ref(route.query.type);
const id = ref(route.query.id);
const businessId = ref('');
const infoData = ref();
const auditButtons = ref([]);
const router = useRouter();
const isAuditEdit = ref(false);
const baseDom = ref();
const steps = computed(() => {
  let arr = [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit: false,
        businessId: businessId.value,
        type: type.value,
        isAuditEdit: isAuditEdit.value,
        ref: refFun
      },
      on: {
        updateID: ({ id: v, businessId: bv }) => {
          id.value = v;
          businessId.value = bv;
        },
        infoUpdate: data => {
          infoData.value = data;
          let { id: v, businessId: bv } = data;
          id.value = v;
          businessId.value = bv;
        }
      }
    },
    ...(businessId.value && (route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode)
      ? [
          {
            title: '办件记录',
            type: 'FuniWorkRecord',
            props: {
              objectListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              busListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              params: {
                businessId: businessId.value,
                dicBusinessTypeCode: route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode
              }
            },
            on: {
              onClick
            }
          }
        ]
      : [])
  ];
  return arr;
});

watch(businessId, async () => {
  if (businessId.value && type.value == 'audit') {
    isAuditEdit.value = await isRequiredIncomeInfoHttp({
      businessId: businessId.value
    });
    // isAuditEdit.value = true;
  }
});

const refFun = e => {
  baseDom.value = e;
};

const copy = () => {
  router.push({
    name: 'ermContractIncomeAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: ['收入确认表', unref(infoData).contractName || '', '新建'].join('-'),
      id: id.value,
      title: unref(infoData).contractName || '收入确认表'
    }
  });
};
const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点',
    btns:
      type.value === 'info'
        ? [
            {
              name: '复制',
              props: { type: 'text' },
              on: { click: copy },
              menuAuth: 'ERM_CONTRACTINCOME_COPY'
            }
          ]
        : []
  };
  return obj;
});
const onClick = row => {
  // console.log(row, 'row')
  router.push({
    name: 'ermContractIncomeInfo',
    query: {
      title: row.contractName,
      bizName: '详情',
      type: 'info',
      id: row.id,
      tab: ['收入确认表', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
const auditEvent = () => {
  auditEndFunc(() => {
    multiTab.closeCurrentPage();
  });
};

const beforeAuditFn = async ({ businessExecutionType }) => {
  if (!isAuditEdit.value) return Promise.resolve({});
  if (['AGREE', 'SUBMIT'].includes(businessExecutionType)) {
    try {
      return await unref(baseDom).authFun();
    } catch {
      return Promise.reject();
    }
  }
  return Promise.resolve({});
};
</script>
