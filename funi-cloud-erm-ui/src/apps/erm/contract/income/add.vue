<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:53:05
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-09 18:56:40
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/income/add.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <funi-detail :bizName="bizName" :auditButtons="[]" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
    <SubmitSuccess ref="submitSuccessRef" />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, unref } from 'vue';
import { useRoute } from 'vue-router';
import BaseInfo from './../component/income/baseInfo.vue';
import { submitBusiness } from '@/apps/erm/config/business.js';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';
const route = useRoute();
const bizName = ref(route.query.bizName);
const type = ref(route.query.type);
const id = ref(route.query.id);
const _route = route.query;
const businessId = ref();
const infoData = ref();
let cId = '';
let cName = '';

if (type.value === 'add') {
  cId = route.query.cId;
  cName = route.query.cName;
}

const submitSuccessRef = ref();
const steps = computed(() => {
  let arr = [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit: !['audit', 'info', 'change_add', 'change_edit'].includes(type.value),
        type: type.value,
        cId,
        cName,
        lastId: ['change_add'].includes(type.value) ? unref(_route).id : '',
        reason: unref(_route).reason
      },
      on: {
        updateID: ({ id: v, businessId: bv }) => {
          id.value = v;
          businessId.value = bv;
        },
        infoUpdate: data => {
          infoData.value = data;
          let { id: v, businessId: bv } = data;
          id.value = v;
          businessId.value = bv;
        },
        otherUpdate: object => {
          Object.keys(object).forEach(key => {
            infoData.value[key] = object[key] || infoData.value[key];
          });
        }
      }
    },
    {
      title: '要件信息',
      preservable: false,
      type: 'FuniFileTable',
      props: {
        params: {
          businessId: businessId.value
        },
        callbackFun: submitBusinessFunc
      }
    }
  ];
  return arr;
});

const businessName = computed(() => {
  let { companyName, deptName, contractName } = unref(infoData);
  let str = `${companyName}-${deptName}-${contractName}`;
  return str;
});

const submitBusinessFunc = async () => {
  // loadingStatus.value.status = true;
  await submitBusiness('ERM_INCOME_CONFIRM_ADD', businessId.value, 'SUBMIT', unref(businessName)).finally(() => {
    //loadingStatus.value.status = false
  });
  submitSuccessRef.value.show();
};

const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点'
  };
  return obj;
});
</script>
