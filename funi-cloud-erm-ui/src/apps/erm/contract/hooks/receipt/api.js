/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-14 14:31:58
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-09 09:37:31
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/receipt/api.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const apiUrl = {
  queryReceiptList: '/erm/receiptList/queryReceiptList',
  receiptNew: '/erm/receipt/new',
  receiptInfo: '/erm/receipt/info',
  queryReceiptListExport: '/erm/receiptList/queryReceiptListExport',
  creatBus: '/erm/receipt/creatBus'
};

export const queryReceiptListHttp = params => {
  return $http.post(apiUrl.queryReceiptList, params);
};
export const receiptNewHttp = params => {
  return $http.post(apiUrl.receiptNew, params);
};
export const receiptInfoHttp = params => {
  return $http.fetch(apiUrl.receiptInfo, params);
};
export const creatBusHttp = params => {
  return $http.post(apiUrl.creatBus, params);
};
