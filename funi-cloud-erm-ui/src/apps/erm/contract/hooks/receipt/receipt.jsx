/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:54:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-07 19:11:54
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/receipt/receipt.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';
import ermHooks from '@/apps/erm/hooks/index.js';
export const useReceiptColumns = ({ seeDateils, editFunc = () => { }, delFunc = () => { }, auditFunc = () => { }, infoFuinc }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '付款公司',
      prop: 'collectionCompanyName',
      width: 200,
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={seeDateils}
            auth={'ERM_CONTRACTRECRIPT_INFO'}
            text={row.collectionCompanyName}
          />
        );
      },
      fixed: 'left'
    },
    {
      label: '收款公司',
      prop: 'receiptCompanyName'
    },
    {
      label: '到账时间',
      prop: 'receiptTime'
    },
    {
      label: '本次收款金额-含税(元)',
      prop: 'preReceiptAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.preReceiptAmount);
      }
    },
    {
      label: '合同名称',
      prop: 'contractName'
    },
    {
      label: '合同编号',
      prop: 'contractSn'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },

    {
      label: '负责人',
      prop: 'employeeName'
    },

    {
      label: '项目名称',
      prop: 'projectName'
    },
    {
      label: '客户名称',
      prop: 'customerName'
    },
    {
      label: '流程节点',
      prop: 'businessNode'
    },
    {
      label: '在办业务',
      prop: 'dicDoingBusName'
    },
    {
      label: '收款单状态',
      prop: 'dicReceiptStatusName'
    },


    {
      label: '生效时间',
      prop: 'takeEffectTime'
    },

    {
      label: '服务内容',
      prop: 'dicServiceContentName'
    },
    {
      label: '确收方式',
      prop: 'dicConfirmModeName'
    },
    {
      label: '税率',
      prop: 'taxRate',
      align: 'right',
    },
    {
      label: '月度',
      prop: 'issue'
    },
    {
      label: '收款单编号',
      prop: 'receiptSn'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        let operationBtn = {
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={editFunc}
              auth={'ERM_CONTRACTRECRIPT_EDIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          DEL: (
            <el-popconfirm
              title="确定删除当前收款单？"
              width="220"
              onConfirm={() => {
                delFunc(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_CONTRACTRECRIPT_DEL'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={auditFunc}
              auth={'ERM_CONTRACTRECRIPT_AUDIT'}
              text={'审核'}
            ></Hyperlink>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={infoFuinc}
              auth={'ERM_CONTRACTRECRIPT_INFO'}
              text={'业务详情'}
            ></Hyperlink>
          )
        };
        let btnList = []
        btnList = $utils.clone(row.buttonList, true)
        if (row.dicReceiptStatusCode != '1' && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO')
        }
        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

export const useBtnsConfig = ({ addFunc = () => { }, editFun = () => { }, changeFunc = () => { }, exportFn = () => { }, hasValid = false }) => {

  return [
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACTRECRIPT_ADD" onClick={addFunc} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button disabled={!hasValid} v-auth="ERM_CONTRACTRECRIPT_CHANGE" onClick={changeFunc} type="primary">
          变更
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACTRECRIPT_EXPORT" onClick={exportFn} type="primary">
          导出
        </el-button>
      )
    }
  ];
};
