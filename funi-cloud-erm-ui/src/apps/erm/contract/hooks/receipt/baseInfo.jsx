/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-03 11:14:08
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-07 10:06:09
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/receipt/baseInfo.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Verifics from '@/apps/erm/component/verifica/index.vue';
import { validateMoney, ermGlobalApi, dicCode } from '@/apps/erm/config/config.jsx';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import ErmTreeSelect from '@/apps/erm/component/ermTree/index.vue';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import LabelIndex from '@/apps/erm/component/ermSelect/labelIndex.vue';
import PersonChoose from '@/apps/erm/component/personChoose/index.vue';
import Vrouter from '@/router';
import ermHooks from '@/apps/erm/hooks/index.js';
import MoneyInput from '@/apps/erm/component/inputMoneyNumber/index.vue';

const datePickerProps = {
  style: { width: '100%' },
  format: 'YYYY-MM-DD',
  'value-format': 'YYYY-MM-DD'
};
const router = Vrouter;
export const useFormSchema = params => {
  return [
    {
      label: '收款单编号',
      component: null,
      prop: 'receiptSn'
    },
    {
      label: '状态',
      component: null,
      prop: 'dicReceiptStatusName'
    },
    {
      label: '创建时间',
      component: null,
      prop: 'createTime',
      hidden: params.isEdit
    },
    {
      label: '生效时间',
      component: null,
      prop: 'takeEffectTime',
      hidden: params.isEdit
    },
    {
      label: '收款合同名称',
      component: ({ formModel }) =>
        params.isEdit ? (
          <InfiniteSelect
            api={ermGlobalApi.queryValidCollectionContractList}
            defaultProps={{
              keyWord: 'contractNameOrSn',
              name: 'contractName',
              id: 'id',
              sn: 'contractSn',
              nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
            }}
            maxWidth={500}
            onChange={(e, { contractSn }) => {
              params.getcontractInfo({ id: e, contractSn });
            }}
          ></InfiniteSelect>
        ) : (
          params.isSpecialProject ? <span>{formModel.contractObj?.name}</span> : <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'ermContractCollectionInfo',
                query: {
                  title: formModel.contractObj?.title,
                  bizName: '详情',
                  type: 'info',
                  id: formModel.contractObj?.id,
                  tab: ['收款合同', formModel.contractObj?.title, '详情'].join('-')
                }
              });
            }}
          >
            {formModel.contractObj?.name}
          </el-button>

        ),
      prop: 'contractObj'
    },
    // {
    //   label: '收款合同编号',
    //   component: ({ formModel }) => (
    //     <el-button
    //       link
    //       type="primary"
    //       onClick={e => {
    //         router.push({
    //           name: 'ermContractCollectionInfo',
    //           query: {
    //             title: formModel.contractObj?.name || '收款合同',
    //             bizName: '详情',
    //             type: 'info',
    //             id: formModel.contractObj.id,
    //             tab: ['收款合同', formModel.contractObj?.name, '详情'].join('-')
    //           }
    //         });
    //       }}
    //     >
    //       {formModel.contractSn}
    //     </el-button>
    //   ),
    //   prop: 'contractSn'
    // },
    {
      label: '归属公司',
      component: null,
      prop: 'companyName'
    },
    {
      label: '归属部门',
      component: null,
      prop: 'deptName'
    },
    {
      label: '项目名称',
      component: ({ formModel }) => (
        params.isSpecialProject ? <span>{formModel.projectObj?.name}</span> : <el-button
          link
          type="primary"
          onClick={e => {
            router.push({
              name: 'erm_projectManage_bulletin_two',
              query: {
                title: formModel.projectObj?.name || '项目管理',
                bizName: '详情',
                type: 'info',
                id: formModel.projectObj.id,
                tab: ['项目管理', formModel.projectObj?.name, '项目管理'].join('-')
              }
            });
          }}
        >
          {formModel.projectObj?.name}
        </el-button>
      ),
      prop: 'projectObj'
    },
    {
      label: '合同签订时间',
      component: null,
      prop: 'signDate'
    },
    {
      label: '负责人',
      component: ({ value }) =>
        params.isEdit ? (
          <InfiniteSelect
            api={ermGlobalApi.queryEmployeeList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'employeeName',
              id: 'id'
            }}
          ></InfiniteSelect>
        ) : (
          <span>{value?.name}</span>
        ),
      prop: 'employeeObj',
      props: {
        placeholder: '请选择负责人'
      }
    },
    {
      label: '客户名称',
      component: ({ formModel }) =>
        params.isEdit ? (
          <InfiniteSelect
            api={ermGlobalApi.queryCustomerList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'customerName',
              id: 'id',
              nameEval: "`${item['customerName']}(${item['suCreditCode']})`"
            }}
            maxWidth={500}
            otherParams={{
              dicCustomerTypeCode: '1'
            }}
          >
            {{
              default: () => (
                <el-button
                  link
                  type="primary"
                  onClick={
                    () => {
                      if (formModel.partA?.id)
                        router.push({
                          name: 'erm_supplier_add',
                          query: {
                            title: formModel.partA?.name || '客商管理',
                            bizName: '编辑',
                            type: 'edit',
                            id: formModel.partA?.id,
                            tab: ['客商管理', formModel.partA?.name, '编辑'].join('-')
                          }
                        })
                    }
                  }
                >
                  维护客商
                </el-button>
              )
            }}
          </InfiniteSelect >
        ) : (
          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'erm_supplier_two',
                query: {
                  title: formModel.partA?.name || '客商管理',
                  bizName: '详情',
                  type: 'info',
                  id: formModel.partA.id,
                  tab: ['客商管理', formModel.partA?.name, '详情'].join('-')
                }
              });
            }}
          >
            {formModel.partA?.name}
          </el-button>
        ),
      prop: 'partA'
    },
    {
      label: '合同金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'contractAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      }
    },
    {
      label: '合同已收款金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'receiptAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      }

    }
  ];
};

export const serveColumns = ({ isEdit, iptChange = () => { }, contactRef = {}, delFunc = () => { }, type = 'add', isSpecialProject = false }) => {
  const reset_v = (key, el) => {
    contactRef[key] = el ? el : contactRef[key];
  };
  // 金额验证
  let validateMoney_table = (value, callback) => {
    if (value === '' || value === null || value === void 0) {
      callback('必填');
    } else if (value == 0 || !/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(value)) {
      callback('请输入大于0且最多保留两位小数的数字');
    } else {
      callback();
    }
  };

  return [
    {
      label: '本次收款金额(元)',
      prop: 'currentCollectionAmount',
      slots: {
        header: 'a'
      },
      align: isEdit ? 'left' : 'right',
      width: isEdit ? 240 : 150,
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            onChange={e => {
              iptChange(index, 'currentCollectionAmount', e);
            }}
            maxlength={50}
            placeholder="本次收款金额(元)"
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_currentCollectionAmount`, el);
            }}
            value={row.currentCollectionAmount}
            c={c}
            key={`${row.uuId}_currentCollectionAmount`}
            rule={type == 'audit' ? [
              { required: true, message: '必填' },
              {
                validator: validateMoney_table
              }
            ] : void 0}
          ></Verifics>
        ) : (
          <span>{ermHooks.erm_intl(row.currentCollectionAmount)}</span>
        );
      }
    },
    {
      label: '来源',
      prop: 'dicServiceSourceName'
    },
    {
      label: '服务内容',
      prop: 'dicServiceContentName'
    },
    {
      label: '是否开放性条款',
      prop: 'isOpenClause',
      render: ({ row, index }) => <span>{row.isOpenClause === true ? '是' : '否'}</span>
    },
    {
      label: '确收方式',
      prop: 'dicConfirmModeName'
    },
    ...isSpecialProject ? [] : [{
      label: '含税金额(元)',
      prop: 'includeTaxAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.includeTaxAmount)
    }],
    {
      label: '税率',
      prop: 'taxRate',
      align: 'right'
    },
    {
      label: '实际开始时间',
      prop: 'actualStartTime'
    },
    {
      label: '实际结束时间',
      prop: 'actualEndTime'
    },
    {
      label: '税额(元)',
      prop: 'taxAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.taxAmount)
    },
    {
      label: '不含税金额(元)',
      prop: 'excludeTaxAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.excludeTaxAmount)
    },
    {
      label: '绩效比例(%)',
      prop: 'performanceProp',
      align: 'right'
    },
    {
      label: '累计已收(元)',
      prop: 'accumulatReceived',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.accumulatReceived)
    },
    ...isSpecialProject ? [] : [{
      label: '剩余未收(元)',
      prop: 'surplusReceived',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.surplusReceived)
    },],

    {
      label: '累计已开票(元)',
      prop: 'invoiceAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.invoiceAmount)
    },
    ...isSpecialProject ? [] : [
      {
        label: '剩余未开票(元)',
        prop: 'unInvoiceAmount',
        align: 'right',
        render: ({ row, index }) => ermHooks.erm_intl(row.unInvoiceAmount)
      },
    ],

    {
      label: '收入大类',
      prop: 'dicIncomeCategoryName'
    },
    {
      label: '收入小类',
      prop: 'dicIncomeSubclassName'
    },
    {
      label: '备注',
      prop: 'remark'
    },
    ...(isEdit
      ? [
        {
          label: '操作',
          prop: '',
          align: 'center',
          fixed: 'right',
          cellInteraction: false,
          render: ({ row, index }) => {
            return (
              <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                {
                  <el-button
                    type="primary"
                    onClick={() => {
                      delFunc(index);
                    }}
                    link
                  >
                    删除
                  </el-button>
                }
              </div>
            );
          }
        }
      ]
      : [])
  ];
};

export const useFormSchema2 = params => {
  return [
    {
      label: '本次收款金额(含税/元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'preReceiptAmount',
      props: {
        placeholder: '本次收款金额',
        moneyCapitalShow: true,
        isEdit: params.isEdit
      },
      on: {
        // change:
      }
    },
    {
      label: '税额(元)',
      prop: 'taxAmount',
      component: () => <MoneyInput></MoneyInput>,
      props: {
        moneyCapitalShow: true,
        isEdit: false
      },
    },
    {
      label: '收款金额(不含税/元)',
      prop: 'sufReceiptAmount',
      component: () => <MoneyInput></MoneyInput>,
      props: {
        moneyCapitalShow: true,
        isEdit: false
      },
    },
    {
      label: '到账时间',
      component: params.isEdit ? 'el-date-picker' : null,
      prop: 'receiptTime',
      props: {
        ...datePickerProps,
        placeholder: '请选择到账时间'
      }
    },

    {
      label: '收入确认表',
      component: ({ formModel }) => {
        return params.type == 'audit' || params.isEdit ? <el-select
          style="width:100%"
        >
          {params.incomeContractList.map(item => (
            <el-option key={item.incomeSn} label={`${item.incomeSn}（${item.includeTaxAmount}元）`} value={item.incomeSn} />
          ))}
        </el-select> :
          formModel?.incomeContractObj?.id ? <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'ermContractIncomeInfo',
                query: {
                  title: formModel?.incomeContractObj?.title || '收入确认表',
                  bizName: '详情',
                  type: 'info',
                  id: formModel?.incomeContractObj?.id,
                  tab: ['收入确认表', formModel?.incomeContractObj?.title, '详情'].join('-')
                }
              });
            }}
          >
            {formModel?.incomeContractObj?.name}
          </el-button> : <span>--</span>
      },
      prop: params.type == 'audit' || params.isEdit ? 'incomeContractSn' : 'incomeContractObj',
      props: {
        placeholder: '请收入确认表'
      },
      hidden: !params?.isOpenClause
    },
    {
      label: '付款公司',
      component: params.isEdit ? 'el-input' : null,
      prop: 'collectionCompanyName',
      props: {
        placeholder: '请输入付款公司'
      }
    },
    {
      label: '收款公司',
      component: params.isEdit
        ? () => (
          <ErmTreeSelect
            propsMap={{
              value: 'id',
              label: 'companyName',
              children: 'childrens'
            }}
            isDetp={false}
            isWatchAll={true}
            onChange={(e, n) => {
              params?.setReceiptCompany({
                id: e,
                name: n
              });
            }}
          ></ErmTreeSelect>
        )
        : null,
      prop: params.isEdit ? 'receiptCompanyId' : 'receiptCompanyName',
      props: {
        style: { width: '100%' },
        placeholder: '请输选择收款公司'
      }
    },
    {
      label: '审核人',
      component: params.isEdit ? () => <PersonChoose></PersonChoose> : null,
      prop: params.isEdit ? 'receiptSignerId' : 'receiptSignerName',
      props: {
        readonly: !params.isEdit
      }
    },
    {
      label: '备注',
      component: ['info', 'audit'].includes(params.type) ? ({ formModel }) => {
        return (
          <span
            style={{
              'white-space': 'pre-wrap'
            }}>
            {formModel.remark || '--'}
          </span>
        )

      } : 'el-input',
      prop: 'remark',
      colProps: {
        span: 24
      },
      props: {
        autosize: { minRows: 2, maxRows: 4 },
        type: 'textarea',
        maxlength: 200,
        'show-word-limit': true
      }
    }
  ];
};

export const useRule = () => {
  return {
    contractObj: [{ required: true, message: '必填', trigger: 'change' }],
    employeeObj: [{ required: true, message: '必填', trigger: 'change' }],
    partA: [{ required: true, message: '必填', trigger: 'change' }]
  };
};
export const useRule2 = type => {
  return {
    preReceiptAmount: [{ required: true, validator: validateMoney, trigger: 'change' }],
    collectionCompanyName: [{ required: true, message: '必填', trigger: 'change' }],
    receiptTime: [{ required: true, message: '必填', trigger: 'change' }],
    collectionCompanyId: [{ required: true, message: '必填', trigger: 'change' }],
    receiptCompanyId: [{ required: true, message: '必填', trigger: 'change' }],
    receiptSignerId: [{ required: true, message: '必填', trigger: 'change' }],
    incomeContractSn: type == 'audit' ? [{ required: true, message: '必填', trigger: 'change' }] : []
  };
};

export const setForm2Money = ({ cData, form }) => {
  let preReceiptAmount = 0;
  let sufReceiptAmount = 0;
  let taxAmount = 0;
  cData.value.forEach(item => {
    let mount = item.currentCollectionAmount * 1 || 0;
    let taxRate = item.taxRate * 1 || 0;
    preReceiptAmount = $utils.round(preReceiptAmount * 1 + mount, 2);
    sufReceiptAmount = $utils.round(sufReceiptAmount * 1 + mount / (1 + taxRate), 2);
  });
  taxAmount = $utils.round(preReceiptAmount - sufReceiptAmount, 2);
  form.value.setValues({
    // preReceiptAmount,
    sufReceiptAmount,
    taxAmount
  });
};


