/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-14 14:31:58
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-08 11:21:38
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/income/api.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const apiUrl = {
  queryIncomeConfirmInfoList: '/erm/incomeConfirmInfoList/queryIncomeConfirmInfoList',
  incomeConfirmInfoNew: '/erm/incomeConfirmInfo/new',
  incomeConfirmInfo: '/erm/incomeConfirmInfo/info',
  queryIncomeConfirmInfoListExport: '/erm/incomeConfirmInfoList/queryIncomeConfirmInfoListExport',
  queryIncomeDetailList: '/erm/incomeDetailList/queryIncomeDetailList', //确收明细表
  validService: '/erm/incomeConfirmInfoList/validService',//根据服务id查询服务是否有草稿或有效的确收表
  creatBus: '/erm/incomeConfirmInfo/creatBus',
  isRequiredIncomeInfo: 'erm/incomeConfirmInfo/isRequiredIncomeInfo'
};

export const queryIncomeConfirmInfoListHttp = params => {
  return $http.post(apiUrl.queryIncomeConfirmInfoList, params);
};
export const incomeConfirmInfoNewHttp = params => {
  return $http.post(apiUrl.incomeConfirmInfoNew, params);
};
export const incomeConfirmInfoHttp = params => {
  return $http.fetch(apiUrl.incomeConfirmInfo, params);
};
export const queryIncomeDetailListHttp = params => {
  return $http.post(apiUrl.queryIncomeDetailList, params);
};
export const validServiceHttp = params => {
  return $http.fetch(apiUrl.validService, params);
};

export const creatBusHttp = params => {
  return $http.post(apiUrl.creatBus, params);
};

export const isRequiredIncomeInfoHttp = params => {
  return $http.fetch(apiUrl.isRequiredIncomeInfo, params);
};