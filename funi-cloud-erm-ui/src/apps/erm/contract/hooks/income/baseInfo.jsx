import Verifics from '@/apps/erm/component/verifica/index.vue';
import { validateMoney, ermGlobalApi, dicCode } from '@/apps/erm/config/config.jsx';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import ErmTreeSelect from '@/apps/erm/component/ermTree/index.vue';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import LabelIndex from '@/apps/erm/component/ermSelect/labelIndex.vue';
import Vrouter from '@/router';
import ermHooks from '@/apps/erm/hooks/index.js';
import MoneyInput from '@/apps/erm/component/inputMoneyNumber/index.vue';

const datePickerProps = {
  style: { width: '100%' },
  format: 'YYYY-MM-DD',
  'value-format': 'YYYY-MM-DD'
};
const router = Vrouter;
export const setAmount = ({ preTaxAmount, form, taxRate }) => {
  // 确收金额(不含税)
  let excludeTaxAmount = $utils.round(preTaxAmount / (1 + Number(taxRate)), 2);
  let taxAmount = $utils.round(preTaxAmount - excludeTaxAmount, 2);

  form.value.setValues({
    excludeTaxAmount,
    taxAmount
  });
};

export const useFormSchema = params => {
  return [
    {
      label: '收入确认表编号',
      component: null,
      prop: 'incomeSn',
      copy: false
    },
    {
      label: '状态',
      component: null,
      prop: 'dicIncomeStatusName',
      copy: false
    },
    {
      label: '创建时间',
      component: null,
      prop: 'createTime',
      hidden: params.isEdit,
      copy: false
    },
    {
      label: '生效时间',
      component: null,
      prop: 'takeEffectTime',
      hidden: params.isEdit,
      copy: false
    },
    {
      label: '收款合同名称',
      component: ({ formModel }) => (
        params.isEdit
          ? <InfiniteSelect
            api={ermGlobalApi.queryValidCollectionContractList}
            defaultProps={{
              keyWord: 'contractNameOrSn',
              name: 'contractName',
              id: 'id',
              sn: 'contractSn',
              nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
            }}
            maxWidth={500}
            onChange={(e, { contractSn }) => {
              params.getcontractInfo({ id: e, contractSn });
            }}
          ></InfiniteSelect> : (
            <el-button
              link
              type="primary"
              onClick={e => {
                router.push({
                  name: 'ermContractCollectionInfo',
                  query: {
                    title: formModel.contractObj?.title,
                    bizName: '详情',
                    type: 'info',
                    id: formModel.contractObj?.id,
                    tab: ['收款合同', formModel.contractObj?.title, '详情'].join('-')
                  }
                });
              }}
            >
              {formModel.contractObj?.name}
            </el-button>
          )



      )
      ,
      prop: 'contractObj'
    },
    // {
    //   label: '收款合同编号',
    //   component: ({ formModel }) => (
    //     <el-button
    //       link
    //       type="primary"
    //       onClick={e => {
    //         router.push({
    //           name: 'ermContractCollectionInfo',
    //           query: {
    //             title: formModel.contractObj?.name || '收款合同',
    //             bizName: '详情',
    //             type: 'info',
    //             id: formModel.contractObj.id,
    //             tab: ['收款合同', formModel.contractObj?.name, '详情'].join('-')
    //           }
    //         });
    //       }}
    //     >
    //       {formModel.contractSn}
    //     </el-button>
    //   ),
    //   prop: 'contractSn'
    // },
    {
      label: '归属公司',
      component: null,
      prop: 'companyName'
    },
    {
      label: '归属部门',
      component: null,
      prop: 'deptName'
    },
    {
      label: '项目名称',
      component: ({ formModel }) => (
        <el-button
          link
          type="primary"
          onClick={e => {
            router.push({
              name: 'erm_projectManage_bulletin_two',
              query: {
                title: formModel.projectObj?.name || '项目管理',
                bizName: '详情',
                type: 'info',
                id: formModel.projectObj.id,
                tab: ['项目管理', formModel.projectObj?.name, '详情'].join('-')
              }
            });
          }}
        >
          {formModel.projectObj?.name}
        </el-button>
      ),
      prop: 'projectObj'
    },
    {
      label: '合同签订时间',
      component: null,
      prop: 'signDate'
    },
    {
      label: '负责人',
      component: params.isEdit
        ? () => (
          <InfiniteSelect
            api={ermGlobalApi.queryEmployeeList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'employeeName',
              id: 'id'
            }}
          ></InfiniteSelect>
        )
        : null,
      prop: params.isEdit ? 'employeeObj' : 'employeeName',
      props: {
        placeholder: '请选择负责人'
      }
    },
    {
      label: '客户名称',
      component: ({ formModel }) => (
        <el-button
          link
          type="primary"
          onClick={e => {
            router.push({
              name: 'erm_supplier_two',
              query: {
                title: formModel.partA?.name || '客商管理',
                bizName: '详情',
                type: 'info',
                id: formModel.partA.id,
                tab: ['客商管理', formModel.partA?.name, '详情'].join('-')
              }
            });
          }}
        >
          {formModel.partA?.name}
        </el-button>
      ),
      prop: 'partA'
    },
    {
      label: '合同服务总金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'serviceTotalAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      }
    },
    {
      label: '合同已确收金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'totalIncomeConfirmAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      }
    },
    {
      label: '是否需要用印',
      component: params.isEdit || ['change_add', 'change_edit'].includes(params.type)
        ? () => (
          <el-select style="width:100%">
            <el-option key="1" label="是" value={true} />
            <el-option key="2" label="否" value={false} />
          </el-select>
        )
        : obj => {
          return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
        },
      prop: 'isSeal',

    },
    {
      label: '用印类型',
      component:
        params.isEdit || ['change_add', 'change_edit'].includes(params.type)
          ? () => (
            <el-checkbox-group>
              {params?.sealTypeList.map(item => {
                return <el-checkbox label={item.code}>{item.name}</el-checkbox>;
              })}
            </el-checkbox-group>
          )
          : null,
      prop: params.isEdit || ['change_add', 'change_edit'].includes(params.type) ? 'dicSealTypeCode' : 'dicSealTypeName',
      hidden: ({ formModel }) => {
        return !formModel.isSeal
      },
      colProps: {
        span: 24
      },

    },
  ];
};

export const useServeColumns = (isEdit, contactRef = {}, delFunc) => {
  const reset_v = (key, el) => {
    contactRef[key] = el;
  };
  return [
    {
      label: '来源',
      prop: 'dicServiceSourceName'
    },
    {
      label: '服务内容',
      prop: 'dicServiceContentName'
    },
    {
      label: '是否开放性条款',
      prop: 'isOpenClause',
      render: ({ row, index }) => {
        return row.isOpenClause === true ? '是' : row.isOpenClause === false ? '否' : '--';
      }
    },
    {
      label: '签约类型',
      prop: 'dicSignTypeCode',
      width: 180,
      slots: {
        header: 'a'
      },
      render: ({ row, index }) => {
        let c = (
          <ErmSelect
            code={dicCode.sign_type}
            onChange={e => {
              row.dicSignTypeCode = e
            }}
            placeholder="签约类型"
          />
        );

        return isEdit && row.isOpenClause == true ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_dicSignTypeCode`, el);
            }}
            value={row.dicSignTypeCode}
            c={c}
            key={`${row.uuId}_dicSignTypeCode`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.dicSignTypeName}</span>
        );
      }
    },
    {
      label: '确收方式',
      prop: 'dicConfirmModeName'
    },
    {
      label: '含税金额(元)',
      prop: 'includeTaxAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.includeTaxAmount);
      }
    },
    {
      label: '税率',
      prop: 'taxRate',
      align: 'right'
    },
    {
      label: '实际开始时间',
      prop: 'actualStartTime'
    },
    {
      label: '实际结束时间',
      prop: 'actualEndTime'
    },
    {
      label: '税额(元)',
      prop: 'taxAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.taxAmount);
      }
    },
    {
      label: '不含税金额(元)',
      prop: 'excludeTaxAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.excludeTaxAmount);
      }
    },
    {
      label: '绩效比例(%)',
      prop: 'performanceProp',
      align: 'right'
    },
    {
      label: '累计已收(元)',
      prop: 'accumulatReceived',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.accumulatReceived);
      }
    },
    {
      label: '剩余未收(元)',
      prop: 'surplusReceived',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.surplusReceived);
      }
    },
    {
      label: '累计已开票(元)',
      prop: 'invoiceAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.invoiceAmount);
      }
    },
    {
      label: '剩余未开票(元)',
      prop: 'unInvoiceAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.unInvoiceAmount);
      }
    },
    {
      label: '收入大类',
      prop: 'dicIncomeCategoryName',
      align: 'left'
    },
    {
      label: '收入小类',
      prop: 'dicIncomeSubclassName',
      align: 'left'
    },
    {
      label: '备注',
      prop: 'remark',
      align: 'left'
    },
    ...(isEdit
      ? [
        {
          label: '操作',
          prop: '',
          align: 'center',
          fixed: 'right',
          cellInteraction: false,
          render: ({ row, index }) => {
            return (
              <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                {
                  <el-button type="primary" onClick={delFunc} link>
                    删除
                  </el-button>
                }
              </div>
            );
          }
        }
      ]
      : [])
  ];
};

export const useDetailColumns = () => {
  return [
    {
      label: '序号',
      prop: '',
      render: ({ row, index }) => {
        return index + 1;
      }
    },
    {
      label: '来源',
      prop: 'source'
    },
    {
      label: '状态',
      prop: 'dicDetailStatusName'
    },
    {
      label: '月度',
      prop: 'issue'
    },
    {
      label: '开始时间',
      prop: 'startTime'
    },
    {
      label: '结束时间',
      prop: 'endTime'
    },
    {
      label: '月度系数',
      prop: 'monthlyCoefficient'
    },
    {
      label: '确收金额(含税/元)',
      prop: 'includeTaxAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.includeTaxAmount);
      }
    },
    {
      label: '确收金额(不含税/元)',
      prop: 'excludeTaxAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.excludeTaxAmount);
      }
    },

    {
      label: '税额(元)',
      prop: 'taxAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.taxAmount);
      }
    }
  ];
};
export const useFormSchema2 = params => {
  return [
    {
      label: '验收时间',
      component: params.isEdit || params.isAuditEdit ? 'el-date-picker' : null,
      prop: 'acceptanceTime',
      props: {
        ...datePickerProps,
        placeholder: '请选择验收时间'
      },
      hidden: params?.serve?.dicConfirmModeCode !== '2'
    },
    {
      label: '开始时间',
      component: params.isEdit ? 'el-date-picker' : null,
      prop: 'startTime',
      props: {
        ...datePickerProps,
        placeholder: '请选择开始时间'
      },
      hidden: params?.serve?.dicConfirmModeCode !== '3'
    },
    {
      label: '结束时间',
      component: params.isEdit ? 'el-date-picker' : null,
      prop: 'endTime',
      props: {
        ...datePickerProps,
        placeholder: '请选择结束时间'
      },
      hidden: params?.serve?.dicConfirmModeCode !== '3'
    },
    {
      label: '确收金额(含税/元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'includeTaxAmount',
      props: {
        placeholder: '请输入确收金额',
        isEdit: (params?.serve?.dicConfirmModeCode == '2' && ['change_add', 'change_edit'].includes(params?.type)) || params.isEdit || params.isAuditEdit,
        moneyCapitalShow: true,
        onChange: e => {
          if (!e) {
            params.form?.value?.setValues({
              taxAmount: void 0,
              excludeTaxAmount: void 0
            });
            return;
          }
          if (e && params.form.value && params?.serve?.taxRate) {
            setAmount({
              preTaxAmount: e,
              form: params.form,
              taxRate: params.serve.taxRate
            });
          }
        }
      }
    },
    {
      label: '税额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'taxAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      }
    },
    {
      label: '确收金额(不含税/元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'excludeTaxAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      }
    },
    {
      label: '收款方式',
      component: params.isEdit || params.isAuditEdit ? () => <ErmSelect code={dicCode.coll_type}></ErmSelect> : null,
      prop: params.isEdit || params.isAuditEdit ? 'dicCollTypeCode' : 'dicCollTypeName',
      props: {
        placeholder: '请选择收款方式'
      }
    },
    {
      label: '验收单说明',
      component: ['info', 'audit'].includes(params.type) ? ({ formModel }) => {
        return (
          <span
            style={{
              'white-space': 'pre-wrap'
            }}>
            {formModel.acceptanceDesc || '--'}
          </span>
        )

      } : 'el-input',
      prop: 'acceptanceDesc',
      colProps: {
        span: 24
      },
      props: {
        autosize: { minRows: 2, maxRows: 4 },
        type: 'textarea',
        maxlength: 200,
        'show-word-limit': true
      }
    }
  ];
};

export const useRule = (isEdit, type) => {
  let b = isEdit || ['change_add', 'change_edit'].includes(type) ? 'dicSealTypeCode' : 'dicSealTypeName';
  return {
    isSeal: [{ required: true, message: '必填', trigger: 'change' }],
    contractObj: [{ required: true, message: '必填', trigger: 'change' }],
    employeeObj: [{ required: true, message: '必填', trigger: 'change' }],
    [b]: [{ required: true, message: '必填', trigger: 'change' }],
  };
};
export const useRule2 = (isEdit, { serviceTotalAmount, totalIncomeConfirmAmount, isOpenClause, type, isAuditEdit }) => {
  let a = isEdit || isAuditEdit ? 'dicCollTypeCode' : 'dicCollTypeName';

  const validateMoneys = (rule, value, callback) => {
    if (value === '' || value === null || value === undefined) {
      callback(new Error('必填'));
    } else if (value == 0 || !/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(value)) {
      callback(new Error('请输入大于0且最多保留两位小数的数字'));
    } else {
      callback();
    }
  }
  return {

    includeTaxAmount: type === 'audit' && isAuditEdit ? [{ required: true, validator: validateMoneys, trigger: 'blur' }] : [],
    acceptanceTime: type === 'audit' && isAuditEdit ? [{ required: true, message: '必填', trigger: 'change' }] : [],
    startTime: [{ required: true, message: '必填', trigger: 'change' }],
    endTime: [{ required: true, message: '必填', trigger: 'change' }],
    preTaxAmount: [{ required: true, validator: validateMoney, trigger: 'change' }],
    [a]: type === 'audit' && isAuditEdit ? [{ required: true, message: '必填', trigger: 'change' }] : [],

  };
};
