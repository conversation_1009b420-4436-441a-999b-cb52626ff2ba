/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:54:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-22 14:33:25
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/other/other.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import ermHooks from '@/apps/erm/hooks/index.js';
import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';
export const useOtherColumns = ({ seeDateils, editFunc = () => { }, delFunc = () => { }, auditFunc = () => { }, infoFuinc }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '合同名称',
      prop: 'contractName',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo row={row} index={index} func={seeDateils} auth={'ERM_CONTRACTOTHER_INFO'} text={row.contractName} />
        );
      },
      fixed: 'left'
    },
    {
      label: '用印申请编号',
      prop: 'sealSn',
    },
    {
      label: '状态',
      prop: 'dicSealStatusName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },
    {
      label: '流程节点',
      prop: 'businessNode'
    },
    {
      label: '归档编号',
      prop: 'archiveSn'
    },
    {
      label: '甲方',
      prop: 'partyA'
    },
    {
      label: '乙方',
      prop: 'partyB'
    },
    {
      label: '合同签订时间',
      prop: 'signDate'
    },
    {
      label: '用印公司',
      prop: 'sealCompanyName'
    },


    {
      label: '合同份数',
      prop: 'contractCount',
      align: 'right'
    },
    {
      label: '是否多方合同',
      prop: 'isMultipartyContract',
      render: ({ row, index }) => {
        return row.isMultipartyContract === true ? '是' : row.isMultipartyContract === false ? '否' : '--';
      }
    },
    {
      label: '是否临时合同',
      prop: 'isTemporaryContract',
      render: ({ row, index }) => {
        return row.isTemporaryContract === true ? '是' : row.isTemporaryContract === false ? '否' : '--';
      }
    },
    {
      label: '负责人',
      prop: 'employeeName'
    },
    {
      label: '生效时间',
      prop: 'takeEffectTime'
    },

    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        let operationBtn = {
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={editFunc}
              auth={'ERM_CONTRACTOTHER_EDIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          DEL: (
            <el-popconfirm
              title="确定删除当前其他合同用印申请？"
              width="220"
              onConfirm={() => {
                delFunc(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_CONTRACTOTHER_DEL'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={auditFunc}
              auth={'ERM_CONTRACTOTHER_AUDIT'}
              text={'审核'}
            ></Hyperlink>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={infoFuinc}
              auth={'ERM_CONTRACTOTHER_INFO'}
              text={'业务详情'}
            ></Hyperlink>
          )
        };
        let btnList = []
        btnList = $utils.clone(row.buttonList, true)
        if (!$utils.isNil(row.dicOtherContractDoingBusCode) && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO')
        }
        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

export const useBtnsConfig = ({
  addFunc = () => { },
  cancelFunc = () => { },
  delFun = () => { },
  hasValid = false,
  exportFun = () => { }
}) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACTOTHER_ADD" onClick={addFunc} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button disabled={!hasValid} v-auth="ERM_CONTRACTOTHER_ABANDON" onClick={cancelFunc} type="primary">
          作废
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACTOTHER_EXPORT" onClick={exportFun} type="primary">
          导出
        </el-button>
      )
    }
  ];
};
