import Verifics from '@/apps/erm/component/verifica/index.vue';
import { validateMoney, ermGlobalApi, dicCode } from '@/apps/erm/config/config.jsx';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import ErmTreeSelect from '@/apps/erm/component/ermTree/index.vue';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import LabelIndex from '@/apps/erm/component/ermSelect/labelIndex.vue';
import Vrouter from '@/router';
const router = Vrouter;

const datePickerProps = {
  style: { width: '100%' },
  format: 'YYYY-MM-DD',
  'value-format': 'YYYY-MM-DD'
};

export const useFormSchema = params => {
  return [
    {
      label: '用印申请编号',
      component: null,
      prop: 'sealSn',
      copy: false
    },
    {
      label: '状态',
      component: null,
      prop: 'dicSealStatusName',
      copy: false
    },
    ...(['info', 'audit', 'cancel_add', 'cancel_edit'].includes(params.type)
      ? [
        {
          label: '归档编号',
          component: null,
          prop: 'archiveSn',
          copy: false
        }
      ]
      : []),
    {
      label: '创建时间',
      component: null,
      prop: 'createTime',
      hidden: params.isEdit,
      copy: false
    },
    {
      label: '生效时间',
      component: null,
      prop: 'takeEffectTime',
      hidden: params.isEdit,
      copy: false
    },
    {
      label: '合同名称',
      component: params.isEdit ? 'el-input' : null,
      prop: 'contractName',
      props: {
        placeholder: '请输入合同名称'
      }
    },
    {
      label: '合同签订日期',
      component:
        params.isEdit ||
          (['audit'].includes(params.type) && ['ERM_OTHER_CONTRACT_SEAL_ADD'].includes(params.dicBusinessTypeCode))
          ? 'el-date-picker'
          : null,
      prop: 'signDate',
      props: {
        placeholder: '请选择合同签订时间',
        ...datePickerProps
      }
    },
    {
      label: '归属公司',
      component: params.isEdit
        ? () => (
          <ErmTreeSelect
            propsMap={{
              value: 'id',
              label: 'companyName',
              children: 'childrens'
            }}
            isDetp={false}
            init={params.type == 'add' ? true : false}
            onChange={(id, name) => {
              params?.setCompany({
                id,
                name
              });
              params?.resetDeptId();
            }}
          ></ErmTreeSelect>
        )
        : null,
      prop: params.isEdit ? 'companyId' : 'companyName',
      props: {
        style: { width: '100%' },
        placeholder: '请输选择归属公司'
      }
    },
    {
      label: '归属部门',
      component: params.isEdit
        ? () => (
          <ErmTreeSelect
            propsMap={{
              value: 'id',
              label: 'deptName',
              children: 'childrenDept'
            }}
            isDetp={true}
            init={params.type == 'add' ? true : false}
            params={{
              id: params.getCompanyId()
            }}
            onChange={(id, name) => {
              params.setDept({
                id,
                name
              });
            }}
          ></ErmTreeSelect>
        )
        : null,
      prop: params.isEdit ? 'deptId' : 'deptName',
      props: {
        style: { width: '100%' },
        placeholder: '请选择归属部门'
      }
    },
    {
      label: '用印公司',
      component: () => <LabelIndex options={params.companyList}></LabelIndex>,
      prop: 'sealCompanyId'
    },
    {
      label: '用印类型',
      component: params.isEdit
        ? () => (
          <el-checkbox-group>
            {params?.sealTypeList.map(item => {
              return <el-checkbox label={item.code}>{item.name}</el-checkbox>;
            })}
          </el-checkbox-group>
        )
        : null,
      prop: params.isEdit ? 'dicSealTypeCode' : 'dicSealTypeName',
      colProps: {
        span: 24
      }
    },
    {
      label: '合同份数',
      component: params.isEdit ? 'el-input' : null,
      prop: 'contractCount',
      props: {
        placeholder: '请输入合同数'
      }
    },
    {
      label: '负责人',
      component: params.isEdit
        ? () => (
          <InfiniteSelect
            api={ermGlobalApi.queryEmployeeList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'employeeName',
              id: 'id'
            }}
          ></InfiniteSelect>
        )
        : null,
      prop: params.isEdit ? 'employeeObj' : 'employeeName',
      props: {
        placeholder: '请选择项目名称'
      }
    },

    {
      label: '是否多方合同',
      component: params.isEdit
        ? () => (
          <el-select
            onChange={e => {
              params?.setisMultipartyContract(e);
            }}
          >
            <el-option key="1" label="是" value={true} />
            <el-option key="2" label="否" value={false} />
          </el-select>
        )
        : obj => {
          return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
        },
      prop: 'isMultipartyContract',
      props: {
        placeholder: '请选择',
        style: { width: '100%' }
      }
    },
    {
      label: '是否临时合同',
      component: params.isEdit
        ? () => (
          <el-select
            onChange={e => {
              params?.setisTemporaryContract(e);
            }}
          >
            <el-option key="1" label="是" value={true} />
            <el-option key="2" label="否" value={false} />
          </el-select>
        )
        : obj => {
          return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
        },
      prop: 'isTemporaryContract',
      props: {
        placeholder: '请选择',
        style: { width: '100%' }
      }
    },
    {
      label: '临时合同份数',
      component: params.isEdit ? 'el-input' : null,
      prop: 'temContractCount',
      props: {
        placeholder: '请输入临时合同份数'
      },
      hidden: !params.isTemporaryContract
    },
    {
      label: '已用临时合同份数 ',
      component: null,
      prop: 'usedTemContractCount',
      hidden: !params.isTemporaryContract,
      copy: false
    },
    {
      label: '甲方',
      component: ({ formModel }) =>
        params.isEdit ? (
          <InfiniteSelect
            api={ermGlobalApi.queryCustomerList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'customerName',
              id: 'id',
              nameEval: "`${item['customerName']}(${item['suCreditCode']})`"
            }}
            maxWidth={500}
          >
            {{
              default: () => (
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    params.router.push({
                      name: 'erm_supplier_add',
                      query: {
                        bizName: '新建',
                        type: 'add',
                        tab: '客商管理-新建'
                      }
                    });
                  }}
                >
                  新建客商
                </el-button>
              )
            }}
          </InfiniteSelect>
        ) : (
          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'erm_supplier_two',
                query: {
                  title: formModel?.partA?.name || '客商管理',
                  bizName: '详情',
                  type: 'info',
                  id: formModel?.partA?.id,
                  tab: ['客商管理', formModel?.partA?.name, '详情'].join('-')
                }
              });
            }}
          >
            {formModel?.partA?.name}
          </el-button>
        ),
      prop: 'partA',
      props: {
        placeholder: '请选择甲方'
      }
    },
    {
      label: '乙方',
      component: ({ formModel }) =>
        params.isEdit ? (
          <InfiniteSelect
            api={ermGlobalApi.queryCustomerList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'customerName',
              id: 'id',
              nameEval: "`${item['customerName']}(${item['suCreditCode']})`"
            }}
            maxWidth={500}
          ></InfiniteSelect>
        ) : (
          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'erm_supplier_two',
                query: {
                  title: formModel?.partB?.name || '客商管理',
                  bizName: '详情',
                  type: 'info',
                  id: formModel?.partB?.id,
                  tab: ['客商管理', formModel?.partB?.name, '详情'].join('-')
                }
              });
            }}
          >
            {formModel?.partB?.name}
          </el-button>
        ),
      prop: 'partB',
      props: {
        placeholder: '请选择乙方'
      }
    }
  ];
};

export const useRule = (isEdit, isTemporaryContract) => {
  let a = isEdit ? 'companyId' : 'companyName';
  let b = isEdit ? 'deptId' : 'deptName';
  let c = isEdit ? 'dicSealTypeCode' : 'dicSealTypeName';
  let d = isEdit ? 'employeeObj' : 'employeeName';
  let e = 'partA';
  let f = 'partB';
  // console.log('isTemporaryContract', isTemporaryContract)
  return {
    contractName: [{ required: true, message: '必填', trigger: 'change' }],
    // [a]: [{ required: true, validator: validateMoney, trigger: 'change' }],
    [a]: [{ required: true, message: '必填', trigger: 'change' }],
    [b]: [{ required: true, message: '必填', trigger: 'blur' }],
    [c]: [{ required: true, message: '必填', trigger: 'change' }],
    contractCount: [{ required: true, message: '必填', trigger: 'change' }],
    [d]: [{ required: true, message: '必填', trigger: 'change' }],
    [e]: isTemporaryContract ? [] : [{ required: true, message: '必填', trigger: 'change' }],
    [f]: [{ required: true, message: '必填', trigger: 'change' }],
    isMultipartyContract: [{ required: true, message: '必填', trigger: 'change' }],
    isTemporaryContract: [{ required: true, message: '必填', trigger: 'change' }],
    temContractCount: [{ required: true, message: '必填', trigger: 'change' }]
  };
};
