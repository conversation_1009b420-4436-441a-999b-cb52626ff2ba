/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-14 14:31:58
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-27 17:12:19
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/other/api.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const apiUrl = {
  queryOtherContractSealList: '/erm/otherContractSealList/queryOtherContractSealList',
  otherContractSealNew: '/erm/otherContractSeal/new',
  otherContractSealInfo: '/erm/otherContractSeal/info',
  queryExport: '/erm/otherContractSealList/queryOtherContractSealListExport',
  creatBus: '/erm/otherContractSeal/creatBus'
};

export const queryOtherContractSealListHttp = params => {
  return $http.post(apiUrl.queryOtherContractSealList, params);
};
export const otherContractSealNewHttp = params => {
  return $http.post(apiUrl.otherContractSealNew, params);
};
export const otherContractSealInfoHttp = params => {
  return $http.fetch(apiUrl.otherContractSealInfo, params);
};
export const creatBusHttp = params => {
  return $http.post(apiUrl.creatBus, params);
};
