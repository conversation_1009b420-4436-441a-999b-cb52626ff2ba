/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 14:22:57
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-01 10:22:33
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/collection/baseInfo.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Verifics from '@/apps/erm/component/verifica/index.vue';
import { validateMoney, ermGlobalApi, dicCode } from '@/apps/erm/config/config.jsx';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import ErmTreeSelect from '@/apps/erm/component/ermTree/index.vue';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import LabelIndex from '@/apps/erm/component/ermSelect/labelIndex.vue';
import MoneyInput from '@/apps/erm/component/inputMoneyNumber/index.vue';
import { apiUrl } from './api.js';
import Vrouter from '@/router';
import ermHooks from '@/apps/erm/hooks/index.js';

const datePickerProps = {
  style: { width: '100%' },
  format: 'YYYY-MM-DD',
  'value-format': 'YYYY-MM-DD'
};
const router = Vrouter;

export const useFormSchema = params => {
  // lastDataInfo
  // console.log('params===>', params)

  return [
    {
      label: '合同号',
      component: null,
      prop: 'contractSn',
      copy: false
    },
    {
      label: '合同名称',
      component: params.isEdit ? 'el-input' : null,
      prop: 'contractName',
      props: {
        placeholder: '请输入合同名称'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'contractName')
    },
    {
      label: '归档编号',
      component: null,
      prop: 'archiveSn',
      hidden: !['info', 'audit', 'end_add', 'cancel_add'].includes(params.type),
      copy: false
    },
    {
      label: '合同金额(元)',
      component: params.isEdit ? () => <MoneyInput></MoneyInput> : null,
      prop: 'contractAmount',
      props: {
        placeholder: '请输入合同金额',
        moneyCapitalShow: true,
        isEdit: true
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'contractAmount', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '合同签订时间',
      component:
        (params.isEdit && params.canChangeAll) ||
        (['audit'].includes(params.type) && ['ERM_COLLECTION_CONTRACT_ADD'].includes(params.dicBusinessTypeCode)) ||
        (['ERM_COLLECTION_CONTRACT_CHANGE'].includes(params.dicBusinessTypeCode) && params.canChangeAll)
          ? 'el-date-picker'
          : null,
      prop: 'signDate',
      props: {
        placeholder: '请选择合同签订时间',
        ...datePickerProps
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'signDate')
    },
    {
      label: '创建时间',
      component: null,
      prop: 'createTime',
      hidden: !['info', 'audit', 'end_add', 'cancel_add'].includes(params.type),
      copy: false
    },
    {
      label: '生效时间',
      component: null,
      prop: 'takeEffectTime',
      hidden: !['info', 'audit', 'end_add', 'cancel_add'].includes(params.type),
      copy: false
    },
    {
      label: '甲方',
      component: ({ formModel }) =>
        params.isEdit ? (
          <InfiniteSelect
            api={apiUrl.queryCustomerList}
            defaultProps={{
              keyWord: 'nameOrSuCreditCode',
              name: 'customerName',
              id: 'id',
              sn: 'suCreditCode',
              nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
            }}
            maxWidth={500}
            otherParams={{
              dicCustomerTypeCode: '1'
            }}
          >
            {{
              default: () => (
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    router.push({
                      name: 'erm_supplier_add',
                      query: {
                        title: '编辑',
                        bizName: '新建',
                        type: 'add',
                        tab: '新增'
                      }
                    });
                  }}
                >
                  新建客商
                </el-button>
              )
            }}
          </InfiniteSelect>
        ) : (
          <div>
            <el-button
              link
              type="primary"
              onClick={e => {
                router.push({
                  name: 'erm_supplier_two',
                  query: {
                    title: formModel?.partyA?.name || '客商管理',
                    bizName: '详情',
                    type: 'info',
                    id: formModel?.partyA?.id,
                    tab: ['客商管理', formModel?.partyA?.name, '详情'].join('-')
                  }
                });
              }}
            >
              {formModel?.partyA?.name}
            </el-button>
          </div>
        ),

      prop: 'partyA',
      props: {
        placeholder: '请选择甲方'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'partyA', obj => {
          return <span class="funi-erm-change">{obj.name}</span>;
        })
    },
    {
      label: '项目乙方', //customerListByCurrent
      component: ({ formModel }) => {
        return params.isEdit && params.canChangeAll ? (
          <el-select
            style="width:100%"
            onChange={e => {
              let list = params.customerListByCurrent.filter(item => item.id === e);
              params?.setPartB(list[0].name);
            }}
          >
            {params.customerListByCurrent.map(item => (
              <el-option key={item.id} label={item.name} value={item.id} />
            ))}
          </el-select>
        ) : (
          <div>
            <el-button
              link
              type="primary"
              onClick={e => {
                router.push({
                  name: 'erm_supplier_two',
                  query: {
                    title: formModel?.partyB_object?.name || '客商管理',
                    bizName: '详情',
                    type: 'info',
                    id: formModel?.partyB_object?.id,
                    tab: ['客商管理', formModel?.partyB_object?.name, '详情'].join('-')
                  }
                });
              }}
            >
              {formModel?.partyB_object?.name}
            </el-button>
          </div>
        );
      },
      prop: params.isEdit ? 'partyB' : 'partyB_object',
      props: {
        placeholder: '请选择乙方'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'partyB_object', obj => {
          return <span class="funi-erm-change">{obj.name}</span>;
        })
    },
    {
      label: '项目归属公司',
      component: () => <LabelIndex options={params.companyList}></LabelIndex>,
      prop: 'companyId',
      props: {
        placeholder: '请输选择项目归属公司'
      }
    },
    {
      label: '项目归属部门',
      component:
        params.isEdit && params.canChangeAll
          ? () => (
              <ErmTreeSelect
                style="width:100%"
                propsMap={{
                  value: 'id',
                  label: 'deptName',
                  children: 'childrenDept'
                }}
                isDetp={true}
                init={params.type == 'add' ? true : false}
                params={{
                  id: params?.getCompanyId()
                }}
                onChange={(e, n) => {
                  params?.setDeptId({
                    id: e,
                    name: n
                  });
                }}
              ></ErmTreeSelect>
            )
          : null,
      prop: params.isEdit && params.canChangeAll ? 'deptId' : 'deptName',
      props: {
        placeholder: '请选择项目归属部门'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'deptName')
    },
    {
      label: '项目名称',
      component: ({ formModel }) =>
        params.isEdit && params.canChangeAll ? (
          <InfiniteSelect
            api={apiUrl.queryProjectListForContract}
            defaultProps={{
              keyWord: 'projectName',
              name: 'projectName',
              id: 'id'
            }}
          ></InfiniteSelect>
        ) : (
          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'erm_projectManage_two',
                query: {
                  title: formModel?.projectObj?.name || '项目管理',
                  bizName: '详情',
                  type: 'info',
                  id: formModel?.projectObj?.id,
                  tab: ['项目管理', formModel?.projectObj?.name, '详情'].join('-')
                }
              });
            }}
          >
            {formModel?.projectObj?.name}
          </el-button>
          // <span>{formModel?.projectObj?.name}</span>
        ),
      prop: 'projectObj',
      props: {
        placeholder: '请选择项目名称'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'projectObj', obj => (
          <span class="funi-erm-change">{obj.name}</span>
        ))
    },
    {
      label: '负责人',
      component: params.isEdit
        ? () => (
            <InfiniteSelect
              api={ermGlobalApi.queryEmployeeList}
              defaultProps={{
                keyWord: 'keyword',
                name: 'employeeName',
                id: 'id'
              }}
            ></InfiniteSelect>
          )
        : null,
      prop: params.isEdit ? 'employeeObj' : 'employeeName',
      props: {
        placeholder: '请选择负责人'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'employeeName')
    },
    {
      label: '合同开始时间',
      component: params.isEdit ? 'el-date-picker' : null,
      prop: 'startDate',
      props: {
        placeholder: '请选择合同开始时间',
        ...datePickerProps,
        'disabled-date': params.disabledStartDate
      },
      on: {
        change: e => {
          params.setDate('startDate', e);
        }
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'startDate')
    },
    {
      label: '合同结束时间',
      component: params.isEdit ? 'el-date-picker' : null,
      prop: 'endDate',
      props: {
        placeholder: '请选择合同结束时间',
        ...datePickerProps,
        'disabled-date': params.disabledEndDate
      },
      on: {
        change: e => {
          params.setDate('endDate', e);
        }
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'endDate')
    },
    {
      label: '结算方式',
      component: params.isEdit ? () => <ErmSelect code={dicCode.settlement_type}></ErmSelect> : null,
      prop: params.isEdit ? 'dicSettlementTypeCode' : 'dicSettlementTypeName',
      props: {
        placeholder: '请选择结算方式'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicSettlementTypeName')
    },
    {
      label: '收款方式',
      component: params.isEdit ? () => <ErmSelect code={dicCode.coll_type}></ErmSelect> : null,
      prop: params.isEdit ? 'dicCollTypeCode' : 'dicCollTypeName',
      props: {
        placeholder: '请选择收款方式'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicCollTypeName')
    },
    {
      label: '合同状态',
      component: params.isEdit ? () => <LabelIndex code=""></LabelIndex> : null,
      prop: params.isEdit ? 'dicContractStatusCode' : 'dicContractStatusName',
      copy: false,
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicContractStatusName')
    },
    {
      label: '执行状态',
      component: null,
      prop: 'dicExecuteTypeName',
      props: {
        placeholder: '请选择执行状态'
      },
      copy: false
    },
    {
      label: '是否格式合同',
      component: params.isEdit
        ? () => (
            <el-select
              onChange={e => {
                params.setisFormat(e);
              }}
              style="width:100%"
            >
              <el-option key="1" label="是" value={true} />
              <el-option key="2" label="否" value={false} />
            </el-select>
          )
        : obj => {
            return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
          },
      prop: 'isFormat',
      props: {
        placeholder: '请选择是否格式合同',
        disabled: params?.isFormatDisableed
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'isFormat', bool => (
          <span class="funi-erm-change">{bool === true ? '是' : bool === false ? '否' : '--'}</span>
        ))
    },
    {
      label: '格式合同类型',
      component: params.isEdit ? () => <ErmSelect options={params.dicFormatTypeList}></ErmSelect> : null,
      prop: params.isEdit ? 'dicFormatTypeCode' : 'dicFormatTypeName',
      props: {
        placeholder: '请选择格式合同类型'
      },
      hidden: !params.isFormat,
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicFormatTypeName')
    },
    {
      label: '临时合同用印申请',
      component: ({ formModel }) =>
        params.isEdit ? (
          <InfiniteSelect
            api={apiUrl.otherContractExt}
            defaultProps={{
              keyWord: 'keyword',
              name: 'tempContractAppTitle',
              id: 'id'
            }}
          ></InfiniteSelect>
        ) : (
          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'ermContractOtherInfo',
                query: {
                  title: formModel?.otherContractSealObj?.name || '其他合同用印申请',
                  bizName: '详情',
                  type: 'info',
                  id: formModel?.otherContractSealObj?.id,
                  tab: ['其他合同用印申请', formModel?.otherContractSealObj?.name].join('-')
                }
              });
            }}
          >
            {formModel?.otherContractSealObj?.name}
          </el-button>
        ),
      prop: 'otherContractSealObj',
      hidden: !params.isFormat,
      props: {
        placeholder: '请选择临时合同用印申请'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'otherContractSealObj', obj => (
          <span class="funi-erm-change">{obj.name}</span>
        ))
    },
    {
      label: '是否包含开放性条款',
      component: params.isEdit
        ? () => (
            <el-select
              onChange={e => {
                params?.setIsIncludeOpenTerm(e, false);
              }}
              style="width:100%"
            >
              <el-option key="1" label="是" value={true} />
              <el-option key="2" label="否" value={false} />
            </el-select>
          )
        : obj => {
            return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
          },
      prop: 'isIncludeOpenTerm',
      props: {
        placeholder: '请选择是否包含开放性条款'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'isIncludeOpenTerm', bool => (
          <span class="funi-erm-change">{bool === true ? '是' : bool === false ? '否' : '--'}</span>
        ))
    },
    {
      label: '是否多方合同',
      component: params.isEdit
        ? () => (
            <el-select
              onChange={e => {
                params?.setisMultipartyContract(e);
              }}
              style="width:100%"
            >
              <el-option key="1" label="是" value={true} />
              <el-option key="2" label="否" value={false} />
            </el-select>
          )
        : obj => {
            return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
          },
      prop: 'isMultipartyContract',
      props: {
        placeholder: '请选择是否多方合同'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'isMultipartyContract', bool => (
          <span class="funi-erm-change">{bool === true ? '是' : bool === false ? '否' : '--'}</span>
        ))
    },
    {
      label: '合同服务总金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'serviceTotalAmount',
      props: {
        isEdit: false,
        moneyCapitalShow: true
      },
      copy: false,
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'serviceTotalAmount', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '合同已开票金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'invoiceAmount',
      props: {
        isEdit: false,
        moneyCapitalShow: true
      },
      copy: false,
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'invoiceAmount', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '合同未开票金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'uninvoiceAmount',
      props: {
        isEdit: false,
        moneyCapitalShow: true
      },
      copy: false,
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'uninvoiceAmount', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '用印公司',
      component: () => <LabelIndex options={params.companyList}></LabelIndex>,
      prop: 'sealCompanyId'
    },
    {
      label: '是否为住建、农业等面向政府侧的合同',
      component: params.isEdit
        ? () => {
            return (
              <el-radio-group v-model={params.isGovernmentContract} disabled={params.type == 'change_add'}>
                <el-radio label={true}>是</el-radio>
                <el-radio label={false}>否</el-radio>
              </el-radio-group>
            );
          }
        : ({ formModel }) => {
            return (
              <span>
                {formModel.isGovernmentContract === true
                  ? '是'
                  : formModel.isGovernmentContract === false
                  ? '否'
                  : '--'}
              </span>
            );
          },
      prop: 'isGovernmentContract'
    },
    ...(['end_add', 'end_edit'].includes(params.type) ||
    params.dicBusinessTypeCode == 'ERM_COLLECTION_CONTRACT_TERMINATE'
      ? [
          {
            label: '是否用印',
            component:
              params.isEdit || ['end_add', 'end_edit'].includes(params.type)
                ? () => (
                    <el-select
                      style="width:100%"
                      onChange={e => {
                        params.isSealUsed.value = e;
                      }}
                    >
                      <el-option key="1" label="是" value={true} />
                      <el-option key="2" label="否" value={false} />
                    </el-select>
                  )
                : obj => {
                    return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
                  },
            prop: 'isSealUsed'
          }
        ]
      : []),
    ...((['end_add', 'end_edit'].includes(params.type) ||
      params.dicBusinessTypeCode == 'ERM_COLLECTION_CONTRACT_TERMINATE') &&
    !params.isSealUsed.value
      ? []
      : [
          {
            label: '用印类型',
            component:
              params.isEdit || params.type.indexOf('end') > -1
                ? () => (
                    <el-checkbox-group>
                      {params?.sealTypeList.map(item => {
                        return <el-checkbox label={item.code}>{item.name}</el-checkbox>;
                      })}
                    </el-checkbox-group>
                  )
                : null,
            prop: params.isEdit || params.type.indexOf('end') > -1 ? 'dicSealTypeCode' : 'dicSealTypeName',
            colProps: {
              span: 24
            },
            extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicSealTypeName')
          },
          {
            label: '合同份数',
            component: params.isEdit ? 'el-input' : null,
            prop: 'contractCount',
            extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'contractCount')
          }
        ]),
    {
      label: '是否招投标',
      component: params.isEdit
        ? () => (
            <el-select style="width:100%">
              <el-option key="1" label="是" value={true} />
              <el-option key="2" label="否" value={false} />
            </el-select>
          )
        : obj => {
            return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
          },
      prop: 'isTender',
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'isTender', bool => (
          <span class="funi-erm-change">{bool === true ? '是' : bool === false ? '否' : '--'}</span>
        ))
    },
    ...(['change_add', 'chang_edit'].includes(params.type) ||
    ['ERM_COLLECTION_CONTRACT_CHANGE'].includes(params.dicBusinessTypeCode)
      ? [
          {
            label: '变更类型',
            component: params.isEdit ? () => <ErmSelect code={dicCode.changeType}></ErmSelect> : null,
            prop: params.isEdit ? 'dicChangeTypeCode' : 'dicChangeTypeName',
            props: {
              placeholder: '请选择变更类型'
            },
            extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicChangeTypeName')
          }
        ]
      : []),
    {
      label: '合同关键条款',
      component: params.isEdit
        ? 'el-input'
        : ({ formModel }) => {
            return (
              <span
                style={{
                  'white-space': 'pre-wrap'
                }}
              >
                {formModel.keyTerm || '--'}
              </span>
            );
          },
      prop: 'keyTerm',
      colProps: {
        span: 24
      },
      props: {
        autosize: { minRows: 2, maxRows: 4 },
        type: 'textarea',
        maxlength: 200,
        'show-word-limit': true
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'keyTerm')
    },
    {
      label: '备注',
      component: params.isEdit
        ? 'el-input'
        : ({ formModel }) => {
            return (
              <span
                style={{
                  'white-space': 'pre-wrap'
                }}
              >
                {formModel.remark || '--'}
              </span>
            );
          },
      prop: 'remark',
      colProps: {
        span: 24
      },
      props: {
        autosize: { minRows: 2, maxRows: 4 },
        type: 'textarea',
        maxlength: 200,
        'show-word-limit': true
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'remark')
    }
  ];
};

export const useRule = ({ isEdit, isRequiredSignDate, type, dicBusinessTypeCode }) => {
  let end_ = type.indexOf('end') > -1;
  const change_ = type.indexOf('change') > -1;
  let a = 'partyA';
  let b = isEdit ? 'partyB' : 'partyB_object';
  let c = isEdit ? 'deptId' : 'deptName';
  let d = isEdit ? 'projectObj' : 'projectName';
  let e = isEdit ? 'employeeObj' : 'employeeName';
  let f = isEdit ? 'dicSettlementTypeCode' : 'dicSettlementTypeName';
  let g = isEdit ? 'dicCollTypeCode' : 'dicCollTypeName';
  let h = isEdit ? 'dicFormatTypeCode' : 'dicFormatTypeName';
  let i = isEdit || end_ ? 'dicSealTypeCode' : 'dicSealTypeName';
  let j = isEdit ? 'dicChangeTypeCode' : 'dicChangeTypeName';
  // ...['change_add', 'chang_edit',].includes(params.type) || ["ERM_COLLECTION_CONTRACT_CHANGE"].includes(params.dicBusinessTypeCode) ?
  return {
    contractName: [{ required: true, message: '必填', trigger: 'change' }],
    contractAmount: [{ required: true, message: '必填', trigger: 'change' }],
    isGovernmentContract: [{ required: true, message: '必填', trigger: 'change' }],
    signDate: isRequiredSignDate ? [{ required: true, message: '必填', trigger: 'change' }] : void 0,
    [a]: [{ required: true, message: '必填', trigger: 'change' }],
    [b]: [{ required: true, message: '必填', trigger: 'change' }],
    [c]: [{ required: true, message: '必填', trigger: 'change' }],
    [d]: [{ required: true, message: '必填', trigger: 'change' }],
    [e]: [{ required: true, message: '必填', trigger: 'change' }],
    [f]: [{ required: true, message: '必填', trigger: 'change' }],
    [g]: [{ required: true, message: '必填', trigger: 'change' }],
    isFormat: [{ required: true, message: '必填', trigger: 'change' }],
    [h]: [{ required: true, message: '必填', trigger: 'change' }],
    isIncludeOpenTerm: [{ required: true, message: '必填', trigger: 'change' }],
    isMultipartyContract: [{ required: true, message: '必填', trigger: 'change' }],
    [i]: end_ || change_ ? [] : [{ required: true, message: '必填', trigger: 'change' }],
    contractCount: [{ required: true, message: '必填', trigger: 'change' }],
    isTender: [{ required: true, message: '必填', trigger: 'change' }],
    keyTerm: [{ required: true, message: '必填', trigger: 'change' }],
    [j]: [{ required: true, message: '必填', trigger: 'change' }],
    ...(['end_add', 'end_edit'].includes(type) || dicBusinessTypeCode == 'ERM_COLLECTION_CONTRACT_TERMINATE'
      ? {
          isSealUsed: [{ required: true, message: '必填', trigger: 'change' }],
          dicSealTypeCode: [{ required: true, message: '必填', trigger: 'change' }],
          dicSealTypeName: [{ required: true, message: '必填', trigger: 'change' }]
        }
      : {})

    // [c]: [{ required: true, message: '必填', trigger: 'change' }],
    // [d]: [{ required: true, message: '必填', trigger: 'change' }],
  };
};

export const serveColumns = ({
  isEdit,
  iptChange = () => {},
  contactRef = {},
  serviceContentList,
  isIncludeOpenTerm,
  delFunc = () => {},
  type = '',
  serviceObj = {},
  setContractAmount = () => {},
  isInitiatorNode
}) => {
  isEdit = isEdit || isInitiatorNode;
  const reset_v = (key, el) => {
    contactRef[key] = el;
  };
  const isChangeAll = row => {
    if (type.indexOf('change') < 0) {
      return true;
    }
    return !row.serviceSn || serviceObj[row.serviceSn];
  };
  return [
    {
      label: '来源',
      prop: 'dicServiceSourceCode',
      slots: {
        header: 'a'
      },
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <ErmSelect
            code={dicCode.service_source}
            onChange={e => {
              iptChange(index, 'dicServiceSourceCode', e);
            }}
            placeholder="来源"
          />
        );
        return isEdit && isChangeAll(row) ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_dicServiceSourceCode`, el);
            }}
            value={row.dicServiceSourceCode}
            c={c}
            key={`${row.uuId}_dicServiceSourceCode`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.dicServiceSourceName}</span>
        );
      }
    },
    {
      label: '服务内容',
      prop: 'dicServiceContentCode',
      slots: {
        header: 'b'
      },
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <el-select
            style="width:100%"
            filterable
            onChange={e => {
              console.log(row, 'rowwwww');
              let v = serviceContentList.filter(item => item.dicServiceContentCode === e);
              iptChange(index, 'dicServiceContentCode', e);
              iptChange(index, 'dicServiceContentName', e ? v[0].dicServiceContentName : '');
              if (row.taxRateModifiable) {
                iptChange(index, 'taxRate', e ? v[0].taxRate : '');
              }
            }}
            placeholder="服务内容"
          >
            {serviceContentList.map(item => (
              <el-option
                key={item.dicServiceContentCode}
                label={item.dicServiceContentName}
                value={item.dicServiceContentCode}
              />
            ))}
          </el-select>
        );
        return isEdit && isChangeAll(row) ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_dicServiceContentCode`, el);
            }}
            value={row.dicServiceContentCode}
            c={c}
            key={`${row.uuId}_dicServiceContentCode`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.dicServiceContentName}</span>
        );
      }
    },

    {
      label: '是否开放性条款',
      prop: 'isOpenClause',
      slots: {
        header: 'c'
      },
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <el-select
            placeholder="请选择"
            onChange={e => {
              console.log(e, 'eeeeeeeeee');
              if (e !== row.isOpenClause) {
                row.dicConfirmModeCode = void 0;
              }
              iptChange(index, 'isOpenClause', e);
              //   setContractAmount();
            }}
          >
            <el-option disabled={!isIncludeOpenTerm} label="是" value={true}></el-option>
            <el-option label="否" value={false}></el-option>
          </el-select>
        );

        return isEdit && isChangeAll(row) ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_isOpenClause`, el);
            }}
            value={row.isOpenClause}
            c={c}
            key={`${row.uuId}_isOpenClause`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.isOpenClause ? '是' : '否'}</span>
        );
      }
    },
    {
      label: '签约类型',
      prop: 'dicSignTypeCode',
      slots: {
        header: 'd'
      },
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <ErmSelect
            code={dicCode.sign_type}
            onChange={e => {
              iptChange(index, 'dicSignTypeCode', e);
            }}
            placeholder="签约类型"
          />
        );

        return isEdit && isChangeAll(row) ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_dicSignTypeCode`, el);
            }}
            value={row.dicSignTypeCode}
            c={c}
            key={`${row.uuId}_dicSignTypeCode`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.dicSignTypeName}</span>
        );
      }
    },
    {
      label: '确收方式',
      prop: 'dicConfirmModeCode',
      slots: {
        header: 'e'
      },
      width: 180,
      render: ({ row, index }) => {
        let disabled = [];
        if (row.isOpenClause === true) {
          disabled = ['1'];
        }
        if (row.isOpenClause === void 0) {
          row.dicConfirmModeCode = void 0;
        }
        let c = (
          <ErmSelect
            code={dicCode.confirm_mode}
            onChange={e => {
              iptChange(index, 'dicConfirmModeCode', e);
              if (row.dicConfirmModeCode !== '1') {
                reset_v(`${row.uuId}_actualStartTime`, void 0);
                iptChange(index, 'actualStartTime', void 0);
                reset_v(`${row.uuId}_actualEndTime`, void 0);
                iptChange(index, 'actualEndTime', void 0);
              }
            }}
            disabledList={disabled}
            placeholder="确收方式"
          />
        );

        return isEdit && isChangeAll(row) ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_dicConfirmModeCode`, el);
            }}
            value={row.dicConfirmModeCode}
            c={c}
            key={`${row.uuId}_dicConfirmModeCode`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.dicConfirmModeName}</span>
        );
      }
    },
    {
      label: '含税金额(元)',
      prop: 'includeTaxAmount',
      slots: {
        header: 'f'
      },
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        if (row.isOpenClause === true) {
          row.includeTaxAmount = '';
        }
        let c = (
          <MoneyInput
            controls={false}
            onChange={e => {
              iptChange(index, 'includeTaxAmount', e);
              setContractAmount();
            }}
            maxlength={50}
            placeholder="含税金额"
          />
        );
        if (isEdit && isChangeAll(row) && row.isOpenClause !== true) {
          return (
            <Verifics
              ref={el => {
                reset_v(`${row.uuId}_includeTaxAmount`, el);
              }}
              value={row.includeTaxAmount}
              c={c}
              key={`${row.uuId}_includeTaxAmount`}
              rule={[{ required: true, message: '必填' }]}
            ></Verifics>
          );
        } else {
          reset_v(`${row.uuId}_includeTaxAmount`, void 0);
          return <span> {ermHooks.erm_intl(row.includeTaxAmount)}</span>;
        }
      }
    },
    {
      label: '实际开始时间',
      prop: 'actualStartTime',
      slots: {
        header: 'g'
      },
      width: 200,
      render: ({ row, index }) => {
        if (isEdit && isChangeAll(row)) {
          if (row.dicConfirmModeCode == '1') {
            let disabledDate = time => {
              return row.actualEndTime && time.getTime() > $utils.Date(row.actualEndTime);
            };
            let c = (
              <el-date-picker
                style="width:100%"
                disabled-date={disabledDate}
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            );
            return (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_actualStartTime`, el);
                }}
                value={row.actualStartTime}
                c={c}
                onChange={e => {
                  iptChange(index, 'actualStartTime', e);
                }}
                key={`${row.uuId}_actualStartTime`}
                rule={[{ required: true, message: '必填' }]}
              ></Verifics>
            );
          } else {
            // reset_v(`${row.uuId}_actualStartTime`, void 0);
            // iptChange(index, 'actualStartTime', void 0);
          }
        } else {
          return <span> {row.actualStartTime || '--'}</span>;
        }
      }
    },
    {
      label: '实际结束时间',
      prop: 'actualEndTime',
      slots: {
        header: 'h'
      },
      width: 180,
      render: ({ row, index }) => {
        if (isEdit && isChangeAll(row)) {
          let disabledDate = time => {
            return row.actualStartTime && time.getTime() < $utils.Date(row.actualStartTime);
          };
          let c = (
            <el-date-picker
              style="width:100%"
              disabled-date={disabledDate}
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            ></el-date-picker>
          );
          if (row.dicConfirmModeCode == '1') {
            return (
              <Verifics
                ref={el => {
                  reset_v(`${row.uuId}_actualEndTime`, el);
                }}
                value={row.actualEndTime}
                c={c}
                onChange={e => {
                  iptChange(index, 'actualEndTime', e);
                }}
                key={`${row.uuId}_actualEndTime`}
                rule={[{ required: true, message: '必填' }]}
              ></Verifics>
            );
          } else {
            // reset_v(`${row.uuId}_actualEndTime`, void 0);
            // iptChange(index, 'actualEndTime', void 0);
          }
        } else {
          return <span> {row.actualEndTime || '--'}</span>;
        }
      }
    },
    {
      // label: "税率(服务0.06,硬件0.13)",
      label: '税率',
      prop: 'taxRate',
      width: 180,
      align: 'right',
      slots: {
        header: 'l'
      },
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            controls={false}
            onChange={e => {
              iptChange(index, 'taxRate', e);
            }}
            maxlength={50}
            placeholder="税率"
          />
        );
        if (isEdit && isChangeAll(row) && row.taxRateModifiable) {
          return (
            <Verifics
              ref={el => {
                reset_v(`${row.id}_taxRate`, el);
              }}
              value={row.taxRate}
              c={c}
              key={`${row.id}_taxRate`}
              rule={[{ required: true, message: '必填' }]}
            ></Verifics>
          );
        } else {
          return <span> {ermHooks.erm_intl(row.taxRate)}</span>;
        }
      }
    },
    {
      label: '税额(元)',
      prop: 'taxAmount',
      width: 180,
      align: 'right',
      render: ({ row, index }) => {
        let { excludeTaxAmount, includeTaxAmount } = row;
        if (excludeTaxAmount && includeTaxAmount) {
          let a = (includeTaxAmount - excludeTaxAmount).toFixed(2);
          row.taxAmount = a;
          return ermHooks.erm_intl(a);
        }
        row.taxAmount = '';
        return '';
      }
    },
    {
      label: '不含税金额(元)',
      prop: 'excludeTaxAmount',
      align: 'right',
      width: 180,
      render: ({ row, index }) => {
        let { taxRate, includeTaxAmount } = row;
        if (taxRate && includeTaxAmount) {
          let a = (includeTaxAmount / (1 + taxRate)).toFixed(2);
          row.excludeTaxAmount = a;
          return ermHooks.erm_intl(a);
        }
        row.excludeTaxAmount = '';
        return '';
      }
    },
    {
      label: '绩效比例(%)',
      prop: 'performanceProp',
      align: 'right',
      width: 180,
      render: ({ row, index }) => {
        let { dicServiceContentCode } = row;
        let data = serviceContentList.filter(item => item.dicServiceContentCode == dicServiceContentCode);
        if (data && data.length) {
          row.performanceRatio = data[0].performanceRatio;
          return data[0].performanceRatio;
        } else {
          row.performanceRatio = '';
          return '--';
        }
      }
    },
    {
      label: '已确收(元)',
      prop: 'confirmReceipt',
      align: 'right',
      width: 180,
      render: ({ row, index }) => ermHooks.erm_intl(row.confirmReceipt)
    },
    {
      label: '未确收(元)',
      prop: 'unconfirmReceipt',
      align: 'right',
      width: 180,
      render: ({ row, index }) => ermHooks.erm_intl(row.unconfirmReceipt)
    },
    {
      label: '累计已收(元)',
      prop: 'accumulatReceived',
      align: 'right',
      width: 180,
      render: ({ row, index }) => ermHooks.erm_intl(row.accumulatReceived)
    },
    {
      label: '剩余未收(元)',
      prop: 'surplusReceived',
      align: 'right',
      width: 180,
      render: ({ row, index }) => ermHooks.erm_intl(row.surplusReceived)
    },
    {
      label: '收入大类',
      prop: 'dicIncomeCategoryName',
      width: 180,
      render: isEdit
        ? ({ row, index }) => {
            let { dicServiceContentCode } = row;
            let data = serviceContentList.filter(item => item.dicServiceContentCode == dicServiceContentCode);
            if (data && data.length) {
              row.dicIncomeCategoryCode = data[0].dicIncomeCategoryCode;
              return data[0].dicIncomeCategoryName;
            } else {
              row.dicIncomeCategoryCode = '';
              return '--';
            }
          }
        : null
    },
    {
      label: '收入小类',
      prop: 'dicIncomeSubclassName',
      width: 180,
      render: isEdit
        ? ({ row, index }) => {
            let { dicServiceContentCode } = row;
            let data = serviceContentList.filter(item => item.dicServiceContentCode == dicServiceContentCode);
            if (data && data.length) {
              row.dicIncomeSubclassCode = data[0].dicIncomeSubclassCode;
              return data[0].dicIncomeSubclassName;
            } else {
              row.dicIncomeSubclassCode = '';
              return '--';
            }
          }
        : null
    },
    {
      label: '备注',
      prop: 'remark',
      width: 180,
      render: ({ row, index }) => {
        return isEdit && isChangeAll(row) ? (
          <el-input v-model={row.remark} {...{ prop: datePickerProps }}></el-input>
        ) : (
          <span>{row.remark}</span>
        );
      }
    },
    ...(isEdit && !isInitiatorNode
      ? [
          {
            label: '操作',
            prop: '',
            align: 'center',
            fixed: 'right',
            cellInteraction: false,
            width: 100,
            render: ({ row, index }) => {
              return isChangeAll(row) ? (
                <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                  {
                    <el-button
                      type="primary"
                      onClick={() => {
                        delFunc(index);
                      }}
                      link
                    >
                      删除
                    </el-button>
                  }
                </div>
              ) : (
                '--'
              );
            }
          }
        ]
      : [])
  ];
};
/**
 * 调整服务金额
 * @param {*} param
 * @returns
 */
export const serveAmountColumns = ({ isEdit, iptChange = () => {}, contactRef = {}, serveAmountDelFn = () => {} }) => {
  const reset_v = (key, el) => {
    contactRef[key] = el;
  };
  return [
    {
      label: '序号',
      prop: 'index',
      render: ({ row, index }) => {
        return index + 1;
      }
    },
    {
      label: '合同编号',
      prop: 'contractSn'
    },
    {
      label: '来源',
      prop: 'dicServiceSourceName'
    },
    {
      label: '服务内容',
      prop: 'dicServiceContentName'
    },

    {
      label: '是否开放性条款',
      prop: 'isOpenClause',
      render: ({ row, index }) => {
        return <span>{row.isOpenClause ? '是' : '否'}</span>;
      }
    },
    {
      label: '签约类型',
      prop: 'dicSignTypeName'
    },
    {
      label: '确收方式',
      prop: 'dicConfirmModeName'
    },
    {
      label: '确收总金额（元）',
      prop: 'confirmReceipt',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.confirmReceipt);
      }
    },
    {
      label: '调整后确收金额（元）',
      prop: 'sufConfirmAmount',
      slots: {
        header: 'a'
      },
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            controls={false}
            onChange={e => {
              iptChange(index, 'sufConfirmAmount', e);
            }}
            maxlength={50}
            placeholder="请输入调整后确收金额"
          />
        );
        if (isEdit) {
          return (
            <Verifics
              ref={el => {
                reset_v(`${row.id}_sufConfirmAmount`, el);
              }}
              value={row.sufConfirmAmount}
              c={c}
              key={`${row.id}_sufConfirmAmount`}
              rule={[{ required: true, message: '必填' }]}
            ></Verifics>
          );
        } else {
          reset_v(`${row.id}_sufConfirmAmount`, void 0);
          return <span> {ermHooks.erm_intl(row.sufConfirmAmount)}</span>;
        }
      }
    },
    {
      label: '收款总金额（元）',
      prop: 'accumulatReceived',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.accumulatReceived);
      }
    },
    {
      label: '调整后收款金额（元）',
      prop: 'sufCollectionAmount',
      slots: {
        header: 'b'
      },
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            controls={false}
            onChange={e => {
              iptChange(index, 'sufCollectionAmount', e);
            }}
            maxlength={50}
            placeholder="请输入调整后收款金额"
          />
        );
        if (isEdit) {
          return (
            <Verifics
              ref={el => {
                reset_v(`${row.id}_sufCollectionAmount`, el);
              }}
              value={row.sufCollectionAmount}
              c={c}
              key={`${row.id}_sufCollectionAmount`}
              rule={[{ required: true, message: '必填' }]}
            ></Verifics>
          );
        } else {
          reset_v(`${row.id}_sufCollectionAmount`, void 0);
          return <span> {ermHooks.erm_intl(row.sufCollectionAmount)}</span>;
        }
      }
    },
    {
      label: '剩余未收金额（元）',
      prop: 'surplusReceived',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.surplusReceived);
      }
    },
    {
      label: '已开票金额（元）',
      prop: 'invoiceAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.invoiceAmount);
      }
    },
    {
      label: '调整后已开票金额（元）',
      prop: 'sufInvoiceAmount',
      slots: {
        header: 'c'
      },
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            controls={false}
            onChange={e => {
              iptChange(index, 'sufInvoiceAmount', e);
            }}
            maxlength={50}
            placeholder="请输入调整后已开票金额"
          />
        );
        if (isEdit) {
          return (
            <Verifics
              ref={el => {
                reset_v(`${row.id}_sufInvoiceAmount`, el);
              }}
              value={row.sufInvoiceAmount}
              c={c}
              key={`${row.id}_sufInvoiceAmount`}
              rule={[{ required: true, message: '必填' }]}
            ></Verifics>
          );
        } else {
          reset_v(`${row.id}_sufInvoiceAmount`, void 0);
          return <span> {ermHooks.erm_intl(row.sufInvoiceAmount)}</span>;
        }
      }
    },
    {
      label: '剩余未开票金额（元）',
      prop: 'unInvoiceAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.unInvoiceAmount);
      }
    },
    {
      label: '含税金额（元）',
      prop: 'includeTaxAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.includeTaxAmount);
      }
    },
    {
      label: '税率',
      prop: 'taxRate',
      align: 'right'
    },
    {
      label: '实际开始时间',
      prop: 'actualStartTime'
    },
    {
      label: '实际结束时间',
      prop: 'actualEndTime'
    },
    {
      label: '税额(元)',
      prop: 'taxAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.taxAmount);
      }
    },
    {
      label: '不含税金额(元)',
      prop: 'excludeTaxAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.excludeTaxAmount);
      }
    },
    {
      label: '绩效比例(%)',
      prop: 'performanceProp',
      align: 'right'
    },

    {
      label: '收入大类',
      prop: 'dicIncomeCategoryName'
    },
    {
      label: '收入小类',
      prop: 'dicIncomeSubclassName'
    },
    {
      label: '备注',
      prop: 'remark'
    },
    ...(isEdit
      ? [
          {
            label: '操作',
            prop: 'action',
            align: 'center',
            fixed: 'right',
            cellInteraction: false,
            width: 100,
            render: ({ row, index }) => {
              return (
                <el-button
                  type="primary"
                  onClick={() => {
                    serveAmountDelFn(index);
                  }}
                  link
                >
                  删除
                </el-button>
              );
            }
          }
        ]
      : [])
  ];
};

export const collection = ({ isEdit, iptChange = () => {}, contactRef = {}, delFunc = () => {} }) => {
  const reset_v = (key, el) => {
    contactRef[key] = el;
  };
  return [
    // ...(isEdit
    //     ? [
    //         {
    //             type: 'selection',
    //             width: '55px',
    //             fixed: 'left'
    //         }
    //     ]
    //     : []),
    {
      label: '应收编号',
      prop: 'collectionSn'
      // slots: {
      //   header: 'a'
      // },
      // render: ({ row, index }) => {
      //   let c = (
      //     <el-input
      //       onInput={e => {
      //         iptChange(index, 'collectionSn', e);
      //       }}
      //       maxlength={50}
      //       placeholder="应收编号"
      //     />
      //   );
      //   return isEdit ? (
      //     <Verifics
      //       ref={el => {
      //         reset_v(`${row.uuId}_collectionSn`, el);
      //       }}
      //       value={row.collectionSn}
      //       c={c}
      //       key={`${row.uuId}_collectionSn`}
      //       rule={[{ required: true, message: '必填' }]}
      //     ></Verifics>
      //   ) : (
      //     <span>{row.collectionSn}</span>
      //   );
      // }
    },
    {
      label: '收款条件',
      prop: 'collectionCondition',
      slots: {
        header: 'b'
      },
      render: ({ row, index }) => {
        let c = (
          <el-input
            onInput={e => {
              iptChange(index, 'collectionCondition', e);
            }}
            maxlength={50}
            placeholder="收款条件"
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_collectionCondition`, el);
            }}
            value={row.collectionCondition}
            c={c}
            key={`${row.uuId}_collectionCondition`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.collectionCondition}</span>
        );
      }
    },
    {
      label: '收款金额(元)',
      prop: 'collectionAmount',
      slots: {
        header: 'c'
      },
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            onChange={e => {
              iptChange(index, 'collectionAmount', e);
            }}
            maxlength={50}
            placeholder="收款金额(元)"
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_collectionAmount`, el);
            }}
            value={row.collectionAmount}
            c={c}
            key={`${row.uuId}_collectionAmount`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{ermHooks.erm_intl(row.collectionAmount)}</span>
        );
      }
    },
    {
      label: '预计收款日期',
      prop: 'expectedCollectionTime',
      width: '250px',
      render: ({ row, index }) => {
        let c = (
          <el-date-picker
            v-model={row.expectedCollectionTime}
            style="width:100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        );
        return isEdit ? c : <span>{row.expectedCollectionTime}</span>;
      }
    },
    {
      label: '收款状态',
      prop: 'dicCollectionStatusName'
    },
    {
      label: '实际收款总金额(元)',
      prop: 'actualCollectionAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.actualCollectionAmount)
    },
    ...(isEdit
      ? [
          {
            label: '操作',
            prop: '',
            align: 'center',
            fixed: 'right',
            cellInteraction: false,
            width: 100,
            render: ({ row, index }) => {
              return (
                <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                  {
                    <el-button
                      type="primary"
                      onClick={() => {
                        delFunc(index);
                      }}
                      link
                    >
                      删除
                    </el-button>
                  }
                </div>
              );
            }
          }
        ]
      : [])
  ];
};

export const busColumns = ({ isEdit, iptChange = () => {}, delFunc = () => {} }) => {
  return [
    // ...(isEdit
    //     ? [
    //         {
    //             type: 'selection',
    //             width: '55px',
    //             fixed: 'left'
    //         }
    //     ]
    //     : []),
    {
      label: '商机编号',
      prop: 'businessOppoSn',
      render: ({ row, index }) => {
        let c = <el-input v-model={row.businessOppoSn}></el-input>;
        return isEdit ? c : <span>{row.businessOppoSn}</span>;
      }
    },
    {
      label: '商机名称',
      prop: 'businessOppoName',
      render: ({ row, index }) => {
        let c = <el-input v-model={row.businessOppoName}></el-input>;
        return isEdit ? c : <span>{row.businessOppoName}</span>;
      }
    },
    {
      label: '商机跟进人',
      prop: 'businessOppoPerson',
      render: ({ row, index }) => {
        let c = <el-input v-model={row.businessOppoPerson}></el-input>;
        return isEdit ? c : <span>{row.businessOppoPerson}</span>;
      }
    },
    {
      label: '商机来源',
      prop: 'businessOppoSource',
      render: ({ row, index }) => {
        let c = <el-input v-model={row.businessOppoSource}></el-input>;
        return isEdit ? c : <span>{row.businessOppoSource}</span>;
      }
    },
    {
      label: '商机创建时间',
      prop: 'businessCreateTime',
      width: 250,
      render: ({ row, index }) => {
        let c = (
          <el-date-picker
            v-model={row.businessCreateTime}
            style="width:100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        );
        return isEdit ? c : <span>{row.businessCreateTime}</span>;
      }
    },
    ...(isEdit
      ? [
          {
            label: '操作',
            prop: '',
            align: 'center',
            fixed: 'right',
            cellInteraction: false,
            width: 100,
            render: ({ row, index }) => {
              return (
                <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                  {
                    <el-button
                      type="primary"
                      onClick={() => {
                        delFunc(index);
                      }}
                      link
                    >
                      删除
                    </el-button>
                  }
                </div>
              );
            }
          }
        ]
      : [])
  ];
};

export const adjustSchema = params => {
  return [
    {
      label: '合同服务总金额(元)',
      prop: 'serviceTotalAmount',
      component: () => <MoneyInput></MoneyInput>,
      props: {
        isEdit: false,
        moneyCapitalShow: true
      }
    },
    {
      label: '调整额度(元)',
      prop: 'contractAdjustAmount',
      component: () => <MoneyInput></MoneyInput>,
      hidden: !['info', 'audit'].includes(params.type),
      props: {
        isEdit: false,
        moneyCapitalShow: true
      }
    },
    {
      label: '调整后合同金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'sufContractAmount',
      props: {
        placeholder: '请输入调整后金额',
        moneyCapitalShow: true,
        isEdit: params.isEdit,
        min: 0
      }
    },
    {
      label: '确收总金额(元)',
      prop: 'confirmReceipt',
      component: () => <MoneyInput></MoneyInput>,
      hidden: !['info', 'audit'].includes(params.type),
      props: {
        isEdit: false,
        moneyCapitalShow: true
      }
    },
    {
      label: '调整额度(元)',
      prop: 'confirmAdjustAmount',
      hidden: !['info', 'audit'].includes(params.type),
      component: () => <MoneyInput></MoneyInput>,
      props: {
        isEdit: false,
        moneyCapitalShow: true
      }
    },
    {
      label: '调整后确收总金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'sufConfirmAmount',
      hidden: !['info', 'audit'].includes(params.type),
      props: {
        placeholder: '请输入调整后金额',
        moneyCapitalShow: true,
        isEdit: params.isEdit,
        min: 0
      }
    },
    {
      label: '收款总金额(元)',
      prop: 'accumulatReceived',
      component: () => <MoneyInput></MoneyInput>,
      hidden: !['info', 'audit'].includes(params.type),
      props: {
        isEdit: false,
        moneyCapitalShow: true
      }
    },
    {
      label: '调整额度(元)',
      prop: 'collectionAdjustAmount',
      hidden: !['info', 'audit'].includes(params.type),
      component: () => <MoneyInput></MoneyInput>,
      props: {
        isEdit: false,
        moneyCapitalShow: true
      }
    },
    {
      label: '调整后收款总金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'sufCollectionAmount',
      hidden: !['info', 'audit'].includes(params.type),
      props: {
        placeholder: '请输入调整后金额',
        moneyCapitalShow: true,
        isEdit: params.isEdit,
        min: 0
      }
    },
    {
      label: '已开票总金额(元)',
      prop: 'invoiceAmount',
      component: () => <MoneyInput></MoneyInput>,
      hidden: !['info', 'audit'].includes(params.type),
      props: {
        isEdit: false,
        moneyCapitalShow: true
      }
    },
    {
      label: '调整额度(元)',
      prop: 'invoiceAdjustAmount',
      hidden: !['info', 'audit'].includes(params.type),
      component: () => <MoneyInput></MoneyInput>,
      props: {
        isEdit: false,
        moneyCapitalShow: true
      }
    },
    {
      label: '调整后已开票总金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'sufInvoiceAmount',
      hidden: !['info', 'audit'].includes(params.type),
      props: {
        placeholder: '请输入调整后金额',
        moneyCapitalShow: true,
        isEdit: params.isEdit,
        min: 0
      }
    }
  ];
};

export const getCDataFields = () => {
  return {
    dicServiceSourceCode: '',
    dicServiceContentCode: '',
    isOpenClause: '',
    dicSignTypeCode: '',
    dicConfirmModeCode: '',
    includeTaxAmount: '',
    actualStartTime: '',
    actualEndTime: '',
    taxRate: '',
    taxAmount: '',
    excludeTaxAmount: '',
    performanceProp: '',
    confirmReceipt: '',
    unconfirmReceipt: '',
    accumulatReceived: '',
    surplusReceived: '',
    dicIncomeCategoryCode: '',
    dicIncomeCategoryName: '',
    dicIncomeSubclassCode: '',
    dicIncomeSubclassName: '',
    taxRateModifiable: true,
    remark: '',
    uuId: $utils.guid()
  };
};

export const gerScDataFields = () => {
  return {
    collectionSn: '',
    collectionCondition: '',
    collectionAmount: '',
    expectedCollectionTime: '',
    dicCollectionStatusName: '',
    dicCollectionStatusCode: '',
    actualCollectionAmount: '',
    uuId: $utils.guid(),
    isNew: true
  };
};

export const getBDataFields = () => {
  return {
    businessOppoSn: '',
    businessOppoName: '',
    businessOppoPerson: '',
    businessOppoSource: '',
    businessCreateTime: '',
    uuId: $utils.guid()
  };
};
