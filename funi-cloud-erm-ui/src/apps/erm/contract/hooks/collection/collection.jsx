/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 10:15:33
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-26 18:43:00
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/collection/collection.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';
import ermHooks from '@/apps/erm/hooks/index.js';
export const userCollectionColumns = ({ seeDateils, auditFunc, editFunc, delFunc, infoFuinc }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '合同名称',
      prop: 'contractName',
      width: 300,
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={seeDateils}
            auth={'ERM_CONTRACT_COLLECTION_INFO'}
            text={row.dataTitle}
          />
        );
      },
      fixed: 'left'
    },
    {
      label: '合同编号',
      prop: 'contractSn'
    },
    {
      label: '甲方',
      prop: 'partyA',
      width: 220
    },
    {
      label: '合同金额(元)',
      prop: 'contractAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.contractAmount);
      }
    },
    {
      label: '合同状态',
      prop: 'dicContractStatusName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '归属公司',
      prop: 'companyName',
      width: 220
    },
    {
      label: '流程节点',
      prop: 'businessNode'
    },

    {
      label: '归档编号',
      prop: 'archiveSn',
    },
    {
      label: '项目名称',
      prop: 'projectName',
      width: 220
    },
    {
      label: '负责人',
      prop: 'employeeName'
    },
    {
      label: '在办业务',
      prop: 'dicCollContractDoingBusName'
    },
    {
      label: '变更类型',
      prop: 'dicChangeTypeName'
    },

    {
      label: '合同签订时间',
      prop: 'signDate'
    },
    {
      label: '生效时间',
      prop: 'takeEffectTime'
    },
    {
      label: '是否框架合同',
      prop: ''
    },
    {
      label: '是否格式合同',
      prop: 'isFormat',
      render: ({ row, index }) => {
        return row.isFormat === true ? '是' : row.isFormat === false ? '否' : '--';
      }
    },

    {
      label: '乙方',
      prop: 'partyB',
      width: 220
    },
    {
      label: '合同开始时间',
      prop: 'startDate'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        let operationBtn = {
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={editFunc}
              auth={'ERM_CONTRACT_COLLECTION_EDIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          DEL: (
            <el-popconfirm
              title="确定删除当前收款合同？"
              width="220"
              onConfirm={() => {
                delFunc(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_CONTRACT_COLLECTION_DEL'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={auditFunc}
              auth={'ERM_CONTRACT_COLLECTION_AUDIT'}
              text={'审核'}
            ></Hyperlink>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={infoFuinc}
              auth={'ERM_CONTRACT_COLLECTION_INFO'}
              text={'业务详情'}
            ></Hyperlink>
          )
        };
        let btnList = [];
        btnList = $utils.clone(row.buttonList, true);
        if (!$utils.isNil(row.dicCollContractDoingBusCode) && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO');
        }
        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

/**
 * @description 按钮配置
 * @param {function} addFunc
 * @param {function} editFun
 * @param {function} delFun
 * **/
export const useBtnsConfig = ({
  addFunc = () => { },
  changeFunc = () => { },
  exportFun = () => { },
  endFunc = () => { },
  cancelFunc = () => { },
  changeEmployee = () => { },
  changeContRelatedParties = () => { },
  hasValid = false,
  selected = false
}) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACT_COLLECTION_ADD" onClick={addFunc} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button disabled={!hasValid} v-auth="ERM_CONTRACT_COLLECTION_CHANGE" onClick={changeFunc} type="primary">
          变更
        </el-button>
      )
    },

    {
      component: () => (
        <el-button disabled={!hasValid} v-auth="ERM_CONTRACT_COLLECTION_STOP" onClick={endFunc} type="primary">
          终止
        </el-button>
      )
    },
    {
      component: () => (
        <el-button disabled={!hasValid} v-auth="ERM_CONTRACT_COLLECTION_ABANDON" onClick={cancelFunc} type="primary">
          作废
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACT_COLLECTION_EXPORT" onClick={exportFun} type="primary">
          导出
        </el-button>
      )
    },
    {
      component: () => (
        <el-button
          disabled={!selected}
          v-auth="ERM_CONTRACT_COLLECTION_UPDATEEMP"
          onClick={changeEmployee}
          type="primary"
        >
          调整负责人
        </el-button>
      )
    },
    {
      component: () => (
        <el-button
          disabled={!selected}
          v-auth="ERM_CONT_COLLECT_UPDATERELATED"
          onClick={changeContRelatedParties}
          type="primary"
        >
          调整合同相关方
        </el-button>
      )
    }
  ];
};
