import { validate<PERSON><PERSON>, ermGlobal<PERSON><PERSON>, dicCode, negative_validate<PERSON><PERSON> } from '@/apps/erm/config/config.jsx';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import ErmTreeSelect from '@/apps/erm/component/ermTree/index.vue';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import LabelIndex from '@/apps/erm/component/ermSelect/labelIndex.vue';
import Vrouter from '@/router';
// import Vroute from '@/route';
import Verifics from '@/apps/erm/component/verifica/index.vue';
import ermHooks from '@/apps/erm/hooks/index.js';
import MoneyInput from '@/apps/erm/component/inputMoneyNumber/index.vue';
import { number } from 'echarts';

const router = Vrouter;

export const useFormSchema = params => {
  let isCancel = ['cancel_add', 'cancel_edit'].includes(params.type)
  return [
    {
      label: '开票申请编号',
      component: null,
      prop: 'applySn'
    },
    {
      label: '状态',
      component: null,
      prop: 'dicFapiaoAppStatusName'
    },
    {
      label: '创建时间',
      component: null,
      prop: 'createTime',
      hidden: params.isEdit
    },
    {
      label: '生效时间',
      component: null,
      prop: 'takeEffectTime',
      hidden: params.isEdit
    },
    {
      label: '收款合同名称',
      component: params.isEdit
        ? () => (
          <InfiniteSelect
            api={ermGlobalApi.queryValidCollectionContractList}
            defaultProps={{
              keyWord: 'contractNameOrSn',
              name: 'contractName',
              id: 'id',
              sn: 'contractSn',
              nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
            }}
            maxWidth={500}
            onChange={(e, { contractSn }) => {
              params.getcontractInfo({ id: e, contractSn });
            }}
          ></InfiniteSelect>
        )
        : ({ formModel }) => (
          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'ermContractCollectionInfo',
                query: {
                  title: formModel.contractObj?.title,
                  bizName: '详情',
                  type: 'info',
                  id: formModel.contractObj?.id,
                  tab: ['收款合同', formModel.contractObj?.title, , '详情'].join('-')
                }
              });
            }}
          >
            {formModel.contractObj?.name}
          </el-button>
        ),
      prop: 'contractObj'
    },
    // {
    //   label: '收款合同编号',
    //   component: null,
    //   prop: 'contractSn'
    // },
    // {
    //   label: '服务内容',
    //   component: params.isEdit ? () => {
    //     return <el-select
    //       style="width:100%"
    //       onChange={(e) => {
    //         params?.serviceChange(e)
    //       }}
    //     >
    //       {params?.serviceList.map(item => (
    //         <el-option key={item.id} label={`${item.dicServiceSourceName}-${item.dicServiceContentName}（${item.includeTaxAmount || '--'}元）`} value={item.id} />
    //       ))}
    //     </el-select>
    //   } : null,
    //   prop: params.isEdit ? 'serviceId' : 'serviceName'
    // },
    // {
    //   label: '收入确认表',
    //   component: ({ formModel }) => {
    //     return params.isEdit ? <el-select
    //       style="width:100%"
    //     >
    //       {params?.incomeContractList.map(item => (
    //         <el-option key={item.incomeSn} label={`${item.incomeSn}（${item.includeTaxAmount || '--'}元）`} value={item.incomeSn} />
    //       ))}
    //     </el-select> : formModel?.incomeContractObj?.id ? <el-button
    //       link
    //       type="primary"
    //       onClick={e => {
    //         router.push({
    //           name: 'ermContractIncomeInfo',
    //           query: {
    //             title: formModel?.incomeContractObj?.title || '收入确认表',
    //             bizName: '详情',
    //             type: 'info',
    //             id: formModel?.incomeContractObj?.id,
    //             tab: ['收入确认表', formModel?.incomeContractObj?.title, '详情'].join('-')
    //           }
    //         });
    //       }}
    //     >
    //       {formModel?.incomeContractObj?.name}
    //     </el-button> : <span>--</span>
    //   },
    //   prop: params.isEdit ? 'incomeContractSn' : 'incomeContractObj',
    //   props: {
    //     placeholder: '请收入确认表'
    //   },
    //   hidden: ({ formModel }) => {
    //     let id = formModel?.serviceId
    //     let obj = void 0
    //     if (id) {
    //       obj = params?.serviceList.find(item => item.id == id)
    //     }
    //     return !obj?.isOpenClause
    //   }
    // },
    {
      label: '归属公司',
      component: () => <LabelIndex options={params.companyList}></LabelIndex>,
      prop: 'companyId'
    },
    {
      label: '归属部门',
      component: () => <LabelIndex options={params.deptList}></LabelIndex>,
      prop: 'deptId'
    },
    {
      label: '服务项目',
      component: ({ formModel }) => (
        <el-button
          link
          type="primary"
          onClick={e => {
            router.push({
              name: 'erm_projectManage_bulletin_two',
              query: {
                title: formModel.projectName,
                bizName: '详情',
                type: 'info',
                id: formModel.projectId,
                tab: ['项目管理', formModel.projectName, '详情'].join('-')
              }
            });
          }}
        >
          {formModel.projectName}
        </el-button>
      ),
      prop: 'projectName'
    },
    {
      label: '负责人',
      component: params.isEdit
        ? () => (
          <InfiniteSelect
            api={ermGlobalApi.queryEmployeeList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'employeeName',
              id: 'id'
            }}
          ></InfiniteSelect>
        )
        : null,
      prop: params.isEdit ? 'employeeObj' : 'employeeName'
    },
    {
      label: '合同金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'contractAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      }
    },
    {
      label: '合同服务金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'serviceTotalAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      }
    },
    {
      label: '合同已开票金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'invoiceAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      }
    },
    {
      label: '乙方',
      component: null,
      prop: 'partBName'
    },
    {
      label: '客户名称(甲方)',
      component: ({ formModel }) => (
        <el-button
          link
          type="primary"
          onClick={e => {
            router.push({
              name: 'erm_supplier_two',
              query: {
                bizName: '详情',
                title: formModel.partAName,
                type: 'info',
                id: formModel.firstCustomerId,
                tab: ['客商管理', formModel.partAName, '详情'].join('-')
              }
            });
          }}
        >
          {formModel.partAName}
        </el-button>
      ),
      prop: 'partAName'
    },
    {
      label: '发票抬头',
      component: ({ formModel }) => {
        let list = params?.customerKaipiaoObjs ? $utils.uniq(params?.customerKaipiaoObjs, 'taxIdNumber') : []
        if (list.length == 1 && params.isEdit && !formModel.invoiceTitle) {
          formModel.invoiceTitle = list[0].taxIdNumber
          params.invoiceTitleChange(list[0].taxIdNumber)
        }
        return params.isEdit ? (
          <div style="display:flex">
            <el-select v-model={formModel.invoiceTitle} style="width:100%" onChange={params.invoiceTitleChange}>
              {
                list.map(item => <el-option label={item.invoiceHeader} value={item.taxIdNumber} />)
              }
            </el-select>
            <el-button
              type="primary"
              link
              style="flex: 0 0 auto"
              onClick={
                params.toSupplier
              }
            >维护客商</el-button>
          </div>
        ) : (<span>{formModel.invoiceTitle || '--'}</span>)

      },
      prop: 'invoiceTitle'
    },
    {
      label: '纳税人识别号',
      component: null,
      prop: 'taxIdNumber'
    },
    {
      label: '开户银行账户名称',
      component: ({ formModel }) => {
        let list = formModel.invoiceTitle && params?.customerKaipiaoObjs ? params?.customerKaipiaoObjs.filter(item => (item.taxIdNumber == formModel.invoiceTitle) && (item.bankAccountNumber || item.bankAccountName)) : []
        if (list.length == 1 && params.isEdit) {
          formModel.bankAccountName = list[0].bankAccountNumber || list[0].bankAccountName
          params.bankAccountNameChange(list[0].bankAccountNumber || list[0].bankAccountName)
        }
        if (!list.length && params.isEdit) {
          params.bankAccountNameChange(void 0)
        }
        return params.isEdit ? (
          <el-select onChange={params.bankAccountNameChange}>
            {
              list.map(item => <el-option label={item.bankAccountName} value={item.bankAccountNumber || item.bankAccountName} />)
            }
          </el-select>
        ) : (<span>{formModel.bankAccountName || '--'}</span>)
      },
      prop: 'bankAccountName'
    },

    {
      label: '开户银行名称',
      component: null,
      prop: 'openBankName'
    },
    // {
    //     label: '开户账号名称',
    //     component: null,
    //     prop: 'projectName',

    // },
    {
      label: '开户银行账号 ',
      component: null,
      prop: 'openAccountNumber'
    },
    {
      label: '公司联系电话 ',
      component: params.isEdit ? 'el-input' : null,
      prop: 'customerTelephone',
      props: {
        placeholder: '请输入公司联系电话'
      }
    },
    {
      label: '公司地址 ',
      component: null,
      prop: 'companyAddress'
    },
    {
      label: '本次开票金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'fapiaoAmount',
      props: {
        placeholder: '请输入本次开票金额',
        moneyCapitalShow: true,
        isEdit: isCancel || params.isEdit
      },
      on: {
        input: () => {
          params.setTaxAmount();
        }
      }
    },
    {
      label: '税率',
      component: isCancel || params.isEdit ? 'el-input' : null,
      prop: 'taxRate',
      props: {
        placeholder: '请输入税率'
      },
      on: {
        input: () => {
          params.setTaxAmount();
        }
      }
    },
    {
      label: '税额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'taxAmount',
      props: {
        placeholder: '请输入税额',
        moneyCapitalShow: true,
        isEdit: isCancel || params.isEdit
      }
    },
    {
      label: '开票类型 ',
      component: isCancel || params.isEdit ? () => <ErmSelect code={dicCode.fapiao_type} onChange={params.setDicFapiaoTypeCode}></ErmSelect> : null,
      prop: isCancel || params.isEdit ? 'dicFapiaoTypeCode' : 'dicFapiaoTypeName',
      props: {
        placeholder: '请选择开票类型'
      }
    },
    ...isCancel || params.dicBusinessTypeCode == 'ERM_ISSUE_FAPIAO_APPLY_REFUND' ? [{
      label: '原开票申请编号',
      component: ({ formModel }) => (
        <el-button
          link
          type="primary"
          onClick={async e => {

            router.push({
              name: 'ermContractInvoiceInfo',
              query: {
                bizName: '详情',
                title: formModel.partAName,
                type: 'info',
                sn: formModel.oldApplySn,
                tab: ['开票申请', formModel.contractObj?.title, '详情'].join('-')
              }
            });
          }}
        >
          {formModel.oldApplySn}
        </el-button>
      ),
      prop: 'oldApplySn',

    }, {
      label: '原开票申请金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'oldFapiaoAmount',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      },
    }] : [],
    {
      label: '领取人',
      component:
        ({ formModel }) => (
          params.type === 'audit' && params.dicFapiaoAppDoingBusCode == '1' ? <InfiniteSelect
            api={ermGlobalApi.queryEmployeeList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'employeeName',
              id: 'id'
            }}
          ></InfiniteSelect> : <span>{formModel?.receivePersonId?.name || '--'}</span>
        )
      ,
      prop: 'receivePersonId',
      props: {
        placeholder: '请选择领取人'
      },
      hidden: ({ formModel }) => {
        return !['audit', 'info'].includes(params.type)
      }
    },
    {
      label: '开票内容',
      component: isCancel || params.isEdit ? 'el-input' : null,
      prop: 'fapiaoContent',
      colProps: {
        span: 24
      },
      props: {
        autosize: { minRows: 2, maxRows: 4 },
        type: 'textarea',
        maxlength: 200,
        'show-word-limit': true
      }
    },
    {
      label: '备注',
      component: isCancel || params.isEdit ? 'el-input' : ({ formModel }) => {
        return (
          <span
            style={{
              'white-space': 'pre-wrap'
            }}>
            {formModel.remark || '--'}
          </span>
        )

      },
      prop: 'remark',
      colProps: {
        span: 24
      },
      props: {
        autosize: { minRows: 2, maxRows: 4 },
        type: 'textarea',
        maxlength: 200,
        'show-word-limit': true
      }
    }
  ];
};

export const useRule = (isEdit, type, fapiaoAmountSum, dicFapiaoTypeCode) => {
  let isCancel = ['cancel_add', 'cancel_edit'].includes(type)
  let a = isEdit ? 'contractObj' : 'contractName';
  let b = isEdit ? 'employeeObj' : 'employeeName';
  let c = isCancel || isEdit ? 'dicFapiaoTypeCode' : 'dicFapiaoTypeName';
  let d = isEdit ? 'serviceId' : 'serviceName';
  let e = type == 'audit' ? 'receivePersonId' : 'receivePersonName'
  let reg = isCancel ? /^-(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/ : /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
  let message = isCancel ? '请输入小于等于0保留两位小数的数字' : '请输入大于0且最多保留两位小数的数字'

  let bank = !['1', '2'].includes(dicFapiaoTypeCode) ? {
    openBankName: [{ required: true, message: '必填', trigger: 'change' }],
    openAccountNumber: [{ required: true, message: '必填', trigger: 'change' }],
    bankAccountName: [{ required: true, message: '必填', trigger: 'change' }],
  } : {}
  return {
    [a]: [{ required: true, message: '必填', trigger: 'change' }],
    [b]: [{ required: true, message: '必填', trigger: 'change' }],
    fapiaoAmount: [{
      required: true, validator: (rule, value, callback) => {
        try {
          value = Number(value)
        } catch { }
        // console.log(value)
        if (value === '' || value === null || value === undefined) {
          callback(new Error('必填'));
        } else if (value == 0) {
          isCancel ? callback() : callback(new Error(message));
        } else if (!reg.test(value)) {
          callback(new Error(message));
        } else if (value) {
          if (fapiaoAmountSum !== value) {
            callback(new Error('服务内容的开票金额不等于当前开票金额'));
          }
          callback();
        } else {
          callback();
        }
      }, trigger: 'change'
    }],
    taxRate: [{ required: true, validator: validateMoney, trigger: 'change' }],
    taxAmount: [{ required: true, validator: !isCancel ? validateMoney : negative_validateMoney, trigger: 'change' }],
    fapiaoContent: [{ required: true, message: '必填', trigger: 'change' }],
    invoiceTitle: [{ required: true, message: '必填', trigger: 'change' }],
    taxIdNumber: [{ required: true, message: '必填', trigger: 'change' }],

    [c]: [{ required: true, message: '必填', trigger: 'change' }],
    [d]: [{ required: true, message: '必填', trigger: 'change' }],
    [e]: [{
      required: true, validator: (rule, value, callback) => {
        if (value === '' || value === null || value === undefined || !value.id) {
          callback(new Error('必填'));
        } else {
          callback();
        }
      }, trigger: 'change'
    }],
    ...bank
  };
};




export const serveColumns = ({
  type,
  dicFapiaoAppDoingBusCode,
  iptChange = () => { },
  contactRef = {},
  delFPFunc = () => { }
}) => {

  const reset_v = (key, el) => {

    contactRef[key] = el;
  };

  return [
    {
      label: '开票时间',
      prop: 'invoicingTime',
      slots: {
        header: 'a'
      },
      render: ({ row, index }) => {
        let c = (
          <el-date-picker
            style="width:100%"
            placeholder="开票时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        );
        return type == 'audit' && dicFapiaoAppDoingBusCode == '1' ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_invoicingTime`, el);
            }}
            value={row.invoicingTime}
            c={c}
            key={`${row.uuId}_invoicingTime`}
            rule={[{ required: true, message: '必填' }]}
            onChange={e => {
              iptChange(index, 'invoicingTime', e);
            }}
          ></Verifics>
        ) : (
          <span>{row.invoicingTime || '--'}</span>
        );
      }
    },
    {
      label: '发票号码',
      prop: 'fapiaoSn',
      slots: {
        header: 'b'
      },
      render: ({ row, index }) => {
        let c = (
          <el-input placeholder="发票号码" onInput={e => {
            iptChange(index, 'fapiaoSn', e);
          }}></el-input>
        );
        return type == 'audit' && dicFapiaoAppDoingBusCode == '1' ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_fapiaoSn`, el);
            }}
            value={row.fapiaoSn}
            c={c}
            key={`${row.uuId}_fapiaoSn`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.fapiaoSn || '--'}</span>
        );
      }
    },
    {
      label: '发票代码',
      prop: 'fapiaoNo',
      slots: {
        header: 'c'
      },
      render: ({ row, index }) => {
        let c = (
          <el-input placeholder="发票代码" onInput={e => {
            iptChange(index, 'fapiaoNo', e);
          }}></el-input>
        );
        return type == 'audit' && dicFapiaoAppDoingBusCode == '1' ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_fapiaoNo`, el);
            }}
            value={row.fapiaoNo}
            c={c}
            key={`${row.uuId}_fapiaoNo`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.fapiaoNo || '--'}</span>
        );
      }
    },
    ...(type == 'audit' && dicFapiaoAppDoingBusCode == '1'
      ? [
        {
          label: '操作',
          prop: '',
          align: 'center',
          fixed: 'right',
          cellInteraction: false,
          render: ({ row, index }) => {
            return (
              <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                {
                  <el-button
                    type="primary"
                    onClick={() => {
                      delFPFunc(index);
                    }}
                    link
                  >
                    删除
                  </el-button>
                }
              </div>
            );
          }
        }
      ]
      : [])

  ];
};


export const serveColumns_ = ({
  isEdit, iptChange = () => { }, contactRef = {}, delFunc = () => { }, type = 'add', form, tableList
}) => {
  const reset_v = (key, el) => {
    contactRef[key] = el
  };
  let isCancel = ['cancel_add', 'cancel_edit'].includes(type)
  // 金额验证
  let validateMoney_table = (value, callback, row) => {

    let reg = isCancel ? /^-(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/ : /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
    let message = isCancel ? '请输入小于等于0保留两位小数的数字' : '请输入大于0且最多保留两位小数的数字'
    const oldFapiaoAmount = Number(row?.oldFapiaoAmount) || 0
    if (value === '' || value === null || value === void 0) {
      callback('必填');
    } else if (value == 0) {
      isCancel ? callback() : callback(message);
    } else if (!reg.test(value)) {
      callback(message);
    } else if (isCancel && Math.abs(value) > oldFapiaoAmount) {
      callback(`退票金额超出原开票金额(${ermHooks.erm_intl(oldFapiaoAmount)})元`);
    } else {
      callback();
    }
  };
  return [
    {
      label: '本次开票金额(元)',
      prop: 'fapiaoAmount',
      slots: {
        header: 'a'
      },
      align: isCancel || isEdit ? 'left' : 'right',
      width: isCancel || isEdit ? 240 : 150,
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            onChange={e => {
              iptChange(index, 'fapiaoAmount', e);
            }}
            maxlength={50}
            placeholder="本次收款金额(元)"
          />
        );
        return isCancel || isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_fapiaoAmount`, el);
            }}
            value={row.fapiaoAmount}
            c={c}
            key={`${row.uuId}_fapiaoAmount`}
            rule={[
              {
                validator: (value, callback) => { validateMoney_table(value, callback, row) }
              }
            ]}
          ></Verifics>
        ) : (
          <span>{ermHooks.erm_intl(row.fapiaoAmount)}</span>
        );
      }
    },
    {
      label: '收入确认表',
      prop: 'incomeContractSn',
      slots: {
        header: 'b'
      },
      align: 'left',
      width: 240,
      render: ({ row, index }) => {
        let c = (
          <el-select
            style="width:100%"
            onChange={e => {
              let v = row.incomeContractList.filter(item => item.incomeSn === e);
              iptChange(index, 'incomeContractSn', e);
              iptChange(index, 'incomeContractId', e ? v[0].incomeContractId : '');
            }}
            placeholder="收入确认表"
          >
            {row.incomeContractList.map(item => (
              <el-option key={item.incomeSn} label={`${item.incomeSn}（${item.includeTaxAmount || '--'}元）`} value={item.incomeSn} />
            ))}
          </el-select>
        );
        return isEdit && row.isOpenClause === true ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_incomeContractSn`, el);
            }}
            value={row.incomeContractSn}
            c={c}
            key={`${row.uuId}_incomeContractSn`}
            rule={[
              { required: true, message: '必填' },
            ]
            }
          ></Verifics >
        ) : (
          <span>{row.incomeContractSn ? `${row.incomeContractSn}${row?.incomeConfirmInfoVo?.includeTaxAmount ? '(' + row.incomeConfirmInfoVo.includeTaxAmount + ')元' : ''}` : '--'}</span>
        );
      }
    },
    {
      label: '来源',
      prop: 'dicServiceSourceName'
    },
    {
      label: '服务内容',
      prop: 'dicServiceContentName'
    },
    {
      label: '是否开放性条款',
      prop: 'isOpenClause',
      render: ({ row, index }) => <span>{row.isOpenClause === true ? '是' : '否'}</span>
    },
    {
      label: '确收方式',
      prop: 'dicConfirmModeName'
    },
    {
      label: '含税金额(元)',
      prop: 'includeTaxAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.includeTaxAmount)
    },
    {
      label: '税率',
      prop: 'taxRate',
      align: 'right'
    },
    {
      label: '实际开始时间',
      prop: 'actualStartTime'
    },
    {
      label: '实际结束时间',
      prop: 'actualEndTime'
    },
    {
      label: '税额(元)',
      prop: 'taxAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.taxAmount)
    },
    {
      label: '不含税金额(元)',
      prop: 'excludeTaxAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.excludeTaxAmount)
    },
    {
      label: '绩效比例(%)',
      prop: 'performanceProp',
      align: 'right'
    },
    {
      label: '累计已收(元)',
      prop: 'accumulatReceived',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.accumulatReceived)
    },
    {
      label: '剩余未收(元)',
      prop: 'surplusReceived',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.surplusReceived)
    },
    {
      label: '累计已开票(元)',
      prop: 'invoiceAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.invoiceAmount)
    },
    {
      label: '剩余未开票(元)',
      prop: 'unInvoiceAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.unInvoiceAmount)
    },
    {
      label: '收入大类',
      prop: 'dicIncomeCategoryName'
    },
    {
      label: '收入小类',
      prop: 'dicIncomeSubclassName'
    },
    {
      label: '备注',
      prop: 'remark'
    },
    ...(isEdit
      ? [
        {
          label: '操作',
          prop: '',
          align: 'center',
          fixed: 'right',
          cellInteraction: false,
          render: ({ row, index }) => {
            return (
              <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                {
                  <el-button
                    type="primary"
                    onClick={() => {

                      delFunc(index);
                    }}
                    link
                  >
                    删除
                  </el-button>
                }
              </div>
            );
          }
        }
      ]
      : [])
  ]
}




export const noConcractColumns = ({ delFunc, iptChange = () => { }, contactRef = {}, isEdit = true, type = '' }) => {
  const reset_v = (key, el) => {
    contactRef[key] = el
  };
  return [
    {
      label: '序号',
      prop: 'index',
      render: ({ index }) => {
        return index + 1;
      }
    },
    {
      label: '客户名称(甲方)',
      prop: isEdit ? 'firstCustomerObj' : "firstCustomerName",
      slots: {
        header: 'a'
      },
      width: 300,
      render: isEdit ? ({ row, index }) => {
        let c = (
          <InfiniteSelect
            api={ermGlobalApi.queryCustomerList}
            defaultProps={{
              keyWord: 'nameOrSuCreditCode',
              name: 'customerName',
              id: 'id',
              sn: 'suCreditCode',
              nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
            }}
            otherParams={{}}
            onChange={(e, obj) => {
              iptChange(index, 'firstCustomerObj', obj);
            }}
          ></InfiniteSelect>
        );
        return (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_firstCustomerObj`, el);
            }}
            value={row.firstCustomerObj}
            c={c}
            key={`${row.uuId}_firstCustomerObj`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        );
      } : void 0
    },
    {
      label: '开票金额(元)',
      prop: 'fapiaoAmount',
      slots: {
        header: 'b'
      },
      width: 180,
      align: 'right',
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            controls={false}
            onChange={e => {
              iptChange(index, 'fapiaoAmount', e);
            }}
            maxlength={50}
            placeholder="开票金额"
          />
        );
        return isEdit || ['cancel_add', 'cancel_edit'].includes(type) ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_fapiaoAmount`, el);
            }}
            value={row.fapiaoAmount}
            c={c}
            key={`${row.uuId}_fapiaoAmount`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : ermHooks.erm_intl(row.fapiaoAmount);
      }
    },
    {
      label: '发票抬头',
      prop: isEdit ? 'invoiceTitleObj' : 'invoiceTitle',
      slots: {
        header: 'c'
      },
      width: 180,
      render: isEdit ? ({ row, index }) => {
        let list = row?.customerKaipiaoObjs ? $utils.uniq($utils.clone(row?.customerKaipiaoObjs, true) || [], 'taxIdNumber') : []
        let c = <el-select style="width:100%" onChange={(e) => {
          iptChange(index, 'invoiceTitleObj', e);
        }}>
          {
            list.map(item => <el-option label={item.invoiceHeader} value={item.taxIdNumber} />)
          }
        </el-select>;
        return (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_invoiceTitleObj`, el);
            }}
            value={row.invoiceTitleObj}
            c={c}
            key={`${row.uuId}_invoiceTitleObj`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        );
      } : void 0
    },
    {
      label: '本次开票金额(元)',
      prop: 'currentFapiaoAmount',
      slots: {
        header: 'd'
      },
      align: 'right',
      width: 180,
      render: ({ row }) => <span>{ermHooks.erm_intl(row.currentFapiaoAmount)}</span>
    },
    {
      label: '开票类型',
      prop: isEdit || ['cancel_add', 'cancel_edit'].includes(type) ? 'dicFapiaoTypeCode' : 'dicFapiaoTypeName',
      slots: {
        header: 'e'
      },
      width: 180,
      render: isEdit || ['cancel_add', 'cancel_edit'].includes(type) ? ({ row, index }) => {
        let c = <ErmSelect style="width:100%" code={dicCode.fapiao_type} onChange={(e) => {
          iptChange(index, 'dicFapiaoTypeCode', e);
        }}></ErmSelect>;
        return (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_dicFapiaoTypeCode`, el);
            }}
            value={row.dicFapiaoTypeCode}
            c={c}
            key={`${row.uuId}_dicFapiaoTypeCode`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        );
      } : void 0
    },
    {
      label: '纳税人识别号',
      prop: 'taxIdNumber',
      width: 180,
    },
    {
      label: '开户银行名称',
      prop: 'openBankName',
      width: 180,

    },
    {
      label: '开户银行账户名称',
      prop: 'openAccountName',
      width: 180,
      render: isEdit ? ({ row, index }) => {
        let list = row?.customerKaipiaoObjs ? row?.customerKaipiaoObjs.filter(item => item.taxIdNumber === row.taxIdNumber) : []

        return (
          <el-select modelValue={row.openAccountNumber} style="width:100%" onChange={(e) => {
            iptChange(index, 'openAccountNumber', e);
          }}>
            {
              list.map(item => <el-option label={item.bankAccountName} value={item.bankAccountNumber ? item.bankAccountNumber : item.bankAccountName} />)
            }

          </el-select>
        );
      } : void 0
    },
    {
      label: '开户银行账户',
      prop: 'openAccountNumber',
      width: 180,
    },
    {
      label: '税额(元)',
      prop: 'taxAmount',
      width: 180,
      align: 'right',
      render: ({ row, index }) => {
        let { fapiaoAmount, taxRate } = row;
        if (taxRate && fapiaoAmount) {
          let a = fapiaoAmount - ((Number(fapiaoAmount) / (1 + Number(taxRate))).toFixed(2));
          row.taxAmount = a;
          return ermHooks.erm_intl(a);
        }
        row.taxAmount = '';
      }

    },
    {
      label: '税率',
      prop: 'taxRate',
      align: 'right',
      width: 180,
    },
    {
      label: '备注',
      prop: 'remark',
      render: isEdit || ['cancel_add', 'cancel_edit'].includes(type) ? ({ row, index }) => {
        return <el-input v-model={row.remark}></el-input>
      } : void 0,
      width: 180,
    },
    ...isEdit && ['add'].includes(type) ? [{
      label: '操作',
      prop: '',
      align: 'center',
      fixed: 'right',
      cellInteraction: false,
      render: ({ row, index }) => {
        return (
          <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
            {
              <el-button
                type="primary"
                onClick={() => {
                  delFunc(index);
                }}
                link
              >
                删除
              </el-button>
            }
          </div>
        );
      }
    }] : []
  ]
}