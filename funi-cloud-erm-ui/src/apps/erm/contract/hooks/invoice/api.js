/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-28 19:36:48
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-06 16:30:27
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/invoice/api.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */

export const apiUrl = {
  customerInfo: '/erm/customer/info',
  issueFapiaoApplyNew: '/erm/issueFapiaoApply/new',
  queryIssueFapiaoApplyList: '/erm/issueFapiaoApplyList/queryIssueFapiaoApplyList',
  issueFapiaoApply: '/erm/issueFapiaoApply/info',
  issueListExport: '/erm/issueFapiaoApplyList/queryIssueFapiaoApplyListExport',
  creatBus: '/erm/issueFapiaoApply/creatBus',
  queryActivityConfigInfo: '/erm/issueFapiaoApply/queryActivityConfigInfo',
  getNoContractFapiaoRequestList: '/erm/issueFapiaoApply/getNoContractFapiaoRequestList',
  getContractListForNoContractFapiao: '/erm/collectionContractList/getContractListForNoContractFapiao',
  deleteIssueFapiaoApplyByIds: '/erm/issueFapiaoApply/deleteIssueFapiaoApplyByIds',
  queryCustomerListNoPage: '/erm/customer/queryCustomerListNoPage'
};

// export const queryIncomeConfirmInfoListHttp = (params) => {
//     return $http.post(apiUrl.queryIncomeConfirmInfoList, params)
// }
export const issueFapiaoApplyNewHttp = params => {
  return $http.post(apiUrl.issueFapiaoApplyNew, params);
};
// export const incomeConfirmInfoHttp = (params) => {
//     return $http.fetch(apiUrl.incomeConfirmInfo, params)
// }

export const customerInfoHttp = params => {
  return $http.fetch(apiUrl.customerInfo, params);
};

export const queryIssueFapiaoApplyListHttp = params => {
  return $http.post(apiUrl.queryIssueFapiaoApplyList, params);
};

export const issueFapiaoApplyHttp = params => {
  return $http.fetch(apiUrl.issueFapiaoApply, params);
};
export const creatBusHttp = params => {
  return $http.post(apiUrl.creatBus, params);
};

export const queryActivityConfigInfoHttp = params => {
  return $http.fetch(apiUrl.queryActivityConfigInfo, params);
};

export const getNoContractFapiaoRequestListHttp = params => {
  return $http.post(apiUrl.getNoContractFapiaoRequestList, params);
};

export const getContractListForNoContractFapiaoHttp = params => {
  return $http.fetch(apiUrl.getContractListForNoContractFapiao, params);
};

export const deleteIssueFapiaoApplyByIdsHttp = params => {
  return $http.fetch(apiUrl.deleteIssueFapiaoApplyByIds, params);
};

export const queryCustomerListNoPageHttp = params => {
  return $http.fetch(apiUrl.queryCustomerListNoPage, params);
};
