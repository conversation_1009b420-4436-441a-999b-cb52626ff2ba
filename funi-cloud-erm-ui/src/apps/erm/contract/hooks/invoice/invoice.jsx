/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:54:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-30 11:26:38
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/invoice/invoice.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import ermHooks from '@/apps/erm/hooks/index.js';
export const useInvoiceColumns = ({ seeDateils, editFunc = () => { }, delFunc = () => { }, auditFunc = () => { }, infoFuinc }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '合同名称',
      prop: 'contractName',
      width: 300,
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={seeDateils}
            auth={'ERM_CONTRACTINVOICE_INFO'}
            text={row.contractName}
          />
        );
      },
      fixed: 'left'
    },
    {
      label: '合同编号',
      prop: 'contractSn',
    },
    {
      label: '开票金额(元)',
      prop: 'fapiaoAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row.fapiaoAmount);
      }
    },

    {
      label: '开票时间',
      prop: '',
      render: ({ row, index }) => {
        if (!row.fapiaoInfoList || !row.fapiaoInfoList?.length) return ''
        return row?.fapiaoInfoList[0]?.invoicingTime
      }
    },

    {
      label: '税率',
      prop: 'taxRate',
    },
    {
      label: '发票抬头',
      prop: 'invoiceTitle',
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },
    {
      label: '(客户名称)甲方',
      prop: 'firstCustomerName'
    },
    {
      label: '开票申请编号',
      prop: 'applySn',

    },


    {
      label: '流程节点',
      prop: 'businessNode'
    },
    {
      label: '状态',
      prop: 'dicFapiaoAppStatusName'
    },
    {
      label: '原开票申请编号',
      prop: 'oldApplySn'
    },
    {
      label: '在办业务',
      prop: 'dicFapiaoAppDoingBusName'
    },

    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '服务项目',
      prop: 'projectName'
    },
    {
      label: '乙方',
      prop: 'secondCustomerName'
    },


    {
      label: '负责人',
      prop: 'employeeName'
    },
    {
      label: '生效时间',
      prop: 'takeEffectTime'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        let operationBtn = {
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={editFunc}
              auth={'ERM_CONTRACTINVOICE_DEIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          DEL: (
            <el-popconfirm
              title="确定删除当前开票申请？"
              width="220"
              onConfirm={() => {
                delFunc(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_CONTRACTINVOICE_DEL'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={auditFunc}
              auth={'ERM_CONTRACTINVOICE_AUDIT'}
              text={'审核'}
            ></Hyperlink>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={infoFuinc}
              auth={'ERM_CONTRACTINVOICE_INFO'}
              text={'业务详情'}
            ></Hyperlink>
          )
        };

        let btnList = []
        btnList = $utils.clone(row.buttonList, true)
        if (!$utils.isNil(row.dicFapiaoAppDoingBusCode) && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO')
        }
        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

export const useBtnsConfig = ({
  addFunc = () => { },
  cancelFunc = () => { },
  delFun = () => { },
  addFuncT = () => { },
  hasValid = false,
  exportFn = () => { },
  contractList = [],
  contractId = null
}) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACTINVOICE_ADD" onClick={addFunc} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (

        <el-popover placement="bottom" width={200} trigger="click">
          {{
            reference: () => <el-button v-auth="ERM_CONTRACTINVOICE_EM_ADD" type="primary">
              无合同开票
            </el-button>,
            default: () => <div>
              <el-radio-group modelValue={contractId.value} onChange={(e) => contractId.value = e}>
                {
                  contractList.map(item => (<el-radio label={item.contractId}>{item.contractName}</el-radio>))
                }
              </el-radio-group>

              <div style="text-align:center">
                <el-button disabled={!contractId.value} onClick={addFuncT} type="primary" size="small" >
                  确定
                </el-button>
              </div>
            </div>
          }}
        </el-popover>

      )
    },
    {
      component: () => (
        <el-button disabled={!hasValid} v-auth="ERM_CONTRACTINVOICE_RETURN" onClick={cancelFunc} type="primary">
          退票
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACTINVOICE_EXPORT" onClick={exportFn} type="primary">
          导出
        </el-button>
      )
    }
  ];
};
