/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-16 15:20:48
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-19 17:34:19
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/payment/api.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const apiUrl = {
  queryPaymentContractList: '/erm/paymentContractList/queryPaymentContractList',
  queryCustomerListByCurrentEmployee: '/erm/customerList/queryPartACustomerListByCurrentEmployee',
  queryCustomerList: '/erm/customerList/queryCustomerList',
  findCompanyByName: '/erm/company/findCompanyByName',
  queryProjectListByName: '/erm/projectManagementList/queryProjectListByName',
  paymentContractNew: '/erm/paymentContract/new',
  paymentContractInfo: '/erm/paymentContract/info',
  paymentContract: '/erm/paymentContract/creatBus',
  queryPaymentContractListExport: '/erm/paymentContractList/queryPaymentContractListExport',
  queryPartBCustomerListByCurrentEmployee: '/erm/customerList/queryPartBCustomerListByCurrentEmployee',
  // queryProjectListForContract:"/erm/projectManagementList/queryProjectListForContract",//
  queryProjectListForContract: '/erm/projectManagementList/queryValidProjectListByUserDeptInfo', //
  queryPartBCustomerListByName: '/erm/customerList/queryPartBCustomerListByName', //付款合同模糊匹配客商列表-乙方

  queryCollectionServiceList: '/erm/collectionServiceList/queryCollectionServiceList', //选了关联收款合同-查询服务内容
  queryPaymentValidSectionList: '/erm/payPurchaseSectionInfoList/queryPayPurchaseSectionInfoList',
  verifyWorkflowNode: 'erm/paymentContract/verifyWorkflowNode'
};

export const queryPaymentContractListHttp = params => {
  return $http.post(apiUrl.queryPaymentContractList, params);
};

export const queryCustomerListByCurrentEmployeeHttp = params => {
  return $http.post(apiUrl.queryCustomerListByCurrentEmployee, params);
};
export const findCompanyByNameHttp = params => {
  return $http.post(apiUrl.findCompanyByName, params);
};
export const paymentContractNewHttp = params => {
  return $http.post(apiUrl.paymentContractNew, params);
};
export const paymentContractInfoHttp = params => {
  return $http.fetch(apiUrl.paymentContractInfo, params);
};
export const paymentContractHttp = params => {
  return $http.post(apiUrl.paymentContract, params);
};
export const queryCollectionServiceListp = params => {
  return $http.post(apiUrl.queryCollectionServiceList, params);
};
export const queryPaymentValidSectionListHttp = params => {
  return $http.post(apiUrl.queryPaymentValidSectionList, params);
};

export const verifyWorkflowNodeHttp = params => {
  return $http.fetch(apiUrl.verifyWorkflowNode, params);
};
