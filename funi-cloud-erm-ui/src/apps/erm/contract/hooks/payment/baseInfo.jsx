/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-03 11:14:08
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-26 15:54:52
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/payment/baseInfo.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Verifics from '@/apps/erm/component/verifica/index.vue';
import { ermGlobalApi, dicCode } from '@/apps/erm/config/config.jsx';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import ErmTreeSelect from '@/apps/erm/component/ermTree/index.vue';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import LabelIndex from '@/apps/erm/component/ermSelect/labelIndex.vue';
import { apiUrl } from './api.js';
import Vrouter from '@/router';
import ermHooks from '@/apps/erm/hooks/index.js';
import MoneyInput from '@/apps/erm/component/inputMoneyNumber/index.vue';
import { collectionContractInfoHttp } from '@/apps/erm/contract/hooks/collection/api.js';
const datePickerProps = {
  style: { width: '100%' },
  format: 'YYYY-MM-DD',
  'value-format': 'YYYY-MM-DD'
};
const router = Vrouter;
export const useFormSchema = params => {
  return [
    {
      label: '合同号',
      component: null,
      prop: 'contractSn',
      copy: false
    },
    {
      label: '合同名称',
      component: params.isEdit ? 'el-input' : null,
      prop: 'contractName',
      props: {
        placeholder: '请输入合同名称'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'contractName')
    },
    {
      label: '归档编号',
      component: null,
      prop: 'archiveSn',
      copy: false,
      hidden: !['info', 'audit', 'end_add', 'cancel_add'].includes(params.type)
    },
    {
      label: '创建时间',
      component: null,
      prop: 'createTime',
      hidden: !['info', 'audit', 'end_add', 'cancel_add'].includes(params.type)
    },
    {
      label: '生效时间',
      component: null,
      prop: 'takeEffectTime',
      hidden: !['info', 'audit', 'end_add', 'cancel_add'].includes(params.type)
    },
    {
      label: '合同分类',
      component: params.isEdit ? () => <ErmSelect code={dicCode.contract_type}></ErmSelect> : null,
      prop: params.isEdit ? 'dicContractTypeCode' : 'dicContractTypeName',
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicContractTypeName')
    },
    {
      label: '合同签订时间',
      component:
        params.isEdit ||
        (['audit'].includes(params.type) &&
          ['ERM_PAYMENT_CONTRACT_ADD', 'ERM_PAYMENT_CONTRACT_CHANGE'].includes(params.dicBusinessTypeCode))
          ? 'el-date-picker'
          : null,
      prop: 'signDate',
      props: {
        placeholder: '请选择合同签订时间',
        ...datePickerProps
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'signDate')
    },
    {
      label: '合同金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'contractAmount',
      props: {
        placeholder: '请输入合同金额',
        moneyCapitalShow: true,
        isEdit: params.isEdit,
        min: 0
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'contractAmount', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '调整后合同金额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'afterAdjustContractAmount',
      props: {
        placeholder: '请输入合同金额',
        isEdit: ['end_add', 'end_edit'].includes(params.type),
        moneyCapitalShow: true
      },
      hidden: ({ formModel }) => {
        if (['end_add', 'end_edit'].includes(params.type)) {
          return false;
        }
        if (['audit', 'info'].includes(params.type) && formModel.afterAdjustContractAmount) {
          return false;
        }
        return true;
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'afterAdjustContractAmount', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '税额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'taxPayment',
      props: {
        placeholder: '请输入税额',
        moneyCapitalShow: true,
        isEdit: params.isEdit,
        min: 0
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'taxPayment', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '调整后税额(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'afterAdjustTaxAmount',
      props: {
        placeholder: '请输入合同金额',
        isEdit: ['end_add', 'end_edit'].includes(params.type),
        moneyCapitalShow: true
      },
      hidden: ({ formModel }) => {
        if (['end_add', 'end_edit'].includes(params.type)) {
          return false;
        }
        if (['audit', 'info'].includes(params.type) && formModel.afterAdjustTaxAmount) {
          return false;
        }
        return true;
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'afterAdjustTaxAmount', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '关联采购项目',
      component: params.isEdit
        ? () => (
            <InfiniteSelect
              api={ermGlobalApi.queryValidPurchaseList}
              defaultProps={{
                keyWord: 'purchaseName',
                name: 'purchaseName',
                id: 'id',
                sn: 'purchaseSn',
                nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
              }}
              maxWidth={500}
              onChange={(_, obj) => {
                params.purchaseProjectChange(obj);
              }}
            ></InfiniteSelect>
          )
        : ({ formModel }) => (
            <el-button
              link
              type="primary"
              onClick={e => {
                router.push({
                  name: 'ermPurchaseApprovalInfo',
                  query: {
                    title: formModel.purchaseProject?.title,
                    bizName: '详情',
                    type: 'info',
                    id: formModel.purchaseProject?.id,
                    operation: 'approval',
                    tab: ['采购立项', formModel.purchaseProject?.title, , '详情'].join('-')
                  }
                });
              }}
            >
              {formModel.purchaseProject?.name}
            </el-button>
          ),
      prop: 'purchaseProject'
    },
    {
      label: '甲方',
      component: ({ formModel }) =>
        params.isEdit ? (
          <el-select
            style="width:100%"
            onChange={e => {
              params.setPartA(e);
            }}
          >
            {params.customerListByCurrent.map(item => (
              <el-option key={item.id} label={item.name} value={item.id} />
            ))}
          </el-select>
        ) : (
          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'erm_supplier_two',
                query: {
                  title: formModel?.partA_object?.name || '客商管理',
                  bizName: '详情',
                  type: 'info',
                  id: formModel?.partA_object?.id,
                  tab: ['客商管理', formModel?.partA_object?.name, '详情'].join('-')
                }
              });
            }}
          >
            {formModel?.partA_object?.name}
          </el-button>
        ),
      prop: params.isEdit ? 'partA' : 'partA_object',
      props: {
        placeholder: '请选择甲方'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'partA_object', obj => {
          return <span class="funi-erm-change">{obj.name}</span>;
        })
    },
    {
      label: '乙方',
      component: ({ formModel }) =>
        params.isEdit ? (
          <InfiniteSelect
            api={apiUrl.queryPartBCustomerListByName}
            maxWidth={500}
            defaultProps={{
              keyWord: 'nameOrSuCreditCode',
              name: 'customerName',
              id: 'id',
              sn: 'suCreditCode',
              nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
            }}
          >
            {{
              default: () => (
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    router.push({
                      name: 'erm_supplier_add',
                      query: {
                        bizName: '新建',
                        type: 'add',
                        tab: '客商管理-新建'
                      }
                    });
                  }}
                >
                  新建客商
                </el-button>
              )
            }}
          </InfiniteSelect>
        ) : (
          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'erm_supplier_two',
                query: {
                  title: formModel?.partB?.name || '客商管理',
                  bizName: '详情',
                  type: 'info',
                  id: formModel?.partB?.id,
                  tab: ['客商管理', formModel?.partB?.name, '详情'].join('-')
                }
              });
            }}
          >
            {formModel?.partB?.name}
          </el-button>
        ),
      prop: 'partB',
      props: {
        placeholder: '请选择乙方'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'partB', obj => {
          return <span class="funi-erm-change">{obj.name}</span>;
        })
    },
    {
      label: '归属公司',
      component: params.isEdit ? () => <LabelIndex options={params.companyList}></LabelIndex> : null,
      prop: params.isEdit ? 'companyId' : 'companyName',
      props: {
        placeholder: '请选择归属公司'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'companyName')
    },
    {
      label: '归属部门',
      component: params.isEdit
        ? () => (
            <ErmTreeSelect
              style="width:100%"
              propsMap={{
                value: 'id',
                label: 'deptName',
                children: 'childrenDept'
              }}
              isDetp={true}
              init={params.type == 'add' ? true : false}
              params={{
                id: params?.getCompanyId()
              }}
              onChange={(e, n) => {
                params?.setDeptName(n);
              }}
            ></ErmTreeSelect>
          )
        : null,
      prop: params.isEdit ? 'deptId' : 'deptName',
      props: {
        placeholder: '请选择归属部门'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'deptName')
    },
    {
      label: '项目名称',
      component: ({ formModel }) =>
        params.isEdit && params.type.indexOf('change') < 0 ? (
          <InfiniteSelect
            api={apiUrl.queryProjectListForContract}
            defaultProps={{
              keyWord: 'projectName',
              name: 'projectName',
              id: 'id'
            }}
            defaultList={[
              {
                id: '-100',
                name: '无'
              }
            ]}
            onChange={(e, obj) => {
              params.setFieldVal('projectSn', obj.projectSn);
            }}
          ></InfiniteSelect>
        ) : (
          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'erm_projectManage_two',
                query: {
                  title: formModel.projectObj?.name,
                  bizName: '详情',
                  type: 'info',
                  id: formModel.projectObj.id,
                  tab: ['项目管理', formModel.projectObj?.name, '详情'].join('-')
                }
              });
            }}
          >
            {formModel.projectObj?.name}
          </el-button>
        ),

      prop: 'projectObj',
      props: {
        placeholder: '请选择项目名称'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'projectObj', obj => {
          return <span class="funi-erm-change">{obj.name}</span>;
        })
    },
    {
      label: '负责人',
      component: params.isEdit
        ? () => (
            <InfiniteSelect
              api={ermGlobalApi.queryEmployeeList}
              defaultProps={{
                keyWord: 'keyword',
                name: 'employeeName',
                id: 'id'
              }}
            ></InfiniteSelect>
          )
        : null,
      prop: params.isEdit ? 'employeeObj' : 'employeeName',
      props: {
        // [17414]bug修改
        placeholder: '请选择负责人'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'employeeName')
    },
    {
      label: '合同开始时间',
      component: params.isEdit ? 'el-date-picker' : null,
      prop: 'startDate',
      props: {
        placeholder: '请选择合同开始时间',
        ...datePickerProps,
        'disabled-date': params.disabledStartDate
      },
      on: {
        change: e => {
          params.setDate('startDate', e);
        }
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'startDate')
    },
    {
      label: '合同结束时间',
      component: params.isEdit ? 'el-date-picker' : null,
      prop: 'endDate',
      props: {
        placeholder: '请选择合同结束时间',
        ...datePickerProps,
        'disabled-date': params.disabledEndDate
      },
      on: {
        change: e => {
          params.setDate('endDate', e);
        }
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'endDate')
    },
    {
      label: '结款方式',
      component: params.isEdit ? () => <ErmSelect code={dicCode.settlement_type}></ErmSelect> : null,
      prop: params.isEdit ? 'dicSettlementTypeCode' : 'dicSettlementTypeName',
      props: {
        placeholder: '请选择结款方式'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicSettlementTypeName')
    },
    {
      label: '付款方式',
      component: params.isEdit ? () => <ErmSelect code={dicCode.pay_type}></ErmSelect> : null,
      prop: params.isEdit ? 'dicPayTypeCode' : 'dicPayTypeName',
      props: {
        placeholder: '请选择付款方式'
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicPayTypeName')
    },
    {
      label: '合同状态',
      component: null,
      prop: 'dicContractStatusName',
      copy: false
    },
    {
      label: '支付状态',
      component: null,
      prop: 'dicPayStatusName',
      copy: false
    },
    {
      label: '是否包含开放性条款',
      component: params.isEdit
        ? () => (
            <el-select
              style="width:100%"
              onChange={e => {
                params?.setIsIncludeOpenTerm(e);
              }}
            >
              <el-option key="1" label="是" value={true} />
              <el-option key="2" label="否" value={false} />
            </el-select>
          )
        : obj => {
            return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
          },
      prop: 'isIncludeOpenTerm',
      props: {
        // [17414]bug修改
        placeholder: '请选择是否包含开放性条款'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'isIncludeOpenTerm', bool => (
          <span class="funi-erm-change">{bool === true ? '是' : bool === false ? '否' : '--'}</span>
        ))
    },
    {
      label: '是否多方合同',
      component: params.isEdit
        ? () => (
            <el-select
              style="width:100%"
              onChange={e => {
                params?.setisMultipartyContract(e);
              }}
            >
              <el-option key="1" label="是" value={true} />
              <el-option key="2" label="否" value={false} />
            </el-select>
          )
        : obj => {
            return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
          },
      prop: 'isMultipartyContract',
      props: {
        // [17414]bug修改
        placeholder: '请选择是否多方合同'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'isMultipartyContract', bool => (
          <span class="funi-erm-change">{bool === true ? '是' : bool === false ? '否' : '--'}</span>
        ))
    },
    {
      label: '累计已付(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'totalPaid',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      },
      copy: false,
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'totalPaid', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '剩余未付(元)',
      component: () => <MoneyInput></MoneyInput>,
      prop: 'residuePaid',
      props: {
        moneyCapitalShow: true,
        isEdit: false
      },
      copy: false,
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'residuePaid', val => (
          <MoneyInput class="funi-erm-change" v-model={val} isEdit={false} moneyCapitalShow={false}></MoneyInput>
        ))
    },
    {
      label: '用印公司',
      component: params.isEdit ? () => <LabelIndex options={params.companyList}></LabelIndex> : null,
      prop: params.isEdit ? 'sealCompanyId' : 'sealCompanyName',
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'sealCompanyName')
    },
    ...(['end_add', 'end_edit'].includes(params.type) || params.dicBusinessTypeCode == 'ERM_PAYMENT_CONTRACT_TERMINATE'
      ? [
          {
            label: '是否用印',
            component:
              params.isEdit || ['end_add', 'end_edit'].includes(params.type)
                ? () => (
                    <el-select
                      style="width:100%"
                      onChange={e => {
                        params.isSealUsed.value = e;
                      }}
                    >
                      <el-option key="1" label="是" value={true} />
                      <el-option key="2" label="否" value={false} />
                    </el-select>
                  )
                : obj => {
                    return <div>{obj.value === true ? '是' : obj.value === false ? '否' : '--'}</div>;
                  },
            prop: 'isSealUsed'
          }
        ]
      : []),
    ...((['end_add', 'end_edit'].includes(params.type) ||
      params.dicBusinessTypeCode == 'ERM_PAYMENT_CONTRACT_TERMINATE') &&
    !params.isSealUsed.value
      ? []
      : [
          {
            label: '用印类型',
            component:
              params.isEdit || params.type.indexOf('end') > -1
                ? () => (
                    <el-checkbox-group>
                      {params?.sealTypeList.map(item => {
                        return <el-checkbox label={item.code}>{item.name}</el-checkbox>;
                      })}
                    </el-checkbox-group>
                  )
                : null,
            prop: params.isEdit || params.type.indexOf('end') > -1 ? 'dicSealTypeCode' : 'dicSealTypeName',
            colProps: {
              span: 24
            },
            extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicSealTypeName')
          },
          {
            label: '合同份数',
            component: params.isEdit ? 'el-input-number' : null,
            prop: 'contractCount',
            props: {
              controls: false,
              precision: 0,
              min: 0
            },
            extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'contractCount')
          }
        ]),

    // {
    //     label: '关联收款合同',
    //     component: params.isEdit ? 'el-input' : null,
    //     prop: params.isEdit ? 'collectionContractId' : 'collectionContractName',
    //     props: {
    //         placeholder: '请选择关联收款合同',
    //     }
    // },
    {
      label: '收款合同名称',
      component:
        params.isEdit && params.type.indexOf('change') < 0
          ? () => {
              console.log('params?.projectSn', params?.projectSn);
              return (
                <InfiniteSelect
                  api={ermGlobalApi.queryValidContractListToPayContract}
                  defaultProps={{
                    keyWord: 'contractNameOrSn',
                    name: 'contractName',
                    id: 'id',
                    sn: 'contractSn',
                    nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
                  }}
                  maxWidth={500}
                  otherParams={{
                    projectSn: params?.projectSn
                  }}
                  onChange={(e, obj) => {
                    params.setFieldVal('collectionContractSn', obj.contractSn);
                    params.setCollectionService(e);
                  }}
                ></InfiniteSelect>
              );
            }
          : ({ formModel }) =>
              formModel.contractObj?.name ? (
                <el-button
                  link
                  type="primary"
                  onClick={e => {
                    collectionContractInfoHttp({
                      collectionContractSn: formModel.contractObj?.collectionContractSn
                    }).then(res => {
                      router.push({
                        name: 'ermContractCollectionInfo',
                        query: {
                          title: formModel.contractObj?.name || '收款合同',
                          bizName: '详情',
                          type: 'info',
                          id: res.id,
                          tab: ['收款合同', formModel.contractObj?.name].join('-')
                        }
                      });
                    });
                  }}
                >
                  {formModel.contractObj?.name}
                </el-button>
              ) : (
                <span>--</span>
              ),
      // prop: params.isEdit && params.type.indexOf('change') < 0 ? 'contractObj' : 'collectionContractName',
      prop: 'contractObj',
      props: {
        placeholder: '请输入收款合同名称/编号并选择'
      },
      extra: ({ formModel }) =>
        ermHooks.compare(params?.lastDataInfo, formModel, 'contractObj', obj => (
          <span class="funi-erm-change">{obj.name}</span>
        ))
    },
    ...(['change_add', 'chang_edit'].includes(params.type) ||
    ['ERM_PAYMENT_CONTRACT_CHANGE'].includes(params.dicBusinessTypeCode)
      ? [
          {
            label: '变更类型',
            component: params.isEdit ? () => <ErmSelect code={dicCode.changeType}></ErmSelect> : null,
            prop: params.isEdit ? 'dicChangeTypeCode' : 'dicChangeTypeName',
            props: {
              placeholder: '请选择变更类型'
            },
            extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'dicChangeTypeName')
          }
        ]
      : []),
    {
      label: '服务内容',
      component:
        params.isEdit && params.type.indexOf('change') < 0
          ? () => (
              <el-select style="width:100%">
                {params.collectionService.map(item => {
                  return <el-option key={item.value} label={item.name} value={item.value} />;
                })}
              </el-select>
            )
          : null,
      prop: params.isEdit && params.type.indexOf('change') < 0 ? 'collectionServiceId' : 'collectionServiceName',
      hidden: !params.collectionContractId,
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'collectionServiceName')
    },
    {
      label: '合同关键条款',
      component: params.isEdit
        ? 'el-input'
        : ({ formModel }) => {
            return (
              <span
                style={{
                  'white-space': 'pre-wrap'
                }}
              >
                {formModel.keyTerm || '--'}
              </span>
            );
          },
      prop: 'keyTerm',
      colProps: {
        span: 24
      },
      props: {
        autosize: { minRows: 2, maxRows: 4 },
        type: 'textarea',
        maxlength: 200,
        'show-word-limit': true
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'keyTerm')
    },
    {
      label: '备注',
      component: params.isEdit
        ? 'el-input'
        : ({ formModel }) => {
            return (
              <span
                style={{
                  'white-space': 'pre-wrap'
                }}
              >
                {formModel.remark || '--'}
              </span>
            );
          },
      prop: 'remark',
      colProps: {
        span: 24
      },
      props: {
        autosize: { minRows: 2, maxRows: 4 },
        type: 'textarea',
        maxlength: 200,
        'show-word-limit': true
      },
      extra: ({ formModel }) => ermHooks.compare(params?.lastDataInfo, formModel, 'remark')
    }
  ];
};

export const useRule = (isEdit, type, dicBusinessTypeCode, verifyWorkflow, currentSelectedSection) => {
  const end_ = type.indexOf('end') > -1;
  const change_ = type.indexOf('change') > -1;
  let a = isEdit ? 'dicContractTypeCode' : 'dicContractTypeName';
  let b = 'projectObj';
  let c = isEdit ? 'employeeObj' : 'employeeName';
  let d = isEdit ? 'dicSettlementTypeCode' : 'dicSettlementTypeName';
  let e = isEdit ? 'dicPayTypeCode' : 'dicPayTypeName';
  let f = isEdit ? 'partA' : 'partA_object';
  let g = 'partB';
  let h = isEdit ? 'deptId' : 'deptName';
  let j = isEdit ? 'dicChangeTypeCode' : 'dicChangeTypeName';
  const signDateValidator = (rule, value, callback) => {
    if (!currentSelectedSection?.sectionGrantDate || !value) {
      callback();
    } else if (currentSelectedSection?.sectionGrantDate && value) {
      let sectionGrantDate = $utils.Date(currentSelectedSection?.sectionGrantDate);
      let nowDate = $utils.Date(value);
      if (nowDate?.isBefore(sectionGrantDate)) {
        callback(new Error('合同签订时间早于中标通知书发放时间'));
      } else {
        callback();
      }
    }
  };
  return {
    contractName: [{ required: true, message: '必填', trigger: 'change' }],
    [a]: [{ required: true, message: '必填', trigger: 'change' }],
    [f]: [{ required: true, message: '必填', trigger: 'change' }],
    [g]: [{ required: true, message: '必填', trigger: 'change' }],
    [h]: [{ required: true, message: '必填', trigger: 'change' }],
    [b]: [{ required: true, message: '必填', trigger: 'change' }],
    [c]: [{ required: true, message: '必填', trigger: 'change' }],
    [d]: [{ required: true, message: '必填', trigger: 'change' }],
    [e]: [{ required: true, message: '必填', trigger: 'change' }],
    signDate: [
      ...(verifyWorkflow ? [{ required: true, message: '必填', trigger: 'change' }] : []),
      {
        validator: signDateValidator,
        trigger: 'change'
      }
    ],
    contractAmount: [{ required: true, message: '必填', trigger: 'change' }],
    taxPayment: [{ required: true, message: '必填', trigger: 'change' }],
    companyId: [{ required: true, message: '必填', trigger: 'change' }],
    isIncludeOpenTerm: [{ required: true, message: '必填', trigger: 'change' }],
    isMultipartyContract: [{ required: true, message: '必填', trigger: 'change' }],
    dicSealTypeCode: end_ || change_ ? [] : [{ required: true, message: '必填', trigger: 'change' }],
    contractCount: [{ required: true, message: '必填', trigger: 'change' }],
    keyTerm: [{ required: true, message: '必填', trigger: 'change' }],
    [j]: [{ required: true, message: '必填', trigger: 'change' }],
    ...(['end_add', 'end_edit'].includes(type) || dicBusinessTypeCode == 'ERM_PAYMENT_CONTRACT_TERMINATE'
      ? {
          isSealUsed: [{ required: true, message: '必填', trigger: 'change' }],
          dicSealTypeCode: [{ required: true, message: '必填', trigger: 'change' }],
          dicSealTypeName: [{ required: true, message: '必填', trigger: 'change' }]
        }
      : {})
  };
};

export const serveColumns = ({
  isEdit,
  iptChange = () => {},
  contactRef = {},
  isIncludeOpenTerm,
  delFunc = () => {}
}) => {
  const reset_v = (key, el) => {
    contactRef[key] = el ? el : void 0;
  };

  const validateTaxRate = isOpenTerm => (value, callback) => {
    if (!Number(value) || Number(value) < 0 || (isOpenTerm ? Number(value) > 1 : false)) {
      callback(isOpenTerm ? '税率大于0小于1' : '税率大于0');
    } else {
      callback();
    }
  };
  return [
    // ...isEdit ? [{
    //     type: 'selection',
    //     width: '55px',
    //     fixed: 'left'
    // }] : [],
    {
      label: '产品名称',
      prop: 'productName',
      slots: {
        header: 'a'
      },
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <el-input
            onInput={e => {
              iptChange(index, 'productName', e);
            }}
            maxlength={50}
            placeholder="产品名称"
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_productName`, el);
            }}
            value={row.productName}
            c={c}
            key={`${row.uuId}_productName`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.productName}</span>
        );
      }
    },
    {
      label: '是否开放性条款',
      prop: 'isOpenTerm',
      slots: {
        header: 'b'
      },
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <el-select
            placeholder="请选择"
            onChange={e => {
              iptChange(index, 'isOpenTerm', e);
            }}
          >
            <el-option label="是" disabled={!isIncludeOpenTerm} value={true}></el-option>
            <el-option label="否" value={false}></el-option>
          </el-select>
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_isOpenTerm`, el);
            }}
            value={row.isOpenTerm}
            c={c}
            key={`${row.uuId}_isOpenTerm`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.isOpenTerm ? '是' : '否'}</span>
        );
      }
    },
    {
      label: '单价(元)',
      prop: 'unitPrice',
      slots: {
        header: 'c'
      },
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        if (row.isOpenTerm === true) {
          row.unitPrice = '';
          return '--';
        }
        let c = (
          <MoneyInput
            min={0}
            onChange={e => {
              iptChange(index, 'unitPrice', e);
              if (e && row.number) {
                row.totalPrice = $utils.round(e * row.number, 2);
              }
            }}
            maxlength={50}
            placeholder="单价"
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_unitPrice`, el);
            }}
            value={row.unitPrice}
            c={c}
            key={`${row.uuId}_unitPrice`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{ermHooks.erm_intl(row.unitPrice)}</span>
        );
      }
    },
    {
      label: '数量',
      prop: 'number',
      slots: {
        header: 'd'
      },
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        if (row.isOpenTerm === true) {
          row.number = '';
          return '--';
        }
        let c = (
          <el-input-number
            controls={false}
            precision={0}
            min={0}
            onInput={e => {
              iptChange(index, 'number', e);
              if (e && row.unitPrice) {
                row.totalPrice = $utils.round(e * row.unitPrice, 2);
              }
            }}
            maxlength={50}
            placeholder="数量"
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_number`, el);
            }}
            value={row.number}
            c={c}
            key={`${row.uuId}_number`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.number}</span>
        );
      }
    },
    {
      label: '税率',
      prop: 'taxRate',
      slots: {
        header: 'e'
      },
      align: isEdit ? 'left' : 'right',
      width: 180,
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            min={0}
            precision={5}
            onChange={e => {
              iptChange(index, 'taxRate', e);
              if (row.totalPrice) {
                //总价不为空
                //不含税金额(元) excludeTaxAmount
                row.excludeTaxAmount = $utils.round(row.totalPrice / (1 + Number(e)), 2);
              }
              if (row.totalPrice && row.excludeTaxAmount) {
                //总价和不含税金额不为空
                //税额(元) taxAmount
                row.taxAmount = $utils.round(row.totalPrice - row.excludeTaxAmount, 2);
              }
            }}
            maxlength={50}
            placeholder="税率"
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_taxRate`, el);
            }}
            value={row.taxRate}
            c={c}
            key={`${row.uuId}_taxRate`}
            rule={[{ required: true, message: '必填', validator: validateTaxRate(row.isOpenTerm) }]}
          ></Verifics>
        ) : (
          <span>{row.taxRate}</span>
        );
      }
    },
    {
      label: '总价(元)',
      prop: 'totalPrice',
      width: 180,
      align: 'right',
      render: ({ row, index }) => {
        if (row.isOpenTerm === true) {
          row.totalPrice = '';
          return '--';
        } else {
          return ermHooks.erm_intl(row.totalPrice);
        }
      }
    },
    {
      label: '税额(元)',
      prop: 'taxAmount',
      width: 180,
      align: 'right',
      render: ({ row, index }) => {
        if (row.isOpenTerm === true) {
          row.taxAmount = '';
          return '--';
        } else {
          row.taxAmount = $utils.round(row.totalPrice - row.excludeTaxAmount, 2);
          return ermHooks.erm_intl(row.taxAmount);
        }
      }
    },
    {
      label: '不含税金额(元)',
      prop: 'excludeTaxAmount',
      width: 180,
      align: 'right',
      render: ({ row, index }) => {
        if (row.isOpenTerm === true) {
          row.excludeTaxAmount = '';
          return '--';
        } else {
          row.excludeTaxAmount = $utils.round(row.totalPrice / (1 + Number(row.taxRate)), 2);
          return ermHooks.erm_intl(row.excludeTaxAmount);
        }
      }
    },
    {
      label: '备注',
      prop: 'remark',
      width: 180,
      render: ({ row, index }) => {
        return isEdit ? (
          <el-input v-model={row.remark} {...{ prop: datePickerProps }}></el-input>
        ) : (
          <span>{row.remark}</span>
        );
      }
    },
    ...(isEdit
      ? [
          {
            label: '操作',
            prop: '',
            align: 'center',
            fixed: 'right',
            cellInteraction: false,
            width: 100,
            render: ({ row, index }) => {
              return (
                <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                  {
                    <el-button
                      type="primary"
                      onClick={() => {
                        delFunc(index);
                      }}
                      link
                    >
                      删除
                    </el-button>
                  }
                </div>
              );
            }
          }
        ]
      : [])
  ];
};

export const payPlanColumns = ({ isEdit, iptChange = () => {}, contactRef = {}, delFunc = () => {} }) => {
  const reset_v = (key, el) => {
    contactRef[key] = el ? el : void 0;
  };
  return [
    // ...isEdit ? [{
    //     type: 'selection',
    //     width: '55px',
    //     fixed: 'left'
    // }] : [],
    {
      label: '付款条件',
      prop: 'payCondition',
      slots: {
        header: 'a'
      },
      render: ({ row, index }) => {
        let c = (
          <el-input
            onInput={e => {
              iptChange(index, 'payCondition', e);
            }}
            maxlength={50}
            placeholder="付款条件" // [17414]bug修改
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_payCondition`, el);
            }}
            value={row.payCondition}
            c={c}
            key={`${row.uuId}_payCondition`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.payCondition}</span>
        );
      }
    },
    {
      label: '付款金额(元)',
      prop: 'payAmount',
      slots: {
        header: 'b'
      },
      align: isEdit ? 'left' : 'right',
      render: ({ row, index }) => {
        let c = (
          <MoneyInput
            placeholder="付款金额"
            onChange={e => {
              iptChange(index, 'payAmount', e);
            }}
          ></MoneyInput>
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_payAmount`, el);
            }}
            value={row.payAmount}
            c={c}
            key={`${row.uuId}_payAmount`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{ermHooks.erm_intl(row.payAmount)}</span>
        );
      }
    },
    {
      label: '预计付款日期',
      prop: 'expectedPaymentTime',
      slots: {
        header: 'c'
      },
      render: ({ row, index }) => {
        let c = <el-date-picker style="width:100%" format="YYYY-MM-DD" value-format="YYYY-MM-DD"></el-date-picker>;
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_expectedPaymentTime`, el);
            }}
            value={row.expectedPaymentTime}
            c={c}
            onChange={e => {
              iptChange(index, 'expectedPaymentTime', e);
            }}
            key={`${row.uuId}_expectedPaymentTime`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.expectedPaymentTime}</span>
        );
      }
    },
    {
      label: '付款状态',
      prop: 'dicPayStatusName'
    },
    {
      label: '实际付款总金额(元)',
      prop: 'actualPaymentAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.actualPaymentAmount)
    },

    ...(isEdit
      ? [
          {
            label: '操作',
            prop: '',
            align: 'center',
            fixed: 'right',
            cellInteraction: false,
            width: 100,
            render: ({ row, index }) => {
              return (
                <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                  {
                    <el-button
                      type="primary"
                      onClick={() => {
                        delFunc(index);
                      }}
                      link
                    >
                      删除
                    </el-button>
                  }
                </div>
              );
            }
          }
        ]
      : [])
  ];
};

export const sectionColumn = () => {
  return [
    {
      label: '标段名称',
      prop: 'sectionName'
    },
    {
      label: '控制价(元)',
      prop: 'controlPricesAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row?.controlPricesAmount || 0);
      }
    },
    {
      label: '开标时间',
      prop: 'openSectionDate'
    },
    {
      label: '挂网时间',
      prop: 'spreadNetTime'
    },
    {
      label: '成交金额(元)',
      prop: 'tradingAmount',
      align: 'right',
      render: ({ row, index }) => {
        return ermHooks.erm_intl(row?.tradingAmount || 0);
      }
    },
    {
      label: '实施方式',
      prop: 'dicImplementTypeName'
    },
    {
      label: '中标通知书发放时间',
      prop: 'sectionGrantDate'
    },
    {
      label: '选聘结果',
      prop: 'dicRecruitmentResultStatusName'
    }
  ];
};

export const gerScDataFields = () => {
  return {
    productName: '',
    isOpenClause: '',
    unitPrice: '',
    number: '',
    taxRate: '',
    totalPrice: '',
    taxAmount: '',
    preTaxAmount: '',
    remark: '',
    uuId: $utils.guid()
  };
};

export const getBDataFields = () => {
  return {
    payCondition: '',
    payAmount: '',
    expectedPaymentTime: '',
    dicPayStatusName: '',
    actualPaymentAmount: '',
    uuId: $utils.guid()
  };
};
