/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:54:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-26 18:43:24
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/payment/payment.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';
import ermHooks from '@/apps/erm/hooks/index.js';
export const usePaymentColumns = ({
  seeDateils,
  editFunc = () => { },
  delFunc = () => { },
  auditFunc = () => { },
  infoFuinc
}) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '合同名称',
      prop: 'contractName',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={seeDateils}
            auth={'ERM_CONTRACT_PAYMENT_INFO'}
            text={row.dataTitle}
          />
        );
      },
      fixed: 'left'
    },
    {
      label: '合同编号',
      prop: 'contractSn'
    },
    {
      label: '甲方',
      prop: 'firstPartyName'
    },
    {
      label: '合同金额(元)',
      prop: 'contractAmount',
      align: 'right',
      render: ({ row, index }) => ermHooks.erm_intl(row.contractAmount)
    },
    {
      label: '合同状态',
      prop: 'dicContractStatusName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },
    {
      label: '流程节点',
      prop: 'businessNode'
    },
    {
      label: '归档编号',
      prop: 'archiveSn'
    },
    {
      label: '项目名称',
      prop: 'projectName'
    },
    {
      label: '负责人',
      prop: 'employeeName'
    },

    {
      label: '在办业务',
      prop: 'dicPayContractDoingBusName'
    },
    {
      label: '变更类型',
      prop: 'dicChangeTypeName'
    },
    {
      label: '支付状态',
      prop: 'dicPayStatusName'
    },
    {
      label: '合同签订时间',
      prop: 'signDate'
    },
    {
      label: '生效时间',
      prop: 'takeEffectTime'
    },
    {
      label: '是否开放性条款',
      prop: 'isIncludeOpenTerm',
      render: ({ row, index }) => {
        return row.isIncludeOpenTerm ? '是' : '否';
      }
    },

    {
      label: '乙方',
      prop: 'secondPartyName'
    },
    {
      label: '结算方式',
      prop: 'dicSettlementTypeName'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      width: 120,
      align: 'center',
      render: ({ row, index }) => {
        let operationBtn = {
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={editFunc}
              auth={'ERM_CONTRACT_PAYMENT_EDIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          DEL: (
            <el-popconfirm
              title="确定删除当前付款合同？"
              width="220"
              onConfirm={() => {
                delFunc(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_CONTRACT_PAYMENT_DEL'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={auditFunc}
              auth={'ERM_CONTRACT_PAYMENT_AUDIT'}
              text={'审核'}
            ></Hyperlink>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={infoFuinc}
              auth={'ERM_CONTRACT_PAYMENT_INFO'}
              text={'业务详情'}
            ></Hyperlink>
          )
        };
        let btnList = [];
        btnList = $utils.clone(row.buttonList, true);
        if (!$utils.isNil(row.dicPayContractDoingBusCode) && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO');
        }
        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

export const useBtnsConfig = ({
  addFunc = () => { },
  changeFunc = () => { },
  exportFn = () => { },
  endFunc = () => { },
  cancelFunc = () => { },
  hasValid = false
}) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACT_PAYMENT_ADD" onClick={addFunc} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button disabled={!hasValid} v-auth="ERM_CONTRACT_PAYMENT_CHANGE" onClick={changeFunc} type="primary">
          变更
        </el-button>
      )
    },

    {
      component: () => (
        <el-button disabled={!hasValid} v-auth="ERM_CONTRACT_PAYMENT_STOP" onClick={endFunc} type="primary">
          终止
        </el-button>
      )
    },
    {
      component: () => (
        <el-button disabled={!hasValid} v-auth="ERM_CONTRACT_PAYMENT_ABANDON" onClick={cancelFunc} type="primary">
          作废
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_CONTRACT_PAYMENT_EXPORT" type="primary" onClick={exportFn}>
          导出
        </el-button>
      )
    }
  ];
};
