<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:52:57
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-30 09:43:42
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/invoice/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
    <AuditDialog v-model="showDialog" :callback="dialogCallBack" />
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref, onBeforeMount } from 'vue';
import { useInvoiceColumns, useBtnsConfig } from './../hooks/invoice/invoice.jsx';
import { queryIssueFapiaoApplyListHttp, getContractListForNoContractFapiaoHttp } from './../hooks/invoice/api.js';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
import AuditDialog from '@/apps/erm/component/auditDialog/index.vue';
import { deleteBusiness, submitBusiness } from '@/apps/erm/config/business.js';
import { apiUrl, creatBusHttp } from '@/apps/erm/contract/hooks/invoice/api.js';
import { expotrFunction } from '@/apps/erm/config/config.jsx';

const router = useRouter();

const listPage = ref();
const rowData = ref(void 0);
const showDialog = ref(false);
const dialogCallBack = ref(void 0);
const queryData = ref(void 0);
const contractList = ref([]);
const contractId = ref('');

const lodaData = async (params, queryParams) => {
  rowData.value = void 0;
  queryData.value = queryParams;
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryIssueFapiaoApplyListHttp({ ...params, ...queryParams });
  return data;
};
onMounted(() => {});
onBeforeMount(async () => {
  let { list } = await getContractListForNoContractFapiaoHttp();
  contractList.value = list;
});
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig({
          addFunc,
          cancelFunc,
          exportFn,
          addFuncT,
          hasValid: rowData.value?.row?.dicFapiaoAppStatusCode === '1',
          contractList: contractList.value,
          contractId
        }),
        // searchConfig: useSearchConfig(),
        lodaData: lodaData,
        fixedButtons: true,
        reloadOnActive: true,
        checkOnRowClick: true,
        columns: useInvoiceColumns({ seeDateils, auditFunc, delFunc, editFunc, infoFuinc }),
        on: {
          rowClick: e => {
            row_click(e);
          }
        }
      }
    }
  ];
});

const seeDateils = row => {
  router.push({
    name: 'ermContractInvoiceInfo',
    query: {
      title: row.contractName || '开票申请',
      bizName: '详情',
      type: 'info',
      tab: ['开票申请', row.contractName || '', '详情'].join('-'),
      id: row.dicFapiaoAppDoingBusCode ? row.lastId || row.id : row.id,
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicFapiaoApplyTypeCode: row.dicFapiaoApplyTypeCode
    }
  });
};

const infoFuinc = row => {
  router.push({
    name: 'ermContractInvoiceInfo',
    query: {
      title: row.contractName || '开票申请',
      bizName: '审核',
      type: 'info',
      tab: ['开票申请', row.contractName || '', '业务详情'].join('-'),
      id: row.id,
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicFapiaoApplyTypeCode: row.dicFapiaoApplyTypeCode
    }
  });
};
const exportFn = () => {
  expotrFunction({ url: apiUrl.issueListExport, params: queryData.value, FileName: '开票申请' });
};

const editFunc = row => {
  let eum = {
    ERM_ISSUE_FAPIAO_APPLY_ADD: 'add_edit',
    ERM_ISSUE_FAPIAO_APPLY_REFUND: 'cancel_edit'
  };
  router.push({
    name: 'ermContractInvoiceAdd',
    query: {
      bizName: '编辑',
      type: eum[row.dicBusinessTypeCode],
      tab: ['开票申请', row.contractName || '', '编辑'].join('-'),
      id: row.id,
      title: row.contractName || '开票申请',
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicFapiaoApplyTypeCode: row.dicFapiaoApplyTypeCode
    }
  });
};

const addFunc = () => {
  router.push({
    name: 'ermContractInvoiceAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: '开票申请-新建'
    }
  });
};

const auditFunc = row => {
  router.push({
    name: 'ermContractInvoiceInfo',
    query: {
      title: row.contractName || '开票申请详情',
      bizName: '审核',
      type: 'audit',
      id: row.id,
      tab: row.contractName ? ['开票申请', row.contractName, '审核'].join('-') : '开票申请详情',
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicFapiaoApplyTypeCode: row.dicFapiaoApplyTypeCode
    }
  });
};

const addFuncT = () => {
  let obj = contractList.value.find(item => item.contractId === contractId.value);
  if (!obj) return;
  router.push({
    name: 'ermContractInvoiceAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: '开票申请-新建',
      contract: JSON.stringify(obj)
    }
  });
};

const delFunc = async row => {
  // await deleteBusiness(row.businessId);
  await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  listPage.value.reload({ resetPage: false });
};
const cancelFunc = async () => {
  let { row } = rowData.value;
  let {
    issueFapiaoApplyDetailVo: { oldApplySn, oldFapiaoAmount }
  } = await creatBusHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_ISSUE_FAPIAO_APPLY_REFUND'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractInvoiceAdd',
      query: {
        bizName: '新建',
        type: 'cancel_add',
        tab: ['开票申请', row.contractName, '退票'].join('-'),
        id: row.id,
        title: row.contractName || '开票申请废弃',
        reason,
        oldApplySn,
        oldFapiaoAmount,
        dicFapiaoApplyTypeCode: row.dicFapiaoApplyTypeCode
      }
    });
  };
  showDialog.value = true;
};

const row_click = e => {
  rowData.value = e;
};
</script>
