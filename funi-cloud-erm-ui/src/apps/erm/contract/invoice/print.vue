<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-10-09 14:49:34
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-30 12:47:00
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/collection/print.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div v-loading="loading">
    <div class="print" :id="uuId">
      <div style="text-align: center">
        <h2>{{ contractName }}</h2>
      </div>
      <GroupTitle title="开票申请表" />
      <funi-form-print :inline="false" :schema="schema" @get-form="setForm" :col="3" :border="true" />

      <GroupTitle title="发票信息" />
      <PrintTable :columns="columns" :dataList="cData" />
      <GroupTitle title="开票明细" />
      <PrintTable :columns="fdColumns" :dataList="fdData" />
      <p style="page-break-after: always"></p>
      <PrintAuditLog ref="printAuditLog" :businessId="businessId" />
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onMounted, unref, nextTick } from 'vue';
import { useFormSchema } from './../hooks/invoice/baseInfo.jsx';

import { Print } from '@/apps/erm/hooks/Print.js';
import funiFormPrint from '@/apps/erm/component/printForm/index.vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import PrintAuditLog from '@/apps/erm/component/printAuditLog/index.vue';
import PrintTable from '@/apps/erm/component/printTable/index.vue';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const businessId = ref(route.query.businessId);
const form = ref();

const printAuditLog = ref();
const loading = ref(false);
const contractName = ref(route.query.contractName);

const multiTab = useMultiTab();
const dicBusinessTypeCode = ref('');
const companyList = ref([]);
const deptList = ref([]);
const cData = ref([]);
const fdData = ref([]);
const setForm = e => {
  form.value = e;
};
companyList.value = JSON.parse(route.query.companyList);
deptList.value = JSON.parse(route.query.deptList);
const dicFapiaoApplyTypeCode = ref(route.query.dicFapiaoApplyTypeCode);
const id = route.query.id;

const uuId = 'print_' + $utils.guid();
const schema = computed(() => {
  if (dicFapiaoApplyTypeCode.value == '2') {
    return [
      {
        label: '收款合同名称',
        prop: 'contractName'
      },
      {
        label: '状态',
        prop: 'status'
      },
      {
        label: '归属公司',
        prop: 'companyName'
      },
      {
        label: '归属部门',
        prop: 'deptName'
      },
      {
        label: '服务项目',

        prop: 'projectName'
      },
      {
        label: '负责人',

        prop: 'employeeName'
      },
      {
        label: '领取人',
        prop: 'receivePersonId'
      }
    ];
  }
  return useFormSchema({
    isEdit: false,
    type: 'info',
    dicBusinessTypeCode: dicBusinessTypeCode.value,
    companyList: companyList.value,
    deptList: deptList.value
  });
});

const printCallback = () => {
  multiTab.closeCurrentPage();
  router.push({
    name: 'ermContractInvoiceInfo',
    query: {
      title: unref(contractName) || '收款合同',
      bizName: '详情',
      type: 'info',
      id: unref(id),
      tab: unref(contractName) ? ['开票申请', unref(contractName), '审核'].join('-') : '开票申请详情',
      businessId: businessId.value,
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicFapiaoApplyTypeCode: row.dicFapiaoApplyTypeCode
    }
  });
};

const printFunc = () => {
  Print(document.querySelector('#' + uuId), {
    type: 'html',
    targetStyles: ['*'],
    scanStyle: false,
    maxWidth: 5000,
    callback: printCallback
  });
};
const columns = [
  {
    title: '开票时间',
    dataIndex: 'invoicingTime'
  },
  {
    title: '发票号码',
    dataIndex: 'fapiaoSn'
  },
  {
    title: '发票代码',
    dataIndex: 'fapiaoNo'
  }
];

const fdColumns = [
  {
    title: '序号',
    dataIndex: 'index'
  },
  {
    title: '发票抬头',
    dataIndex: 'invoiceTitle'
  },
  {
    title: '本次开票金额',
    dataIndex: 'currentFapiaoAmount'
  },
  {
    title: '开票类型',
    dataIndex: 'dicFapiaoTypeName'
  },
  {
    title: '纳税人识别号',
    dataIndex: 'taxIdNumber'
  }
];

onMounted(() => {
  getInvoiceInfo();
});

const getInvoiceInfo = async () => {
  const { obj, list, fapiaoDetailRequestList } = route.query;
  form.value.setValues(JSON.parse(obj));
  cData.value = JSON.parse(list);
  fdData.value = JSON.parse(fapiaoDetailRequestList)?.map((item, index) => {
    return {
      ...item,
      index: index + 1
    };
  });

  console.log(fdData.value);
  loading.value = true;
  await unref(printAuditLog)
    .getInfo()
    .finally(() => {
      loading.value = false;
    });

  await nextTick();
  startPrint();
};

const startPrint = () => {
  setTimeout(() => {
    printFunc();
  }, 500);
};
</script>
<style scoped>
* {
  box-sizing: border-box;
}
.print {
  padding: 10px;
}
:deep(.group-title .title:after) {
  width: 0;
}
:deep(.group-title .title) {
  color: var(--el-text-color);
  padding-left: 0;
}
</style>
