<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:53:05
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-30 11:22:40
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/invoice/info.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <funi-detail
      :bizName="_route.bizName"
      :auditButtons="buttons"
      :steps="steps"
      :showWorkflow="true"
      :detailHeadOption="detailHeadOption || {}"
      :businessId="['info', 'audit'].includes(_route.type) && businessId ? businessId : void 0"
      :beforeAuditFn="beforeAuditFn"
      @auditClick="auditClick"
      @auditEvent="auditEvent"
    />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, watch, nextTick, unref, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import BaseInfo from './../component/invoice/baseInfo.vue';
import { auditEndFunc } from '@/apps/erm/config/business.js';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { queryActivityConfigInfoHttp } from './../hooks/invoice/api';
import BaseInfoNoConcract from './../component/invoice/baseInfoNoConcract.vue';
const multiTab = useMultiTab();
const route = useRoute();
const router = useRouter();
const id = ref(route.query.id);
const sn = ref(route.query.sn);
const businessId = ref('');
const infoData = ref({});

const baseDom = ref();
const _route = ref(route.query);
const dicFapiaoApplyTypeCode = ref(route.query.dicFapiaoApplyTypeCode);

const buttons = computed(() => {
  return unref(infoData).businessNode && unref(_route).type == 'audit'
    ? [
        {
          key: '暂存',
          action: 'ts'
        }
      ]
    : [];
});
const steps = computed(() => {
  let arr = [
    {
      title: '开票申请表',
      preservable: true,
      type: dicFapiaoApplyTypeCode.value == '2' ? BaseInfoNoConcract : BaseInfo,
      props: {
        id: unref(id),
        sn: unref(sn),
        isEdit: !['audit', 'info'].includes(unref(_route).type),
        type: unref(_route).type,
        businessId: unref(businessId),
        ref: refFun
      },
      on: {
        updateID: ({ id: v, businessId: bv }) => {
          id.value = v;
          businessId.value = bv;
        },
        infoUpdate: data => {
          infoData.value = data;

          let { id: v, businessId: bv } = data;
          id.value = v;
          businessId.value = bv;
        }
      }
    },
    ...(businessId.value && (route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode)
      ? [
          {
            title: '办件记录',
            type: 'FuniWorkRecord',
            props: {
              objectListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              busListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              params: {
                businessId: businessId.value,
                dicBusinessTypeCode: route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode
              }
            },
            on: {
              onClick
            }
          }
        ]
      : [])
  ];
  return arr;
});
const refFun = e => {
  baseDom.value = e;
};

const beforeAuditFn = async ({ businessExecutionType }) => {
  if (unref(infoData).dicFapiaoAppDoingBusCode != '1') {
    return Promise.resolve({});
  }
  let flag = await queryActivityConfigInfoHttp({ businessId: businessId.value });
  if (['AGREE', 'SUBMIT'].includes(businessExecutionType)) {
    try {
      let t = flag ? void 0 : 'notValidate';
      return await unref(baseDom).authFun(t);
    } catch {
      return Promise.reject();
    }
  }
  return Promise.resolve({});
};
const auditClick = e => {
  if (e == 'ts') {
    unref(baseDom).authFun('ts');
  }
};

const print = () => {
  let obj = {};
  let list = [];
  let companyList = [];
  let deptList = [];
  let fapiaoDetailRequestList = [];
  try {
    const form = baseDom.value?.getForm();
    obj = $utils.clone(form?.getValues() ?? {}, true);
    if (dicFapiaoApplyTypeCode.value == '2') {
      obj.receivePersonId = obj?.receivePersonId?.name ?? '--';
    }

    ({ list, companyList, deptList, fapiaoDetailRequestList } = baseDom.value?.getPrintData());
  } catch {}
  router.push({
    name: 'ermContractInvoicePrint',
    query: {
      obj: JSON.stringify(obj),
      list: JSON.stringify(list),
      businessId: businessId.value,
      companyList: JSON.stringify(companyList),
      deptList: JSON.stringify(deptList),
      fapiaoDetailRequestList: JSON.stringify(fapiaoDetailRequestList),
      id: id.value,
      dicBusinessTypeCode: route.query.dicBusinessTypeCode,
      dicFapiaoApplyTypeCode: route.query.dicFapiaoApplyTypeCode,
      contractName: route.query.title
    }
  });
};

const copy = () => {
  router.push({
    name: 'ermContractInvoiceAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: ['开票申请', unref(infoData).contractName || '', '新建'].join('-'),
      id: id.value,
      title: unref(infoData).contractName || '开票申请'
    }
  });
};

const detailHeadOption = computed(() => {
  const { dicFapiaoAppStatusCode } = infoData.value;
  let obj = {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点',
    btns: [
      {
        name: '打印',
        props: { type: 'text' },
        on: { click: print }
      },
      ...(unref(_route).type === 'info' && dicFapiaoApplyTypeCode.value != '2'
        ? [
            {
              name: '复制',
              props: { type: 'text' },
              on: { click: copy },
              menuAuth: 'ERM_CONTRACTINVOICE_COPY'
            }
          ]
        : [])
    ]
  };
  return obj;
});

const auditEvent = () => {
  auditEndFunc(() => {
    multiTab.closeCurrentPage();
  });
};
const onClick = row => {
  router.push({
    name: 'ermContractInvoiceInfo',
    query: {
      title: row.contractName || '开票申请',
      bizName: '详情',
      type: 'info',
      tab: ['开票申请', row.contractName || '', '详情'].join('-'),
      id: row.dicFapiaoAppDoingBusCode ? row.lastId || row.id : row.id,
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      dicFapiaoApplyTypeCode: row.dicFapiaoApplyTypeCode
    }
  });
};
</script>
