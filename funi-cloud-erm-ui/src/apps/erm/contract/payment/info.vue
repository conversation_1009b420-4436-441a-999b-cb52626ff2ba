<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:53:05
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-30 12:07:42
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/payment/info.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <funi-detail
      :bizName="bizName"
      :auditButtons="buttons"
      :steps="steps"
      :showWorkflow="true"
      :detailHeadOption="detailHeadOption || {}"
      @auditEvent="auditEvent"
      :beforeAuditFn="beforeAuditFn"
      :businessId="['info', 'audit'].includes(route.query.type) ? businessId : void 0"
    />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, watch, nextTick, unref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import BaseInfo from './../component/payment/baseInfo.vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { auditEndFunc } from '@/apps/erm/config/business.js';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
const multiTab = useMultiTab();

const route = useRoute();
const router = useRouter();
const id = ref(route.query.id);
const businessId = ref('');
const infoData = ref({});
const buttons = ref([]);
const type = route.query.type;
const bizName = ref(route.query.bizName);
const baseDom = ref();

// const auditDrawer = ref();
// watch(businessId, async () => {
//   await nextTick();
//   if (type === 'audit' && businessId.value) {
//     auditDrawer.value.show();
//   }
// });
const steps = computed(() => {
  let arr = [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        type,
        isEdit: !['audit', 'info'].includes(route.query.type),
        businessId: businessId.value,
        ref: refFun
      },
      on: {
        updateID: ({ id: v, businessId: bv }) => {
          id.value = v;
          businessId.value = bv;
        },
        infoUpdate: data => {
          infoData.value = data;
          let { id: v, businessId: bv } = data;
          id.value = v;
          businessId.value = bv;
        }
      }
    },
    ...(businessId.value && (route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode)
      ? [
          {
            title: '办件记录',
            type: 'FuniWorkRecord',
            props: {
              objectListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              busListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              params: {
                businessId: businessId.value,
                dicBusinessTypeCode: route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode
              }
            },
            on: {
              onClick
            }
          }
        ]
      : [])
  ];
  return arr;
});

const print = () => {
  router.push({
    name: 'ermContractPaymentPrint',
    query: {
      id: id.value
    }
  });
};
const copy = () => {
  router.push({
    name: 'ermContractPaymentAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: ['付款合同', unref(infoData).contractName || '', '新建'].join('-'),
      id: id.value,
      title: unref(infoData).contractName || '付款合同'
    }
  });
};
const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点',
    btns: [
      ...(type === 'info'
        ? [
            {
              name: '打印',
              props: { type: 'text' },
              on: { click: print }
            },
            {
              name: '复制',
              props: { type: 'text' },
              on: { click: copy },
              menuAuth: 'ERM_CONTRACT_PAYMENT_COPY'
            }
          ]
        : [])
    ]
  };
  return obj;
});
const refFun = e => {
  baseDom.value = e;
};

const beforeAuditFn = async ({ businessExecutionType }) => {
  if (
    ['AGREE', 'SUBMIT'].includes(businessExecutionType) &&
    ['ERM_PAYMENT_CONTRACT_ADD', 'ERM_PAYMENT_CONTRACT_CHANGE'].includes(unref(infoData).dicBusinessTypeCode)
  ) {
    try {
      return await unref(baseDom).authFun();
    } catch {
      return Promise.reject();
    }
  }
  return Promise.resolve({});
};
const auditEvent = () => {
  auditEndFunc(() => {
    multiTab.closeCurrentPage();
  });
};
const onClick = row => {
  router.push({
    name: 'ermContractPaymentInfo',
    query: {
      title: row.contractName || '付款合同',
      bizName: '详情',
      type: 'info',
      id: row.dicPayContractDoingBusCode ? row.lastId || row.id : row.id,
      tab: ['付款合同', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
</script>
