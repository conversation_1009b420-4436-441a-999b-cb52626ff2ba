<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 16:52:57
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-30 12:10:09
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/payment/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
    <AuditDialog v-model="showDialog" :callback="dialogCallBack" />
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref } from 'vue';
import { usePaymentColumns, useBtnsConfig } from './../hooks/payment/payment.jsx';
import { queryPaymentContractListHttp } from './../hooks/payment/api.js';
import { ElNotification } from 'element-plus';
import AuditDialog from '@/apps/erm/component/auditDialog/index.vue';
import { useRouter } from 'vue-router';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
import { apiUrl, paymentContractHttp } from '@/apps/erm/contract/hooks/payment/api.js';
import { deleteBusiness, submitBusiness } from '@/apps/erm/config/business.js';

const router = useRouter();
const listPage = ref();
let query = ref();
const rowData = ref(void 0);
const showDialog = ref(false);
const dialogCallBack = ref(void 0);
const lodaData = async (params, queryParams) => {
  rowData.value = void 0;
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryPaymentContractListHttp({ ...params, ...queryParams });
  query.value = queryParams;
  return data;
};
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig({
          addFunc,
          changeFunc,
          endFunc,
          exportFn,
          cancelFunc,
          hasValid: rowData.value?.row?.dicContractStatusCode === '1'
        }),
        // searchConfig: useSearchConfig(),
        lodaData: lodaData,
        fixedButtons: true,
        reloadOnActive: true,
        checkOnRowClick: true,
        columns: usePaymentColumns({ seeDateils, auditFunc, editFunc, delFunc, infoFuinc }),
        on: {
          rowClick: e => {
            row_click(e);
          }
        }
      }
    }
  ];
});

const seeDateils = row => {
  router.push({
    name: 'ermContractPaymentInfo',
    query: {
      title: row.contractName || '付款合同',
      bizName: '详情',
      type: 'info',
      id: row.dicPayContractDoingBusCode ? row.lastId || row.id : row.id,
      tab: ['付款合同', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const infoFuinc = row => {
  router.push({
    name: 'ermContractPaymentInfo',
    query: {
      title: row.contractName || '付款合同',
      bizName: '审核',
      type: 'info',
      id: row.id,
      tab: ['付款合同', row.contractName || '', '业务详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};

const exportFn = () => {
  expotrFunction({ url: apiUrl.queryPaymentContractListExport, params: query.value, FileName: '付款合同' });
};

const editFunc = row => {
  let eum = {
    ERM_PAYMENT_CONTRACT_ADD: 'add_edit',
    ERM_PAYMENT_CONTRACT_CHANGE: 'change_edit',
    ERM_PAYMENT_CONTRACT_TERMINATE: 'end_edit',
    ERM_PAYMENT_CONTRACT_CANCEL: 'cancel_edit'
  };
  router.push({
    name: 'ermContractPaymentAdd',
    query: {
      bizName: '编辑',
      type: eum[row.dicBusinessTypeCode],
      tab: ['付款合同', row.contractName || '', '编辑'].join('-'),
      id: row.id,
      title: row.contractName || '付款合同',
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
const auditFunc = row => {
  router.push({
    name: 'ermContractPaymentInfo',
    query: {
      title: row.contractName || '付款合同',
      bizName: '审核',
      type: 'audit',
      id: row.id,
      tab: ['付款合同', row.contractName, '审核'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
const delFunc = async row => {
  // await deleteBusiness(row.businessId);
  await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  listPage.value.reload({ resetPage: false });
};
const addFunc = () => {
  router.push({
    name: 'ermContractPaymentAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: '付款合同-新建'
    }
  });
};

const changeFunc = async () => {
  let { row } = rowData.value;
  await paymentContractHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_PAYMENT_CONTRACT_CANCEL'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractPaymentAdd',
      query: {
        bizName: '新建',
        type: 'change_add',
        tab: ['付款合同', row.contractName, '变更'].join('-'),
        id: row.id,
        title: row.contractName || '付款合同',
        reason
      }
    });
  };
  showDialog.value = true;
};

const endFunc = async () => {
  let { row } = rowData.value;
  await paymentContractHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_PAYMENT_CONTRACT_TERMINATE'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractPaymentAdd',
      query: {
        bizName: '新建',
        type: 'end_add',
        tab: ['付款合同', row.contractName, '终止'].join('-'),
        id: row.id,
        title: row.contractName || '付款合同',
        reason
      }
    });
  };
  showDialog.value = true;
};

const cancelFunc = async () => {
  let { row } = rowData.value;
  await paymentContractHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_PAYMENT_CONTRACT_TERMINATE'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractPaymentAdd',
      query: {
        bizName: '新建',
        type: 'cancel_add',
        tab: ['收款合同', row.contractName, '作废'].join('-'),
        id: row.id,
        title: row.contractName || '收款合同',
        reason
      }
    });
  };
  showDialog.value = true;
};

const row_click = e => {
  rowData.value = e;
};
</script>
