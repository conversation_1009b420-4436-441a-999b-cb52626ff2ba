<template>
  <div v-loading="loading">
    <div class="print" :id="uuId">
      <div style="text-align: center">
        <h2>{{ contractName }}</h2>
      </div>
      <GroupTitle title="基本信息" />
      <funi-form-print :inline="false" :schema="schema" @get-form="setForm" :col="3" :border="true" />
      <p style="page-break-after: always"></p>
      <GroupTitle title="合同相关方" />
      <PrintTable :columns="columns" :dataList="dataList" />
      <PrintAuditLog ref="printAuditLog" :businessId="businessId" />
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onMounted, unref, nextTick } from 'vue';
import { useFormSchema } from './../hooks/payment/baseInfo.jsx';
import { paymentContractInfoHttp, queryCollectionServiceListp } from './../hooks/payment/api.js';
import { Print } from '@/apps/erm/hooks/Print.js';
import funiFormPrint from '@/apps/erm/component/printForm/index.vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import PrintAuditLog from '@/apps/erm/component/printAuditLog/index.vue';
import PrintTable from '@/apps/erm/component/printTable/index.vue';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
import { useRoute, useRouter } from 'vue-router';

const companyList = ref([]);
const route = useRoute();
const router = useRouter();
const businessId = ref();
const form = ref();
const dataList = ref([]);
const printAuditLog = ref();
const loading = ref(false);
const contractName = ref('');
const multiTab = useMultiTab();
const collectionService = ref();
const setForm = e => {
  form.value = e;
};

const id = route.query.id || 'c6ec8e40-011d-4f5e-ac3e-1e06e456f655';
const uuId = 'print_' + $utils.guid();
const schema = computed(() => {
  return useFormSchema({
    isEdit: false,
    type: 'info',
    companyList: companyList.value
  });
});

const printFunc = () => {
  Print(document.querySelector('#' + uuId), {
    type: 'html',
    targetStyles: ['*'],
    scanStyle: false,
    maxWidth: 5000,
    callback: printCallback
  });
};

const printCallback = () => {
  multiTab.closeCurrentPage();
  router.push({
    name: 'ermContractPaymentInfo',
    query: {
      title: unref(contractName) || '收款合同',
      bizName: '详情',
      type: 'info',
      id: unref(id),
      tab: ['收款合同', unref(contractName), '详情'].join('-')
    }
  });
};
const columns = [
  {
    title: '签约方',
    dataIndex: 'dicMultipartyTypeName',
    width: '40'
  },
  {
    title: '签约方名称',
    dataIndex: 'customerName',
    width: '60'
  }
];
onMounted(() => {
  if (id) {
    getPaymentInfo();
  }
});

/**
 * @description 获取详情
 * **/
const getPaymentInfo = async () => {
  let data = void 0;
  let httpName = void 0;
  let params = {};
  httpName = paymentContractInfoHttp;
  params = {
    paymentContractId: id
  };
  loading.value = true;
  data = await httpName(params);
  if (data.collectionContractId) {
    await setCollectionService(data.collectionContractId, false);
  }
  contractName.value = data.contractName;
  businessId.value = data.businessId;
  await nextTick();
  await unref(printAuditLog)
    .getInfo()
    .finally(() => {
      loading.value = false;
    });
  let obj = {};
  let filed = ['projectObj', 'employeeObj'];
  schema.value.forEach(item => {
    if (filed.indexOf(item.prop) > -1) {
      let str = item.prop.slice(0, -3);
      obj[item.prop] = {
        id: data[`${str}Id`],
        name: data[`${str}Name`]
      };
    } else if (item.prop === 'partA' || item.prop === 'partA_object') {
      let o = data?.payConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '1';
      });
      if (o && o.length) {
        obj['partA'] = o[0].customerId;
        obj['partA_object'] = {
          name: o[0].customerName,
          id: o[0].customerId
        };
      }
    } else if (item.prop === 'partB' || item.prop === 'partBName') {
      let o = data?.payConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '2';
      });

      if (o && o.length) {
        obj['partB'] = {
          id: o[0].customerId,
          name: o[0].customerName
        };
        obj['partBName'] = o[0].customerName;
      }
    } else if (item.prop === 'dicSealTypeCode') {
      obj[item.prop] = data[item.prop] ? data[item.prop].split(',') : void 0;
    } else if (item.prop === 'contractObj') {
      obj[item.prop] = {
        id: data.collectionContractId,
        name: data.collectionContractName
      };
    } else if (item.prop === 'collectionServiceName') {
      let contract_object =
        collectionService?.value && collectionService?.value.length
          ? collectionService?.value.find(el => el.value === data.collectionServiceId)
          : void 0;
      obj[item.prop] = contract_object?.name;
    } else {
      obj[item.prop] = data[item.prop];
    }
  });
  companyList.value = [
    {
      value: data.companyId,
      name: data.companyName
    }
  ];
  dataList.value = data.payConMultipartyInfoVoList.filter(
    item => item.dicMultipartyTypeCode !== '1' && item.dicMultipartyTypeCode !== '2'
  );

  form.value.setValues(obj);
  startPrint();
};

const setCollectionService = async (contractId, init = true) => {
  let res = await queryCollectionServiceListp({ contractId });
  collectionService.value = res.list.map(item => {
    let { id, dicServiceSourceName, dicServiceContentName, includeTaxAmount } = item;
    return {
      name: `${dicServiceSourceName || '--'}-${dicServiceContentName || '--'}(${includeTaxAmount || '--'}元)`,
      value: id
    };
  });
  if (init) {
    form.value.setValues({
      collectionServiceId: void 0
    });
  }
};

const startPrint = () => {
  setTimeout(() => {
    printFunc();
  }, 500);
};
</script>
<style scoped>
* {
  box-sizing: border-box;
}
.print {
  padding: 10px;
}
:deep(.group-title .title:after) {
  width: 0;
}
:deep(.group-title .title) {
  color: var(--el-text-color);
  padding-left: 0;
}
</style>
