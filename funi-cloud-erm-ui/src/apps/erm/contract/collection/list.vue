<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-02 10:13:54
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-30 11:57:05
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/collection/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
    <AuditDialog v-model="showDialog" :callback="dialogCallBack" />
    <ChangeEmployee ref="employeeRef" @output="() => listPage.reload({ resetPage: false })" />
    <ChangeContRelatedParties ref="relatedPartiesRef" @output="() => listPage.reload({ resetPage: false })" />
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref } from 'vue';
import { userCollectionColumns, useBtnsConfig } from './../hooks/collection/collection.jsx';
import { queryCollectionContractListHttp, collectionContractDelHttp } from './../hooks/collection/api.js';
import { ElNotification } from 'element-plus';
import { apiUrl, creatBusHttp } from '@/apps/erm/contract/hooks/collection/api.js';
import AuditDialog from '@/apps/erm/component/auditDialog/index.vue';
import ChangeEmployee from '../component/collection/changeEmployee.vue';
import ChangeContRelatedParties from '../component/collection/changeContRelatedParties.vue';
import { deleteBusiness, submitBusiness } from '@/apps/erm/config/business.js';
import { useRouter } from 'vue-router';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
const router = useRouter();
const listPage = ref();
const employeeRef = ref();
const relatedPartiesRef = ref();
let query = ref();
const lodaData = async (params, queryParams) => {
  rowData.value = void 0;
  query.value = queryParams;
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryCollectionContractListHttp({ ...params, ...queryParams });
  return data;
};
const rowData = ref(void 0);
const showDialog = ref(false);
const dialogCallBack = ref(void 0);
onMounted(() => {});
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig({
          addFunc,
          changeFunc,
          endFunc,
          exportFun,
          cancelFunc,
          changeEmployee,
          changeContRelatedParties,
          hasValid: rowData.value?.row?.dicContractStatusCode === '1',
          selected: !!rowData.value?.row.id
        }),
        // searchConfig: useSearchConfig(),
        lodaData: lodaData,
        fixedButtons: true,
        reloadOnActive: true,
        checkOnRowClick: true,
        columns: userCollectionColumns({ seeDateils, auditFunc, editFunc, delFunc, infoFuinc }),
        on: {
          rowClick: e => {
            row_click(e);
          }
        }
      }
    }
  ];
});

const seeDateils = row => {
  router.push({
    name: 'ermContractCollectionInfo',
    query: {
      title: row.contractName || '收款合同',
      bizName: '详情',
      type: 'info',
      id: row.dicCollContractDoingBusCode ? row.lastId || row.id : row.id,
      tab: ['收款合同', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
const editFunc = row => {
  let eum = {
    ERM_COLLECTION_CONTRACT_ADD: 'add_edit',
    ERM_COLLECTION_CONTRACT_CHANGE: 'change_edit',
    ERM_COLLECTION_CONTRACT_TERMINATE: 'end_edit',
    ERM_COLLECTION_CONTRACT_CANCEL: 'cancel_edit'
  };
  router.push({
    name: 'ermContractCollectionAdd',
    query: {
      bizName: '编辑',
      type: eum[row.dicBusinessTypeCode],
      tab: ['收款合同', row.contractName || '', '编辑'].join('-'),
      id: row.id,
      title: row.contractName || '收款合同',
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
const infoFuinc = row => {
  router.push({
    name: 'ermContractCollectionInfo',
    query: {
      title: row.contractName || '收款合同',
      bizName: '审核',
      type: 'info',
      id: row.id,
      tab: ['收款合同', row.contractName || '', '业务详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
const auditFunc = row => {
  router.push({
    name: 'ermContractCollectionInfo',
    query: {
      title: row.contractName || '收款合同',
      bizName: '审核',
      type: 'audit',
      id: row.id,
      tab: ['收款合同', row.contractName || '', '审核'].join('-')
      // businessId: row.businessId,
      // dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
const delFunc = async row => {
  // await deleteBusiness(row.businessId);
  await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  listPage.value.reload({ resetPage: false });
};
const addFunc = () => {
  router.push({
    name: 'ermContractCollectionAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: '收款合同-新建'
    }
  });
};

const changeFunc = async () => {
  let { row } = rowData.value;
  await creatBusHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_COLLECTION_CONTRACT_CHANGE'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractCollectionAdd',
      query: {
        bizName: '新建',
        type: 'change_add',
        tab: ['收款合同', row.contractName, '变更'].join('-'),
        id: row.id,
        title: row.contractName || '收款合同变更',
        reason
      }
    });
  };
  showDialog.value = true;
};

const endFunc = async () => {
  let { row } = rowData.value;
  await creatBusHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_COLLECTION_CONTRACT_TERMINATE'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractCollectionAdd',
      query: {
        bizName: '新建',
        type: 'end_add',
        tab: ['收款合同', row.contractName, '终止'].join('-'),
        id: row.id,
        title: row.contractName || '收款合同终止',
        reason
      }
    });
  };
  showDialog.value = true;
};

const cancelFunc = async () => {
  let { row } = rowData.value;
  await creatBusHttp({
    id: row?.id,
    dicBusinessTypeCode: 'ERM_COLLECTION_CONTRACT_CANCEL'
  });
  dialogCallBack.value = reason => {
    router.push({
      name: 'ermContractCollectionAdd',
      query: {
        bizName: '新建',
        type: 'cancel_add',
        tab: ['收款合同', row.contractName, '作废'].join('-'),
        id: row.id,
        title: row.contractName || '收款合同作废',
        reason
      }
    });
  };
  showDialog.value = true;
};

const exportFun = () => {
  expotrFunction({ url: apiUrl.queryPaymentContractListExport, params: query.value, FileName: '收款合同' });
};

const row_click = e => {
  rowData.value = e;
};
const changeEmployee = () => {
  employeeRef.value.show(rowData.value?.row);
};
const changeContRelatedParties = () => {
  relatedPartiesRef.value.show(rowData.value?.row);
};
</script>
