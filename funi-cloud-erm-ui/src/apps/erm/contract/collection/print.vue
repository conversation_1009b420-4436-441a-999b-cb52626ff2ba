<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-10-09 14:49:34
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-30 12:47:00
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/collection/print.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->


<template>
  <div v-loading="loading">
    <div class="print" :id="uuId">
      <div style="text-align: center">
        <h2>{{ contractName }}</h2>
      </div>
      <GroupTitle title="基本信息" />
      <funi-form-print :inline="false" :schema="schema" @get-form="setForm" :col="3" :border="true" />
      <p style="page-break-after: always"></p>
      <GroupTitle title="合同相关方" />
      <PrintTable :columns="columns" :dataList="dataList" />
      <template v-if="dicBusinessTypeCode == 'ERM_COLLECTION_CONTRACT_TERMINATE'">
        <GroupTitle title="调整合同金额" />
        <funi-form-print :inline="false" :schema="aschema" @get-form="e => (from01 = e)" :col="3" :border="true" />
        <p style="page-break-after: always"></p>
        <GroupTitle title="调整服务金额" />
        <PrintTable :columns="columns2" :thTd="thTd" :dataList="sAmountData" />
        <p style="page-break-after: always"></p>
        <GroupTitle title="服务明细" />
        <PrintTable :columns="columns3" :tbTd="tbTd" :dataList="cData" />
        <p style="page-break-after: always"></p>
      </template>

      <PrintAuditLog ref="printAuditLog" :businessId="businessId" />
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onMounted, unref, nextTick } from 'vue';
import { useFormSchema, adjustSchema } from './../hooks/collection/baseInfo.jsx';
import { collectionContractInfoHttp } from './../hooks/collection/api.js';
import { Print } from '@/apps/erm/hooks/Print.js';
import funiFormPrint from '@/apps/erm/component/printForm/index.vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import PrintAuditLog from '@/apps/erm/component/printAuditLog/index.vue';
import PrintTable from '@/apps/erm/component/printTable/index.vue';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
import { useRoute, useRouter } from 'vue-router';

const companyList = ref([]);
const route = useRoute();
const router = useRouter();
const businessId = ref();
const form = ref();
const from01 = ref();
const dataList = ref([]);
const printAuditLog = ref();
const loading = ref(false);
const contractName = ref('');
const multiTab = useMultiTab();
const dicBusinessTypeCode = ref('');
const collectionAdjust = ref();
const sAmountData = ref([]);
const cData = ref([]);
const thTd = ref({
  fontSize: '10px',
  fontWeight: 'auto',
  color: '#000000',
  padding: '8px'
});
const tbTd = ref({
  fontSize: '10px',
  fontWeight: 'auto',
  color: 'var(--el-text-color)',
  padding: '8px'
});

const setForm = e => {
  form.value = e;
};
const id = route.query.id || 'c248b322-864d-4139-9bbd-ff52a887acb9';
const uuId = 'print_' + $utils.guid();
const schema = computed(() => {
  return useFormSchema({
    isEdit: false,
    type: 'info',
    companyList: companyList.value
  });
});
const aschema = computed(() => {
  return adjustSchema({
    isEdit: false,
    type: 'info'
  });
});
const printCallback = () => {
  multiTab.closeCurrentPage();

  router.push({
    name: 'ermContractCollectionInfo',
    query: {
      title: unref(contractName) || '收款合同',
      bizName: '详情',
      type: 'info',
      id: unref(id),
      tab: ['收款合同', unref(contractName), '详情'].join('-')
    }
  });
};

const printFunc = () => {
  Print(document.querySelector('#' + uuId), {
    type: 'html',
    targetStyles: ['*'],
    scanStyle: false,
    maxWidth: 5000,
    callback: printCallback
  });
};
const columns = [
  {
    title: '签约方',
    dataIndex: 'dicMultipartyTypeName',
    width: '40'
  },
  {
    title: '签约方名称',
    dataIndex: 'customerName',
    width: '60'
  }
];
const columns2 = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '4'
  },
  {
    title: '合同编号',
    dataIndex: 'contractSn',
    width: '4'
  },
  {
    title: '来源',
    dataIndex: 'dicServiceSourceName',
    width: '4'
  },
  {
    title: '服务内容',
    dataIndex: 'dicServiceContentName',
    width: '4'
  },

  {
    title: '是否开放性条款',
    dataIndex: 'isOpenClause',
    width: '4'
  },
  {
    title: '签约类型',
    dataIndex: 'dicSignTypeName',
    width: '4'
  },
  {
    title: '确收方式',
    dataIndex: 'dicConfirmModeName',
    width: '4'
  },
  {
    title: '确收总金额（元）',
    dataIndex: 'confirmReceipt',
    width: '4'
  },
  {
    title: '调整后确收金额（元）',
    dataIndex: 'sufConfirmAmount',
    width: '4'
  },
  {
    title: '收款总金额（元）',
    dataIndex: 'accumulatReceived',
    width: '4'
  },
  {
    title: '调整后收款金额（元）',
    dataIndex: 'sufCollectionAmount',
    width: '4'
  },
  {
    title: '剩余未收金额（元）',
    dataIndex: 'surplusReceived',
    width: '4'
  },
  {
    title: '已开票金额（元）',
    dataIndex: 'invoiceAmount',
    width: '4'
  },
  {
    title: '调整后已开票金额（元）',
    dataIndex: 'sufInvoiceAmount',
    width: '4'
  },
  {
    title: '剩余未开票金额（元）',
    dataIndex: 'unInvoiceAmount',
    width: '4'
  },
  {
    title: '含税金额（元）',
    dataIndex: 'includeTaxAmount',
    width: '4'
  },
  {
    title: '税率',
    dataIndex: 'taxRate',
    width: '4'
  },
  {
    title: '实际开始时间',
    dataIndex: 'actualStartTime',
    width: '4'
  },
  {
    title: '实际结束时间',
    dataIndex: 'actualEndTime',
    width: '4'
  },
  {
    title: '税额(元)',
    dataIndex: 'taxAmount',
    width: '4'
  },
  {
    title: '不含税金额(元)',
    dataIndex: 'excludeTaxAmount',
    width: '4'
  },
  {
    title: '绩效比例(%)',
    dataIndex: 'performanceProp',
    width: '4'
  },
  {
    title: '收入大类',
    dataIndex: 'dicIncomeCategoryName',
    width: '4'
  },
  {
    title: '收入小类',
    dataIndex: 'dicIncomeSubclassName',
    width: '4'
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: '4'
  }
];

const columns3 = [
  {
    title: '来源',
    dataIndex: 'dicServiceSourceName',
    width: '5.26'
  },
  {
    title: '服务内容',
    dataIndex: 'dicServiceContentName',
    width: '5.26'
  },

  {
    title: '是否开放性条款',
    dataIndex: 'isOpenClause',
    width: '5.26'
  },
  {
    title: '签约类型',
    dataIndex: 'dicSignTypeName',
    width: '5.26'
  },
  {
    title: '确收方式',
    dataIndex: 'dicConfirmModeName',
    width: '5.26'
  },
  {
    title: '含税金额(元)',
    dataIndex: 'includeTaxAmount',
    width: '5.26'
  },
  {
    title: '实际开始时间',
    dataIndex: 'actualStartTime',
    width: '5.26'
  },
  {
    title: '实际结束时间',
    dataIndex: 'actualEndTime',
    width: '5.26'
  },
  {
    title: '税率',
    dataIndex: 'taxRate',
    width: '5.26'
  },
  {
    title: '税额(元)',
    dataIndex: 'taxAmount',
    width: '5.26'
  },
  {
    title: '不含税金额(元)',
    dataIndex: 'excludeTaxAmount',
    width: '5.26'
  },
  {
    title: '绩效比例(%)',
    dataIndex: 'performanceProp',
    width: '5.26'
  },
  {
    title: '已确收(元)',
    dataIndex: 'confirmReceipt',
    width: '5.26'
  },
  {
    title: '未确收(元)',
    dataIndex: 'unconfirmReceipt',
    width: '5.26'
  },
  {
    title: '累计已收(元)',
    dataIndex: 'accumulatReceived',
    width: '5.26'
  },
  {
    title: '剩余未收(元)',
    dataIndex: 'surplusReceived',
    width: '5.26'
  },
  {
    title: '收入大类',
    dataIndex: 'dicIncomeCategoryName',
    width: '5.26'
  },
  {
    title: '收入小类',
    dataIndex: 'dicIncomeSubclassName',
    width: '5.26'
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: '5.26'
  }
];

onMounted(() => {
  if (id) {
    getCollectionInfo();
  }
});

const getCollectionInfo = async () => {
  let data = void 0;
  loading.value = true;
  data = await collectionContractInfoHttp({ collectionContractId: id });
  contractName.value = data.contractName;
  businessId.value = data.businessId;
  dicBusinessTypeCode.value = data.dicBusinessTypeCode;
  await nextTick();
  await unref(printAuditLog)
    .getInfo()
    .finally(() => {
      loading.value = false;
    });

  // TODO 写入基本信息表单数据
  let obj = {};
  collectionAdjust.value = data.collectionAdjust;
  sAmountData.value = (data.serviceAdjustList || []).map((item, index) => ({
    ...item,
    index: index + 1,
    isOpenClause: item.isOpenClause ? '是' : '否'
  }));
  cData.value = data.collectionServiceList.map(item => {
    return {
      ...item,
      isOpenClause: item.isOpenClause ? '是' : '否'
    };
  });
  let filed = ['projectObj', 'employeeObj', 'otherContractSealObj'];
  schema.value.forEach(item => {
    if (filed.indexOf(item.prop) > -1) {
      let str = item.prop.slice(0, -3);
      obj[item.prop] = {
        id: data[`${str}Id`],
        name: data[`${str}Name`]
      };
    } else if (item.prop === 'partyA' || item.prop === 'partyAName') {
      let o = data?.multipartyInfoList?.filter(el => {
        return el.dicMultipartyTypeCode === '1';
      });
      if (o && o.length) {
        obj['partyA'] = {
          id: o[0].customerId,
          name: o[0].customerName
        };
        obj['partyAName'] = o[0].customerName;
      }
    } else if (item.prop === 'partyB' || item.prop === 'partyB_object') {
      let o = data?.multipartyInfoList?.filter(el => {
        return el.dicMultipartyTypeCode === '2';
      });
      if (o && o.length) {
        obj['partyB'] = o[0].customerId;
        obj['partyB_object'] = {
          name: o[0].customerName,
          id: o[0].customerId
        };
      }
    } else if (item.prop === 'dicSealTypeCode') {
      obj[item.prop] = data[item.prop] ? data[item.prop].split(',') : data['dicSealTypeCodeList'];
    } else {
      obj[item.prop] = data[item.prop];
    }
  });
  companyList.value = [
    {
      value: data.companyId,
      name: data.companyName
    }
  ];
  dataList.value = data.multipartyInfoList.filter(
    item => item.dicMultipartyTypeCode !== '1' && item.dicMultipartyTypeCode !== '2'
  );
  form.value.setValues(obj);
  if (from01.value) {
    from01.value.setValues(collectionAdjust.value);
  }
  startPrint();
};
const startPrint = () => {
  setTimeout(() => {
    printFunc();
  }, 500);
};
</script>
<style scoped>
* {
  box-sizing: border-box;
}
.print {
  padding: 10px;
}
:deep(.group-title .title:after) {
  width: 0;
}
:deep(.group-title .title) {
  color: var(--el-text-color);
  padding-left: 0;
}
</style>
