<template>
  <div>
    <GroupTitle title="开票申请表"></GroupTitle>
    <funi-form
      :schema="schema"
      @get-form="setForm"
      :rules="{
        receivePersonId: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === '' || value === null || value === undefined || !value.id) {
                callback(new Error('必填'));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ]
      }"
      :col="2"
      :border="false"
    ></funi-form>

    <GroupTitle title="开票明细" isSymbol>
      <template v-if="isEdit">
        <el-button v-if="['add'].includes(type)" type="primary" @click="addFunc('1')">新增</el-button>
        <el-button type="primary" @click="addFunc('2')">添加客商</el-button>
      </template>
    </GroupTitle>
    <funi-curd :rowSelection="false" :columns="columns" :data="dataList" :pagination="false">
      <template #a>
        <div class="musterW"><span>*</span>客户名称(甲方)</div>
      </template>
      <template #b>
        <div class="musterW"><span>*</span>开票金额(元)</div>
      </template>
      <template #c>
        <div class="musterW"><span>*</span>发票抬头</div>
      </template>
      <template #d>
        <div class="musterW"><span>*</span>本次开票金额(元)</div>
      </template>
      <template #e>
        <div class="musterW"><span>*</span>开票类型</div>
      </template>
    </funi-curd>
    <template v-if="['audit', 'info'].includes(type)">
      <div>
        <GroupTitle title="发票信息" isSymbol>
          <el-button v-if="['audit'].includes(type)" type="primary" @click="addFPFunc">添加</el-button>
        </GroupTitle>
        <funi-curd ref="curd1" :rowSelection="false" :columns="columns2" :data="cData" :pagination="false">
          <template #a>
            <div class="musterW"><span>*</span>开票时间</div>
          </template>
          <template #b>
            <div class="musterW"><span>*</span>发票号码</div>
          </template>
          <template #c>
            <div class="musterW"><span>*</span>发票代码</div>
          </template>
        </funi-curd>
      </div>
    </template>
    <div v-if="['info', 'audit'].includes(type) && businessId">
      <GroupTitle title="要件信息"> </GroupTitle>
      <FuniFileTable :onlyShow="type == 'info'" :params="{ businessId }"></FuniFileTable>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, onMounted, computed, inject, nextTick, unref } from 'vue';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { openBusiness, submitBusiness } from '@/apps/erm/config/business.js';
import { useRouter, useRoute } from 'vue-router';
import { noConcractColumns, serveColumns } from './../../hooks/invoice/baseInfo.jsx';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';

import {
  customerInfoHttp,
  getNoContractFapiaoRequestListHttp,
  issueFapiaoApplyHttp,
  issueFapiaoApplyNewHttp,
  deleteIssueFapiaoApplyByIdsHttp
} from './../../hooks/invoice/api.js';
import { ElNotification } from 'element-plus';
const loadingStatus = inject('loadingStatus');
const props = defineProps({
  ...baseInfoProps,
  contract: { type: Object, default: {} },
  sn: String
});
const router = useRouter();
const route = useRoute();
const taxRate = ref('');
const dataList = ref([]);
const contactRef = {};
const form = ref(null);
const infoData = ref(void 0);
const emit = defineEmits(['submitEnd', 'updateID', 'infoUpdate', 'otherUpdate']);
const cData = ref([{ invoicingTime: void 0, fapiaoSn: void 0, fapiaoNo: void 0, uuId: $utils.guid() }]);
const dicFapiaoAppDoingBusCode = ref('1');
const dicBusinessTypeCode = ref('');

let { oldApplySn, oldFapiaoAmount } = route.query;

const setForm = e => {
  form.value = e;
};
const iptChange = (index, key, value) => {
  let keyName = props.isEdit || ['cancel_add', 'cancel_edit'].includes(props.type) ? dataList : cData;
  if (key === 'firstCustomerObj') {
    keyName.value[index]['firstCustomerId'] = value?.id;
    keyName.value[index]['firstCustomerName'] = value?.customerName;
    value?.id && getCustomerInfo(value?.id, index);
    for (let _key in keyName.value[index]) {
      if (
        ![
          'fapiaoAmount',
          'currentFapiaoAmount',
          'dicFapiaoTypeCode',
          'taxAmount',
          'taxRate',
          'firstCustomerObj',
          'firstCustomerId',
          'firstCustomerName'
        ].includes(_key)
      ) {
        keyName.value[index][_key] = void 0;
      }
    }

    //
  }
  if (key === 'invoiceTitleObj') {
    let obj = keyName.value[index].customerKaipiaoObjs.find(item => item.taxIdNumber == value);
    keyName.value[index]['invoiceTitle'] = obj?.invoiceHeader;
    keyName.value[index]['taxIdNumber'] = value;
    for (let _key in keyName.value[index]) {
      if (['openBankName', 'openAccountNumber', 'openAccountNumber'].includes(_key)) {
        keyName.value[index][_key] = void 0;
      }
    }
  }

  if (key === 'openAccountNumber') {
    let list = keyName.value[index]?.customerKaipiaoObjs
      ? keyName.value[index]?.customerKaipiaoObjs.filter(item => item.taxIdNumber === keyName.value[index]?.taxIdNumber)
      : [];
    let obj = list.find(item => item.bankAccountNumber === value);
    keyName.value[index]['openAccountName'] = obj.bankAccountName;
    keyName.value[index]['openBankName'] = obj.bankName;
    keyName.value[index]['bankAccountId'] = obj.bankAccountId;
  }
  if (key === 'fapiaoAmount') {
    keyName.value[index]['currentFapiaoAmount'] = value;
  }

  keyName.value[index][key] = value;
};

const columns = computed(() => {
  return noConcractColumns({ delFunc, iptChange, contactRef, isEdit: props.isEdit, type: props.type });
});

const columns2 = computed(() => {
  return serveColumns({
    type: props.type,
    dicFapiaoAppDoingBusCode: dicFapiaoAppDoingBusCode.value,
    iptChange,
    contactRef,
    delFPFunc
  });
});
const schema = computed(() => {
  return [
    {
      label: '收款合同名称',
      prop: 'contractName'
    },
    {
      label: '状态',
      prop: 'status'
    },
    {
      label: '归属公司',
      prop: 'companyName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '服务项目',

      prop: 'projectName'
    },
    {
      label: '负责人',

      prop: 'employeeName'
    },
    {
      label: '领取人',
      component: ({ formModel }) =>
        props.type === 'audit' && dicFapiaoAppDoingBusCode.value == '1' ? (
          <InfiniteSelect
            api={ermGlobalApi.queryEmployeeList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'employeeName',
              id: 'id'
            }}
          ></InfiniteSelect>
        ) : (
          <span>{formModel?.receivePersonId?.name || '--'}</span>
        ),
      prop: 'receivePersonId',
      props: {
        placeholder: '请选择领取人'
      },
      hidden: ({ formModel }) => {
        return !['audit', 'info'].includes(props.type);
      }
    }
  ];
});

onMounted(() => {
  setFormValue({
    ...props.contract,
    status: '草稿'
  });

  if (props.id || props.sn) {
    getInvoiceInfo();
  }
});

const setFormValue = obj => {
  let {
    contractName,
    companyName,
    deptName,
    employeeName,
    projectName,
    taxRate: taxRateNum,
    status,
    receivePersonId,
    receivePersonName
  } = obj;
  taxRate.value = taxRateNum;
  form.value.setValues({
    contractName,
    companyName,
    deptName,
    employeeName,
    status,
    projectName,
    receivePersonId: ['audit', 'info'].includes(props.type)
      ? {
          id: receivePersonId,
          name: receivePersonName
        }
      : void 0
  });
};

const getInvoiceInfo = async () => {
  loadingStatus.value.status = true;
  let {
    issueFapiaoApplyDetailVo: data,
    fapiaoDetailRequestList,
    fapiaoInfoList
  } = await issueFapiaoApplyHttp({
    issueFapiaoApplyId: props.id,
    issueFapiaoApplySn: props.sn
  }).finally(() => {
    loadingStatus.value.status = false;
  });
  infoData.value = data;

  /**@description 设置业务编码**/
  dicBusinessTypeCode.value = data?.dicBusinessTypeCode;

  if (dicBusinessTypeCode.value === 'ERM_ISSUE_FAPIAO_APPLY_REFUND') {
    oldApplySn = data?.oldApplySn;
    oldFapiaoAmount = data?.oldFapiaoAmount;
  }

  if (['cancel_add'].includes(props.type)) {
    dicBusinessTypeCode.value == 'ERM_ISSUE_FAPIAO_APPLY_REFUND';
  }

  dataList.value = (fapiaoDetailRequestList || []).map((item, index) => {
    let { fapiaoAmount, currentFapiaoAmount, dicFapiaoTypeCode, dicFapiaoTypeName } = item;
    if (['cancel_add'].includes(props.type)) {
      fapiaoAmount = void 0;
      currentFapiaoAmount = void 0;
      dicFapiaoTypeCode = void 0;
      dicFapiaoTypeName = void 0;
    }
    if (item.firstCustomerId) {
      getCustomerInfo(item.firstCustomerId, index);
    }
    return {
      ...item,
      fapiaoAmount,
      currentFapiaoAmount,
      dicFapiaoTypeCode,
      dicFapiaoTypeName,
      firstCustomerObj: {
        name: item.firstCustomerName,
        id: item.firstCustomerId
      },
      invoiceTitleObj: {
        name: item.invoiceTitle,
        id: item.taxIdNumber
      }
      // openAccountName: props.isEdit ? item.bankAccountNumber || item.bankAccountName : item.bankAccountName
    };
  });

  console.log('dataList', dataList.value);

  setFormValue({
    ...data,

    status: data.dicFapiaoAppStatusName
  });
  dicFapiaoAppDoingBusCode.value = data.dicFapiaoAppDoingBusCode;
  cData.value = (fapiaoInfoList || []).map(item => {
    let { invoicingTime, fapiaoSn, fapiaoNo } = item;

    return {
      invoicingTime: invoicingTime || '',
      fapiaoSn: fapiaoSn || '',
      fapiaoNo: fapiaoNo || '',
      uuId: $utils.guid()
    };
  });
  emit('infoUpdate', {
    ...data,
    id: ['cancel_add', 'add'].includes(props.type) ? void 0 : data.id
  });
  await nextTick();
  if (!props.isEdit) {
    setTimeout(() => {
      form.value.clearValidate(['receivePersonId']);
    }, 800);
  }
};

const getCustomerInfo = async (customerId, index) => {
  let { customerVo } = await customerInfoHttp({
    customerId
  });
  dataList.value[index]['customerKaipiaoObjs'] = customerVo?.customerKaipiaoObjs || [];
  console.log('customerVo', customerVo);
};

const addFunc = key => {
  if (key === '1') {
    dataList.value.push({
      uuId: $utils.guid(),
      taxRate: taxRate.value
    });
  }
  if (key === '2') {
    router.push({
      name: 'erm_supplier_add',
      query: {
        bizName: '新建',
        type: 'add',
        tab: '客商管理-新建'
      }
    });
  }
};

/**
 * @description 新增发票
 * **/

const addFPFunc = () => {
  cData.value.push({
    invoicingTime: '',
    fapiaoSn: '',
    fapiaoNo: '',
    uuId: $utils.guid()
  });
};

/**
 * @description 删除某一项开票
 * **/

const delFPFunc = index => {
  cData.value.splice(index, 1);
};

const verificationTableForm = obj => {
  let v_l = [];
  for (let key in obj) {
    let v = obj[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};

const delFunc = index => {
  dataList.value.splice(index, 1);
};

const save = async (list = [], bool = false) => {
  let promiseList = await Promise.allSettled(
    list.map(item => {
      return openBusiness('ERM_ISSUE_FAPIAO_APPLY_ADD', item, void 0, '开票申请新增');
    })
  );
  let errIndex = [];
  let fulfilledList = promiseList
    .filter((item, index) => {
      let flag = item.status === 'fulfilled';
      if (!flag) {
        errIndex.push(index);
      }
      return flag;
    })
    .map(item => item.value);

  dataList.value = dataList.value.filter((item, index) => errIndex.includes(index));
  let submitPromiseList = await Promise.allSettled(
    fulfilledList.map(item => {
      let info = item?.businessData.issueFapiaoApplyDetailVo;
      let businessName = [info?.companyName || '', info?.deptName || '', info?.contractName || ''].join('-');
      return submitBusiness(
        'ERM_ISSUE_FAPIAO_APPLY_ADD',
        item?.businessInstanceInfo?.businessId,
        'SUBMIT',
        businessName
      );
    })
  );
  let errorList = [];
  let submitFulfilledList = submitPromiseList.filter((item, index) => {
    let flag = item.status === 'fulfilled';
    if (!flag) {
      errorList.push(fulfilledList[index]?.value?.businessData?.issueFapiaoApplyDetailVo?.id);
    }
    return flag;
  });
  if (errorList && errorList.length) {
    await deleteIssueFapiaoApplyByIdsHttp({ kaipiaoIds: errorList });
  }

  ElNotification({
    title: '通知',
    message: `一共提交${list.length}条数据${
      submitFulfilledList.length === list.length
        ? '，全部成功。'
        : `，提交成功${submitFulfilledList.length}条，提交失败${list.length - submitFulfilledList.length}条。`
    }`,
    dangerouslyUseHTMLString: true,
    type: 'success'
  });
  if (list.length === fulfilledList.length) {
    emit('submitEnd');
  }
  return Promise.resolve({});
};

const authFun = async type => {
  let isAudit = true;
  let isValid = true;
  let { receivePersonId } = form.value.getValues();
  if (type == 'ts') {
    isAudit = false;
  } else {
    let validate = type !== 'notValidate' ? await form.value.validateField(['receivePersonId']) : { isValid: true };
    let tableValidate = type !== 'notValidate' ? verificationTableForm(contactRef) : true;
    if (!dataList.value?.length && type !== 'notValidate') {
      ElNotification({
        title: '提示',
        message: '发票信息必填',
        type: 'warning'
      });
      tableValidate = false;
    }
    isValid = validate.isValid && tableValidate;
    if (!isValid) {
      return Promise.reject({});
    }
    return Promise.resolve({
      fapiaoInfoRequestList: unref(cData),
      fapiaoDetailRequestList: unref(dataList),
      id: props.id,
      receivePersonId: receivePersonId?.id,
      receivePersonName: receivePersonId?.name,
      isAudit,
      dicFapiaoApplyTypeCode: 2
    });
  }
  try {
    loadingStatus.value.status = true;
    await issueFapiaoApplyNewHttp({
      fapiaoInfoRequestList: unref(cData),
      fapiaoDetailRequestList: unref(dataList),
      id: props.id,
      receivePersonId: receivePersonId.id,
      receivePersonName: receivePersonId.name,
      isAudit,
      dicFapiaoApplyTypeCode: 2,
      businessConfigCode: 'ERM_ISSUE_FAPIAO_APPLY_ADD'
    }).finally(() => {
      loadingStatus.value.status = false;
    });
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
    return Promise.resolve({});
  } catch {
    return Promise.reject({});
  }
};
const save_next = async (list, bool) => {
  if (!props.id) {
    let resData = await openBusiness(
      'ERM_ISSUE_FAPIAO_APPLY_REFUND',
      {
        ...infoData.value,
        dicBusinessTypeCode: 'ERM_ISSUE_FAPIAO_APPLY_REFUND',
        fapiaoDetailRequestList: list,
        lastId: props.lastId || void 0,
        id: void 0,
        oldApplySn,
        fapiaoAmount: list[0].fapiaoAmount,
        oldFapiaoAmount,
        businessId: void 0,
        businessNo: void 0,
        businessNode: void 0,
        businessNode: void 0,
        dicDataStatusCode: void 0,
        dicFapiaoAppStatusCode: void 0,
        takeEffectTime: void 0,
        applySn: void 0,
        isSubmit: bool
      },
      props.reason,
      '开票申请退票'
    );
    let { businessData } = resData;

    let { applySn, id, businessId } = businessData?.issueFapiaoApplyDetailVo || {};
    infoData.value = {
      ...(businessData?.issueFapiaoApplyDetailVo || {}),
      id,
      businessId: businessId || props.businessId
    };
    emit('infoUpdate', {
      ...infoData.value
    });
  } else {
    await issueFapiaoApplyNewHttp({
      ...infoData.value,
      fapiaoDetailRequestList: list,
      oldApplySn,
      id: props.id,
      fapiaoAmount: list[0].fapiaoAmount,
      dicBusinessTypeCode: dicBusinessTypeCode.value,
      oldFapiaoAmount,
      isSubmit: bool
    });

    if (!bool) {
      ElNotification({
        title: '暂存成功',
        type: 'success'
      });
    }
  }
};
const ts = () => {
  return nextStep(void 0, false);
};
const nextStep = async (object, bool = true) => {
  let flage = true;
  if (bool) {
    flage = verificationTableForm(contactRef);
  }

  if (flage) {
    await save_next(dataList.value, bool);
    return Promise.resolve({});
  }
  return Promise.reject();
};
const submit = async () => {
  let flage = verificationTableForm(contactRef);
  if (flage) {
    let list = await getRequestData();
    if (!list && !list?.length) {
      return Promise.reject();
    }
    return await save(list);
  }
  return Promise.reject();
};

const getRequestData = async () => {
  if (!dataList.value.length) {
    ElNotification({
      title: '提示',
      message: '开票明细必填',
      type: 'warning'
    });
    return false;
  }
  let { list } = await getNoContractFapiaoRequestListHttp({
    ...props.contract,
    fapiaoDetailRequestList: dataList.value,
    dicFapiaoApplyTypeCode: 2
  });
  return list;
};

const getForm = () => {
  return form.value;
};

const getPrintData = () => {
  return {
    companyList: [],
    deptList: [],
    list: cData.value,
    fapiaoDetailRequestList: dataList.value
  };
};
defineExpose({
  ts,
  nextStep,
  submit,
  authFun,
  getForm,
  getPrintData
});
</script>

<style scoped>
.musterW {
  overflow: hidden;
  width: 100%;
  height: 26px;
  padding-left: 5px;
}

.musterW > span {
  color: red;
  display: inline-block;
  /* margin: 3px 3px 0 0; */
  position: relative;
  top: 4px;
  right: 3px;
}
</style>
