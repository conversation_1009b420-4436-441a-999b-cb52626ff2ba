<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-03 11:11:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-01 10:42:21
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/component/invoice/baseInfo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <GroupTitle title="开票申请表"></GroupTitle>
    <funi-form :schema="schema" @get-form="setForm" :rules="rules" :col="3" :border="false"></funi-form>
    <GroupTitle title="服务内容" isSymbol>
      <div v-if="isEdit">
        <el-button type="primary" @click="addFunc">添加</el-button>
      </div>
    </GroupTitle>
    <funi-curd :rowSelection="false" :columns="sColumns" :data="csData" :pagination="false">
      <template #a>
        <div class="musterW"><span>*</span>本次开票金额(元)</div>
      </template>
      <template #b>
        <div class="musterW"><span>*</span>收入确认表</div>
      </template>
    </funi-curd>
    <template v-if="['audit', 'info'].includes(type)">
      <div>
        <GroupTitle title="发票信息" isSymbol>
          <el-button v-if="['audit'].includes(type)" type="primary" @click="addFPFunc">添加</el-button>
        </GroupTitle>
        <funi-curd ref="curd1" :rowSelection="false" :columns="columns" :data="cData" :pagination="false">
          <template #a>
            <div class="musterW"><span>*</span>开票时间</div>
          </template>
          <template #b>
            <div class="musterW"><span>*</span>发票号码</div>
          </template>
          <template #c>
            <div class="musterW"><span>*</span>发票代码</div>
          </template>
        </funi-curd>
      </div>
    </template>

    <div v-if="['info', 'audit'].includes(type) && businessId">
      <GroupTitle title="要件信息"> </GroupTitle>
      <FuniFileTable :onlyShow="type == 'info'" :params="{ businessId }"></FuniFileTable>
    </div>
  </div>
  <ChooseCollection ref="chooseModal" @exportArray="setCollection"></ChooseCollection>
</template>
<script setup lang="jsx">
import { ref, computed, inject, nextTick, watch, onMounted, unref, onActivated } from 'vue';
import { useFormSchema, useRule, serveColumns, serveColumns_ } from './../../hooks/invoice/baseInfo.jsx';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import {
  baseInfoProps,
  queryCollectionServiceListByContractIdHttp,
  queryIncomeConfirmInfoByServiceIdHttp
} from '@/apps/erm/config/config.jsx';
import { openBusiness } from '@/apps/erm/config/business.js';
import { collectionContractInfoHttp } from './../../hooks/collection/api.js';
import {
  customerInfoHttp,
  issueFapiaoApplyNewHttp,
  issueFapiaoApplyHttp,
  queryCustomerListNoPageHttp
} from './../../hooks/invoice/api.js';
import { ElNotification } from 'element-plus';
import ChooseCollection from './modal/chooseCollection.vue';
const fapiaoAmountSum = ref(0);
const form = ref();
const emit = defineEmits(['updateID', 'infoUpdate', 'otherUpdate']);
const loadingStatus = inject('loadingStatus');
import { useRouter, useRoute } from 'vue-router';
const props = defineProps({
  ...baseInfoProps,
  reason: String,
  cId: String,
  cName: String,
  sn: String
});
const collectionInfo = ref({});
const customerInfo = ref({});
const companyList = ref([]);
const deptList = ref([]);
const contractId = ref();
const serviceList = ref([]);
const incomeContractList = ref([]);
const contactRef = {};
const dicFapiaoAppDoingBusCode = ref('1');
const cData = ref([{ invoicingTime: void 0, fapiaoSn: void 0, fapiaoNo: void 0, uuId: $utils.guid() }]);
const csData = ref([]);
const chooseModal = ref();
const setForm = e => (form.value = e);
let _contactRef = {};
const customerKaipiaoObjs = ref([]);
const infoData = ref({});
const router = useRouter();
const route = useRoute();
let kaipiaoObjId = void 0;
let bankAccountId = void 0;
const fapiaoDetailRequestList = ref([]);
const dicBusinessTypeCode = ref('');
const dicFapiaoTypeCode = ref('');
let queryCustomerLis = [];
let { oldApplySn, oldFapiaoAmount } = route.query;

let classType = {
  add: {
    code: 'ERM_ISSUE_FAPIAO_APPLY_ADD',
    name: '开票申请新增'
  },
  cancel_add: {
    code: 'ERM_ISSUE_FAPIAO_APPLY_REFUND',
    name: '开票申请退票'
  }
};

onMounted(() => {
  if (props.id || props.sn) {
    getInvoiceInfo();
  }
  if (props.type == 'add') {
    setDefaultContract();
  }
});

/**
 * @description 新增发票
 * **/

const addFPFunc = () => {
  cData.value.push({
    invoicingTime: '',
    fapiaoSn: '',
    fapiaoNo: '',
    uuId: $utils.guid()
  });
};

/**
 * @description 删除某一项开票
 * **/

const delFPFunc = index => {
  cData.value.splice(index, 1);
};

const setDefaultContract = () => {
  if (props.cId && props.cName) {
    unref(form).setValues({
      contractObj: {
        id: props.cId,
        name: props.cName
      }
    });
    getcontractInfo({ id: props.cId }, false);
  }
};
const rules = computed(() => useRule(props.isEdit, props.type, fapiaoAmountSum.value, dicFapiaoTypeCode.value));
const sColumns = computed(() => {
  _contactRef = {};
  return serveColumns_({
    isEdit: props.isEdit,
    iptChange: _iptChange,
    contactRef: _contactRef,
    delFunc,
    type: props.type
  });
});
const _iptChange = async (index, key, e) => {
  csData.value[index][key] = e;
  if (key == 'fapiaoAmount') {
    fapiaoAmountSum.value =
      csData.value && csData.value.length
        ? $utils.sum(csData.value.map(item => (item['fapiaoAmount'] ? item['fapiaoAmount'] : 0)))
        : 0;
    await nextTick();
    form.value.validateField(['fapiaoAmount']);
  }
};
const delFunc = async index => {
  csData.value.splice(index, 1);
  fapiaoAmountSum.value =
    csData.value && csData.value.length
      ? $utils.sum(csData.value.map(item => (item['fapiaoAmount'] ? item['fapiaoAmount'] : 0)))
      : 0;
  await nextTick();
  form.value.validateField(['fapiaoAmount']);
};
const schema = computed(() => {
  return useFormSchema({
    isEdit: props.isEdit,
    getcontractInfo,
    deptList: deptList.value,
    companyList: companyList.value,
    serviceList: serviceList.value,
    incomeContractList: incomeContractList.value,
    serviceChange,
    setTaxAmount,
    type: props.type,
    dicFapiaoAppDoingBusCode: dicFapiaoAppDoingBusCode.value,
    customerKaipiaoObjs: unref(customerKaipiaoObjs),
    invoiceTitleChange,
    bankAccountNameChange,
    toSupplier,
    dicBusinessTypeCode: dicBusinessTypeCode.value,
    setDicFapiaoTypeCode
  });
});

const setDicFapiaoTypeCode = async e => {
  dicFapiaoTypeCode.value = e;
  await nextTick();
  form.value.clearValidate(['openBankName', 'openAccountNumber', 'bankAccountName']);
};

const invoiceTitleChange = (e, bool = true) => {
  let param = props.isEdit ? { invoiceTitle: e } : {};
  let other = bool ? { openBankName: void 0, openAccountNumber: void 0, bankAccountName: void 0 } : {};
  form.value.setValues({
    ...param,
    taxIdNumber: e,
    other
  });
  let obj = customerKaipiaoObjs.value.find(item => item.taxIdNumber == e);
  kaipiaoObjId = obj?.id;
  form.value.clearValidate(['taxIdNumber']);
  console.log('obj', obj);
  const { dicContactProvinceName, dicContactCityName, dicContactDistrictName, contactAddress, telephone } = obj.father;

  let companyAddress =
    (dicContactProvinceName || '') +
    (dicContactCityName || '') +
    (dicContactDistrictName || '') +
    (contactAddress || '');

  form.value.setValues({
    companyAddress,
    customerTelephone: telephone
  });
};
const bankAccountNameChange = (e, bool = true) => {
  let { openBankName, openAccountNumber, openAccountName } = unref(infoData);
  if (bool) {
    // let { taxIdNumber } = form.value?.getValues() || {};
    let customerKaipiaoObj = customerKaipiaoObjs.value.find(item => {
      if (item.bankAccountNumber) {
        return item.bankAccountNumber == e;
      }
      if (item.bankAccountName) {
        return item.bankAccountName == e;
      }
      return false;
    });

    bankAccountId = customerKaipiaoObj?.bankAccountId;
    openBankName = customerKaipiaoObj?.bankName;
    openAccountNumber = customerKaipiaoObj?.bankAccountNumber;

    if (!e) {
      form.value?.setValues({
        bankAccountName: void 0
      });
    }
  } else {
    bankAccountId = unref(infoData).bankAccountId;
  }

  form.value?.setValues({
    openBankName,
    openAccountNumber
  });
  form.value?.clearValidate(['openBankName', 'openAccountNumber']);
};
const iptChange = (index, key, e) => {
  cData.value[index][key] = e;
};
const columns = computed(() => {
  return serveColumns({
    type: props.type,
    dicFapiaoAppDoingBusCode: dicFapiaoAppDoingBusCode.value,
    iptChange,
    contactRef,
    delFPFunc
  });
});

const toSupplier = () => {
  let obj = collectionInfo.value.multipartyInfoList.find(item => item.dicMultipartyTypeCode === '1');
  router.push({
    name: 'erm_supplier_add',
    query: {
      title: obj.customerName || '客商管理',
      bizName: '编辑',
      type: 'edit',
      id: obj.customerId,
      tab: ['客商管理', obj.customerName, '编辑'].join('-')
    }
  });
};

/**
 * @description 服务处理
 * **/
const addFunc = () => {
  chooseModal.value.show(
    collectionInfo.value.collectionServiceList.map(item => {
      let index = csData.value.findIndex(el => el.serviceId === item.id);

      return {
        ...item,
        serviceId: item.id,
        itemCheckId: index > -1
      };
    })
  );
};

const setCollection = async (e, bool = true) => {
  loadingStatus.value.status = true;
  let res_list = await Promise.all(e.map(item => getIncomeContractList(item.serviceId)));
  e.map((item, index) => {
    csData.value.push({
      ...item,
      incomeContractList: res_list[index] || [],
      uuId: $utils.guid()
    });
  });
  loadingStatus.value.status = false;
  fapiaoAmountSum.value =
    csData.value && csData.value.length
      ? $utils.sum(csData.value.map(item => (item['fapiaoAmount'] ? item['fapiaoAmount'] : 0)))
      : 0;
};

/**
 * 自动计算税额
 */
const setTaxAmount = () => {
  let { fapiaoAmount, taxRate } = form.value.getValues();
  // fapiaoAmount = Math.abs(fapiaoAmount);
  if (!$utils.isNil(taxRate) && !$utils.isNil(fapiaoAmount)) {
    let taxAmount = $utils.round(fapiaoAmount - $utils.round(fapiaoAmount / (1 + taxRate * 1), 2), 2);
    nextTick(() => {
      form.value.setValues({
        taxAmount
      });
      form.value.validateField(['taxAmount']);
    });
  }
};
const getInvoiceInfo = async () => {
  loadingStatus.value.status = true;
  let data = await issueFapiaoApplyHttp({ issueFapiaoApplyId: props.id, issueFapiaoApplySn: props.sn }).finally(() => {
    loadingStatus.value.status = false;
  });
  /**@description 设置业务编码**/
  dicBusinessTypeCode.value = data?.issueFapiaoApplyDetailVo.dicBusinessTypeCode;
  fapiaoDetailRequestList.value = data?.fapiaoDetailRequestList;
  if (dicBusinessTypeCode.value === 'ERM_ISSUE_FAPIAO_APPLY_REFUND') {
    oldApplySn = data?.issueFapiaoApplyDetailVo?.oldApplySn;
    oldFapiaoAmount = data?.issueFapiaoApplyDetailVo?.oldFapiaoAmount;
  }
  Object.assign(data, data?.issueFapiaoApplyDetailVo);

  //判断是否是详情
  if (['audit', 'info', 'cancel_add', 'cancel_edit'].includes(props.type)) {
    if (props.type == 'info' && !data?.fapiaoInfoList?.length) cData.value = [];
    if (data?.fapiaoInfoList?.length)
      cData.value = data.fapiaoInfoList.map(item => {
        let { invoicingTime, fapiaoSn, fapiaoNo } = item;
        return {
          invoicingTime: invoicingTime || '',
          fapiaoSn: fapiaoSn || '',
          fapiaoNo: fapiaoNo || '',
          uuId: $utils.guid()
        };
      });
  }

  let {
    incomeSn,
    id: incomeId,
    contractName: incomeName,
    includeTaxAmount: _includeTaxAmount
  } = data?.incomeConfirmInfoVo || {};
  data.incomeContractName = `${incomeSn || '--'}(${_includeTaxAmount || '--'}元)`;
  let { dicServiceSourceName, dicServiceContentName, includeTaxAmount } = data?.collectionServiceVo || {};
  data.serviceName = `${dicServiceSourceName || '--'}-${dicServiceContentName || '--'}(${includeTaxAmount || '--'}元)`;
  emit('infoUpdate', {
    ...data,
    id: ['cancel_add', 'add'].includes(props.type) ? void 0 : data.id
  });
  infoData.value = data;

  await getcontractInfo({ id: data.contractId, contractSn: data.contractSn }, false);
  setCollection(
    data.fapiaoServiceListVos
      ? data.fapiaoServiceListVos.map(item => {
          return {
            ...item,
            ...item.collectionServiceVo,
            uuId: $utils.guid(),
            id: void 0,
            fapiaoAmount: 'cancel_add' == props.type ? 0 : item.fapiaoAmount,
            oldFapiaoAmount: 'cancel_add' == props.type ? item.fapiaoAmount : item.oldFapiaoAmount
          };
        })
      : []
  );

  getIncomeContractList(data.serviceId);
  let {
    contractId,
    contractSn,
    contractName,
    applySn,
    dicFapiaoAppStatusName,
    fapiaoAmount,
    taxRate,
    taxAmount,
    dicFapiaoTypeCode,
    dicFapiaoTypeName,
    remark,
    fapiaoContent,
    // dicServiceContentName,
    serviceId,
    serviceName,
    incomeContractSn,
    incomeContractName,
    createTime,
    takeEffectTime,
    receivePersonId,
    receivePersonName,
    employeeId,
    employeeName,
    customerTelephone,
    openAccountName
  } = data;

  if (['cancel_add', 'add'].includes(props.type)) {
    applySn = void 0;
    dicFapiaoAppStatusName = void 0;
    createTime = void 0;
    takeEffectTime = void 0;
    fapiaoAmount = void 0;
    taxAmount = void 0;
    receivePersonId = void 0;
  }

  dicFapiaoAppDoingBusCode.value = data.dicFapiaoAppDoingBusCode;
  let params = {};
  if (props.isEdit) {
    params.employeeObj = {
      id: employeeId,
      name: employeeName
    };
  } else {
    params.employeeName = employeeName;
  }

  form.value.setValues({
    ...(props.isEdit
      ? {
          contractObj: {
            id: contractId,
            name: `${contractName}(${contractSn})`,
            title: contractName
          }
        }
      : {}),
    incomeContractObj: {
      id: incomeId,
      name: incomeContractName,
      title: incomeName
    },
    receivePersonId: {
      id: receivePersonId,
      name: receivePersonName
    },
    applySn,
    dicFapiaoAppStatusName,
    fapiaoAmount,
    taxRate,
    taxAmount,
    dicFapiaoTypeCode,
    dicFapiaoTypeName,
    serviceId,
    incomeContractSn,
    remark,
    fapiaoContent,
    serviceName,
    createTime,
    takeEffectTime,
    customerTelephone,
    ...params,
    oldApplySn,
    oldFapiaoAmount
    // dicServiceContentName
  });

  setDicFapiaoTypeCode(dicFapiaoTypeCode);
};

const getcontractInfo = async ({ id, contractSn }, bool = true) => {
  if (!id && !contractSn) return;
  csData.value = [];
  contractId.value = id;
  let params = {
    collectionContractId: contractSn ? void 0 : id,
    collectionContractSn: contractSn ? contractSn : void 0
  };
  getServiceList();
  loadingStatus.value.status = true;
  const data = await collectionContractInfoHttp(params).finally(() => {
    loadingStatus.value.status = false;
  });
  collectionInfo.value = data;
  let obj = {
    contractId: data.id, //跳转使用
    projectId: data.projectId
  };
  let exclude = [
    'applySn',
    'dicFapiaoAppStatusName',
    // 'contractObj',
    'invoiceTitle',
    'taxIdNumber',
    'openBankName',
    // 'bankAccountNumber',
    'openAccountNumber',
    'customerTelephone',
    'companyAddress',
    'createTime',
    'takeEffectTime',
    'receivePersonId',
    'remark'
  ];
  if (props.type !== 'add' && !bool) {
    exclude.push(...['employeeObj', 'employeeName']);
  }
  schema.value.forEach(element => {
    if (exclude.indexOf(element.prop) < 0) {
      if (element.prop == 'employeeObj') {
        obj[element.prop] = {
          id: collectionInfo.value.employeeId,
          name: collectionInfo.value.employeeName
        };
      } else if (element.prop == 'partBName' || element.prop == 'partAName') {
        let code = element.prop == 'partBName' ? '2' : '1';
        let part = collectionInfo.value.multipartyInfoList.find(item => item.dicMultipartyTypeCode === code);
        obj[element.prop] = part?.customerName;
        if (code == '1') {
          obj.firstCustomerId = part?.customerId;
          // getPartyAInfo(part.customerId, bool);
        }
      } else if (element.prop === 'contractName') {
        obj[element.prop] = `${collectionInfo.value.contractName}(${collectionInfo.value.contractSn})`;
      } else if (element.prop === 'contractObj') {
        let { contractName, contractSn } = collectionInfo.value;
        obj[element.prop] = {
          id: collectionInfo.value.id,
          name: `${contractName}(${contractSn})`,
          title: contractName
        };
      } else {
        obj[element.prop] = collectionInfo.value[element.prop];
      }
    }
  });
  companyList.value = [
    {
      value: collectionInfo.value.companyId,
      name: collectionInfo.value.companyName
    }
  ];
  deptList.value = [
    {
      value: collectionInfo.value.deptId,
      name: collectionInfo.value.deptName
    }
  ];

  form.value.setValues(obj);
  getAllCustomer(bool);
};

const getServiceList = async () => {
  let { list } = await queryCollectionServiceListByContractIdHttp({ contractId: contractId.value });
  serviceList.value = list;
};
const serviceChange = e => {
  getIncomeContractList(e);
};
const getIncomeContractList = async e => {
  if (!e) return;
  let { list } = await queryIncomeConfirmInfoByServiceIdHttp({
    serviceId: e
  });
  return list;
  // incomeContractList.value = list;
};
const getAllCustomer = async bool => {
  const { list } = await queryCustomerListNoPageHttp({ contractId: contractId.value });
  queryCustomerLis = list;

  customerKaipiaoObjs.value = [];
  list.map(item => {
    customerKaipiaoObjs.value.push(
      ...(item?.customerVo?.customerKaipiaoObjs.map(el => ({ ...el, father: item?.customerVo })) ?? [])
    );
  });
  let invoiceTitle = '';
  form.value.setValues({
    invoiceTitle: void 0,
    taxIdNumber: void 0,
    openBankName: void 0,
    openAccountNumber: void 0,
    bankAccountName: void 0
    // ...(bool ? { customerTelephone: telephone } : {})
  });
  // 第一步设置 发票抬头信息

  if (!bool && unref(infoData).taxIdNumber) {
    if (props.isEdit) {
      invoiceTitle = unref(infoData).taxIdNumber;
    } else {
      invoiceTitle = unref(infoData).invoiceTitle;
    }

    form.value.setValues({
      invoiceTitle
    });

    invoiceTitleChange(unref(infoData).taxIdNumber, bool);
  }
  await nextTick();
  // 第二步设置 银行信息
  let bankAccountName = '';
  if (!bool) {
    if (props.isEdit) {
      bankAccountName = unref(infoData).openAccountNumber || unref(infoData).openAccountName;
    } else {
      bankAccountName = unref(infoData)?.openAccountName;
    }
    form.value.setValues({
      bankAccountName
    });
    if (unref(infoData).openAccountNumber) bankAccountNameChange(unref(infoData).openAccountNumber, bool);
  }

  await nextTick();
  if (!props.isEdit) {
    setTimeout(() => {
      form.value.clearValidate();
    }, 800);
  }
};

const getPartyAInfo = async (customerId, bool) => {
  return;

  let { customerVo } = await customerInfoHttp({
    customerId
  });

  customerInfo.value = customerVo;
  let {
    invoiceHeader: invoiceTitle,
    taxIdNumber,
    bankName: openBankName,
    bankAccountNumber,
    telephone,
    dicContactProvinceName,
    dicContactCityName,
    dicContactDistrictName,
    contactAddress,
    customerKaipiaoObjs: kaipiaoList
  } = customerVo;

  let companyAddress =
    (dicContactProvinceName || '') +
    (dicContactCityName || '') +
    (dicContactDistrictName || '') +
    (contactAddress || '');

  customerKaipiaoObjs.value = kaipiaoList;
  form.value.setValues({
    invoiceTitle: void 0,
    taxIdNumber: void 0,
    openBankName: void 0,
    openAccountNumber: void 0,
    bankAccountName: void 0,
    companyAddress,
    ...(bool ? { customerTelephone: telephone } : {})
  });
  // 第一步设置 发票抬头信息

  if (!bool && unref(infoData).taxIdNumber) {
    if (props.isEdit) {
      invoiceTitle = unref(infoData).taxIdNumber;
    } else {
      invoiceTitle = unref(infoData).invoiceTitle;
    }

    form.value.setValues({
      invoiceTitle
    });

    invoiceTitleChange(unref(infoData).taxIdNumber, bool);
  }
  await nextTick();
  // 第二步设置 银行信息
  let bankAccountName = '';
  if (!bool) {
    if (props.isEdit) {
      bankAccountName = unref(infoData).openAccountNumber || unref(infoData).openAccountName;
    } else {
      bankAccountName = unref(infoData)?.openAccountName;
    }
    console.log('bankAccountName', bankAccountName);
    form.value.setValues({
      bankAccountName
    });
    if (unref(infoData).openAccountNumber) bankAccountNameChange(unref(infoData).openAccountNumber, bool);
  }

  await nextTick();
  if (!props.isEdit) {
    setTimeout(() => {
      form.value.clearValidate();
    }, 800);
  }
};

/**
 * @description 数据保存函数
 * **/
const saveDate = async type => {
  let data = await getData(type);
  if (!data) return Promise.reject();
  if (!props.id) {
    let resData = await openBusiness(classType[props.type]['code'], data, props.reason, classType[props.type]['name']);
    let { businessData } = resData;
    let { applySn, id, businessId } = businessData?.issueFapiaoApplyDetailVo || {};
    emit('infoUpdate', {
      ...(businessData?.issueFapiaoApplyDetailVo || {}),
      id,
      businessId: businessId || props.businessId
    });
    form.value.setValues({
      applySn
    });
  } else if (props.id && type === 'ts') {
    await issueFapiaoApplyNewHttp({
      ...data,
      id: props.id,
      isSubmit: false
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject(222);
  if (props.id && !type)
    await issueFapiaoApplyNewHttp({
      ...data,
      id: props.id,
      isSubmit: true
    });

  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  emit('otherUpdate', {
    companyName: data.companyName,
    deptName: data.deptName,
    contractName: data.contractName
  });
  return Promise.resolve({});
};

const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await form.value.validate();
  if (type != 'ts') {
    isValid = isValid ? verificationTable() : false;
  }

  if (isValid) {
    let formData = form.value.getValues();
    let { companyId, companyName, deptId, deptName, contractSn } = collectionInfo.value;
    let {
      fapiaoAmount,
      taxRate,
      taxAmount,
      remark,
      dicFapiaoTypeCode,
      incomeContractSn,
      serviceId,
      fapiaoContent,
      taxIdNumber,
      openAccountNumber,
      customerTelephone,
      openBankName,
      bankAccountName
    } = formData;
    let openAccountName = void 0;
    let incomeContractId = void 0;
    let invoiceTitle = void 0;
    let object = void 0;

    console.log('openAccountNumber', formData);
    if (openAccountNumber) {
      let customerKaipiaoObj = customerKaipiaoObjs.value.find(
        item => item.taxIdNumber == taxIdNumber && item.bankAccountNumber == openAccountNumber
      );
      openAccountName = customerKaipiaoObj.bankAccountName;
    } else if (customerKaipiaoObjs.value && customerKaipiaoObjs.value.length == 1) {
      openAccountName = customerKaipiaoObjs.value[0].bankAccountName;
    } else {
      openAccountName = bankAccountName;
    }
    if (taxIdNumber) {
      let { invoiceHeader } = customerKaipiaoObjs.value.find(item => item.taxIdNumber == taxIdNumber);
      invoiceTitle = invoiceHeader;
    }

    if (incomeContractSn) {
      object = incomeContractList.value.find(item => item.incomeSn == incomeContractSn);
    }
    incomeContractId = object?.incomeContractId || object?.id;

    let firstCustomerId = collectionInfo.value?.multipartyInfoList?.find(
      item => item.dicMultipartyTypeCode === '1'
    ).customerId;
    let firstCustomerName = collectionInfo.value?.multipartyInfoList?.find(
      item => item.dicMultipartyTypeCode === '1'
    ).customerName;
    let secondCustomerId = collectionInfo.value?.multipartyInfoList?.find(
      item => item.dicMultipartyTypeCode === '2'
    ).customerId;
    let secondCustomerName = collectionInfo.value?.multipartyInfoList?.find(
      item => item.dicMultipartyTypeCode === '2'
    ).customerName;
    // infoData.value
    let requestParams = {
      contractId: formData?.contractObj?.id,
      contractName: formData?.contractObj?.name,
      employeeId: formData?.employeeObj?.id,
      employeeName: formData?.employeeObj?.name,
      firstCustomerId: firstCustomerId || '',
      firstCustomerName: firstCustomerName || '',
      secondCustomerId: secondCustomerId || '',
      secondCustomerName: secondCustomerName || '',
      fapiaoAmount,
      taxRate,
      taxAmount,
      remark,
      fapiaoContent,
      customerTelephone,
      dicFapiaoTypeCode,
      incomeContractSn,
      incomeContractId,
      serviceId,
      taxIdNumber: taxIdNumber || '',
      openAccountNumber: openAccountNumber || '',
      contractSn,
      invoiceTitle,
      taxIdNumber,
      openAccountName,
      openBankName
    };
    if (['cancel_add', 'cancel_edit'].includes(props.type)) {
      let arr = ['fapiaoAmount', 'taxRate', 'taxAmount', 'dicFapiaoTypeCode'];
      for (let key in requestParams) {
        if (!arr.includes(key)) requestParams[key] = infoData.value[key];
      }

      requestParams.lastId = props.lastId || void 0;
    }

    return {
      ...requestParams,
      companyId,
      companyName,
      deptId,
      deptName,
      kaipiaoObjId,
      bankAccountId,
      oldApplySn,
      oldFapiaoAmount,
      fapiaoServiceRequestList: unref(csData).map(item => {
        return {
          serviceId: item.serviceId || item.id,
          serviceSn: item.serviceSn,
          incomeContractId: item.incomeContractId,
          incomeContractSn: item.incomeContractSn,
          fapiaoAmount: item.fapiaoAmount,
          oldFapiaoAmount: item.oldFapiaoAmount
        };
      })
    };
  }

  return false;
};

const verificationTable = () => {
  if (csData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '服务内容必填',
      type: 'warning'
    });
    return false;
  } else {
    return _verificationTableForm(_contactRef);
  }
};
const _verificationTableForm = obj => {
  let v_l = [];
  for (let key in obj) {
    let v = obj[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};

/**
 * @description 下一步 提交数据
 * **/
const nextStep = () => {
  return saveDate();
};
const ts = () => {
  return saveDate('ts');
};
const authFun = async type => {
  let isAudit = true;
  let isValid = true;
  let { receivePersonId } = form.value.getValues();
  if (type == 'ts') {
    isAudit = false;
  } else {
    let validate = type !== 'notValidate' ? await form.value.validateField(['receivePersonId']) : { isValid: true };
    let tableValidate = type !== 'notValidate' ? verificationTableForm(contactRef) : true;
    isValid = validate.isValid && tableValidate;
    if (!isValid) {
      return Promise.reject({});
    }
    return Promise.resolve({
      fapiaoInfoRequestList: unref(cData),
      id: props.id,
      receivePersonId: receivePersonId?.id,
      receivePersonName: receivePersonId?.name,
      isAudit
    });
  }
  try {
    loadingStatus.value.status = true;
    await issueFapiaoApplyNewHttp({
      fapiaoInfoRequestList: unref(cData),
      id: props.id,
      receivePersonId: receivePersonId.id,
      receivePersonName: receivePersonId.name,
      isAudit,
      businessConfigCode: 'ERM_ISSUE_FAPIAO_APPLY_ADD'
    }).finally(() => {
      loadingStatus.value.status = false;
    });
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
    return Promise.resolve({});
  } catch {
    return Promise.reject({});
  }
};

const verificationTableForm = obj => {
  if (cData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '发票信息必填',
      type: 'warning'
    });
    return false;
  }
  let v_l = [];
  for (let key in obj) {
    let v = obj[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};
const getForm = () => {
  return form.value;
};

const getPrintData = () => {
  return {
    companyList: companyList.value,
    deptList: deptList.value,
    list: cData.value,
    fapiaoDetailRequestList: fapiaoDetailRequestList.value
  };
};

defineExpose({
  nextStep,
  ts,
  authFun,
  getInvoiceInfo,
  getForm,
  getPrintData
});
</script>
<style scoped>
.musterW {
  overflow: hidden;
  width: 100%;
  height: 26px;
  padding-left: 5px;
}

.musterW > span {
  color: red;
  display: inline-block;
  /* margin: 3px 3px 0 0; */
  position: relative;
  top: 4px;
  right: 3px;
}
</style>
