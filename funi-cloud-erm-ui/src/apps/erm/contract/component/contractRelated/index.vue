<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-03 14:12:09
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-15 10:20:46
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/component/contractRelated/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <GroupTitle :isSymbol="isSymbol" v-if="showTitle" title="合同相关方">
      <div v-if="isEdit">
        <el-button type="primary" @click="addFunc('1')">添加</el-button>
        <el-button type="primary" @click="addFunc('2')">添加客商</el-button>
      </div>
    </GroupTitle>
    <funi-curd ref="curd" :rowSelection="false" :columns="cColumns" :data="cData" :pagination="false">
      <template #nameHead>
        <div class="musterW"><span>*</span>签约方</div>
      </template>
      <template #conName>
        <div class="musterW"><span>*</span>签约方名称</div>
      </template>
    </funi-curd>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, watch } from 'vue';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import Verifics from '@/apps/erm/component/verifica/index.vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import { apiUrl } from './../../hooks/collection/api.js';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
const props = defineProps({
  ...baseInfoProps,
  list: {
    type: Array,
    default: []
  },
  showTitle: {
    type: Boolean,
    default: true
  },
  isSymbol: {
    type: Boolean,
    default: false
  }
});

const curd = ref();
const cData = ref([]);
const contactRef = {};
const router = useRouter();
const correlationColumns = ({ isEdit, iptChange = () => {}, contactRef = {} }) => {
  const reset_v = (key, el) => {
    contactRef[key] = el ? el : contactRef[key];
  };
  return [
    {
      label: '签约方',
      prop: 'dicMultipartyTypeCode',
      slots: {
        header: 'nameHead'
      },
      width: '600px',
      render: ({ row, index }) => {
        let c = (
          <ErmSelect
            code="multiparty_type"
            onChange={e => {
              iptChange(index, 'dicMultipartyTypeCode', e);
            }}
            maxlength={50}
            exclude={['1', '2']}
            placeholder="签约方"
          />
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_dicMultipartyTypeCode`, el);
            }}
            value={row.dicMultipartyTypeCode}
            c={c}
            key={`${row.uuId}_dicMultipartyTypeCode`}
            rule={[{ required: true, message: '必填' }]}
          ></Verifics>
        ) : (
          <span>{row.dicMultipartyTypeName}</span>
        );
      }
    },
    {
      label: '签约方名称',
      prop: 'customerObj',
      slots: {
        header: 'conName'
      },

      render: ({ row, index }) => {
        let c = (
          <InfiniteSelect
            api={apiUrl.queryCustomerList}
            defaultProps={{
              keyWord: 'nameOrSuCreditCode',
              name: 'customerName',
              id: 'id',
              sn: 'suCreditCode',
              nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
            }}
            otherParams={{}}
            onChange={(e, obj) => {
              iptChange(index, 'customerObj', obj);
            }}
          ></InfiniteSelect>
        );
        return isEdit ? (
          <Verifics
            ref={el => {
              reset_v(`${row.uuId}_customerObj`, el);
            }}
            value={row.customerObj}
            c={c}
            key={`${row.uuId}_customerObj`}
            rule={[
              {
                validator: (value, callback) => {
                  if (value === '' || value === null || value === undefined || !value.id) {
                    callback('必填');
                  } else {
                    callback();
                  }
                }
              }
            ]}
          ></Verifics>
        ) : (
          // <span>{row.customerName}</span>

          <el-button
            link
            type="primary"
            onClick={e => {
              router.push({
                name: 'erm_supplier_two',
                query: {
                  title: row.customerName || '客商管理',
                  bizName: '详情',
                  type: 'info',
                  id: row.customerId,
                  tab: ['客商管理', row.customerName, '详情'].join('-')
                }
              });
            }}
          >
            {row.customerName}
          </el-button>
        );
      }
    },
    ...(isEdit
      ? [
          {
            label: '操作',
            prop: '',
            align: 'center',
            fixed: 'right',
            cellInteraction: false,
            render: ({ row, index }) => {
              return (
                <div style="display: inline-flex;justify-content: space-around;align-items: center;gap:12px">
                  {
                    <el-button
                      type="primary"
                      onClick={() => {
                        delFunc(index);
                      }}
                      link
                    >
                      删除
                    </el-button>
                  }
                </div>
              );
            }
          }
        ]
      : [])
  ];
};

const addFunc = key => {
  if (key === '1') {
    cData.value.push({
      dicMultipartyTypeCode: '',
      customerId: '',
      customerName: '',
      customerObj: {
        id: void 0,
        name: ''
      },
      uuId: $utils.guid()
    });
  }
  if (key === '2') {
    router.push({
      name: 'erm_supplier_add',
      query: {
        bizName: '新建',
        type: 'add',
        tab: '客商管理-新建'
      }
    });
  }
};

const delFunc = index => {
  cData.value.splice(index, 1);
};

const iptChange = (index, key, e) => {
  cData.value[index][key] = e;
  if (key === 'customerObj') {
    cData.value[index]['customerId'] = e?.id;
    cData.value[index]['customerName'] = e?.defaultName || e?.name;
  }
};

watch(
  () => props.list,
  newVal => {
    if (!newVal) newVal = [];

    cData.value.push(
      ...newVal.map(item => {
        return {
          ...item,
          customerObj: {
            id: item.customerId,
            name: item.customerName
          },
          uuId: $utils.guid()
        };
      })
    );
  },
  {
    deep: true,
    immediate: true
  }
);

const verificationTable = () => {
  if (cData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '合同相关方必填',
      type: 'warning'
    });
    return false;
  } else {
    return verificationTableForm();
  }
};
const verificationTableForm = () => {
  let v_l = [];
  for (let key in contactRef) {
    let v = contactRef[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};
const getData = () => {
  return cData.value;
};

const cColumns = correlationColumns({
  isEdit: props.isEdit,
  iptChange,
  contactRef
});
defineExpose({
  verificationTable,
  getData
});
</script>
<style scoped>
.musterW {
  overflow: hidden;
  width: 100%;
  height: 26px;
  padding-left: 5px;
}
.musterW > span {
  color: red;
  display: inline-block;
  /* margin: 3px 3px 0 0; */
  position: relative;
  top: 4px;
  right: 3px;
}
</style>
