<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="large" title="选择服务" @close="cancelClick">
      <div>
        <FuniCurd
          ref="funiCurd"
          @get-curd="
            e => {
              funi_curd = e;
            }
          "
          max-height="calc(50vh - 40px)"
          :columns="columnsProject"
          :useSearchV2="false"
          :isShowSearch="true"
          :searchConfig="searchConfig"
          @row-click="getData"
          :row-class-name="tableRowClassName"
          size="small"
          :lodaData="lodaData"
          :pagination="false"
        >
        </FuniCurd>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" :disabled="row === void 0" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref } from 'vue';
import { dicCode } from '@/apps/erm/config/config.jsx';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
import { validServiceHttp } from './../../../hooks/income/api.js'; //根据服务id查询服务是否有草稿或有效的确收表
import { ElNotification } from 'element-plus';
const funi_curd = ref(null);
const dialogVisible = ref(false);
const funiCurd = ref(void 0);
const row = ref(void 0);
const cData = ref([]);
let infoServeSn = '';
const columnsProject = computed(() => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left',
      selectable: (row, index) => {
        return !row.itemCheckId && row.dicConfirmModeCode !== '1';
      }
    },
    {
      label: '来源',
      prop: 'dicServiceSourceName'
    },
    {
      label: '服务内容',
      prop: 'dicServiceContentName'
    },
    {
      label: '是否开放性条款',
      prop: 'isOpenClause',
      width: 120,
      render: ({ row, index }) => (row.isOpenClause === true ? '是' : row.isOpenClause === false ? '否' : '--')
    },
    {
      label: '签约类型',
      prop: 'dicSignTypeName'
    },
    {
      label: '确收方式',
      prop: 'dicConfirmModeName'
    },
    {
      label: '金额',
      prop: 'includeTaxAmount',
      align: 'right'
    },
    {
      label: '税率',
      prop: 'taxRate',
      align: 'right'
    },

    {
      label: '已确收(元)',
      prop: 'confirmReceipt',
      align: 'right'
    },
    {
      label: '未确收(元)',
      prop: 'unconfirmReceipt',
      align: 'right'
    }
  ];
});
const searchConfig = computed(() => {
  let obj = {
    schema: [
      {
        label: '服务内容',
        component: () => <ErmSelect />,
        prop: 'dicServiceContentCode',
        props: {
          placeholder: '请选择',
          code: dicCode.service_content
        }
      },
      {
        label: '确收方式',
        component: () => <ErmSelect />,
        prop: 'dicConfirmModeCode',
        props: {
          placeholder: '请选择',
          code: dicCode.confirm_mode
        }
      }
    ]
  };
  return obj;
});
/**
 *
 * @param {*} page
 * @param {*} params
 */
const lodaData = async (page, params) => {
  let list = cData.value
    .filter(item => (params.dicServiceContentCode ? params.dicServiceContentCode == item.dicServiceContentCode : true))
    .filter(item => (params.dicConfirmModeCode ? params.dicConfirmModeCode == item.dicConfirmModeCode : true));
  return { list };
};
const emit = defineEmits(['exportObject']);

const getData = e => {
  row.value = e.row;
};

const show = async (data, sn) => {
  infoServeSn = sn;
  cData.value = data;
  dialogVisible.value = true;
  await nextTick();
};
const tableRowClassName = ({ row }) => {
  /**
   * dicConfirmModeCode 为期间 不能选择
   */
  if (row.itemCheckId || row.dicConfirmModeCode == '1') {
    return 'disabled-row';
  }
  return '';
};

const confirmFunc = async () => {
  let dataList = [];
  if (row.value.dicConfirmModeCode == '3' && !row.value.isOpenClause && infoServeSn !== row.value.serviceSn) {
    //待生效期间服务
    let { list } = await validServiceHttp({ serviceSn: row.value.serviceSn }); //待生效期间服务不可在存入服务内容表
    dataList = list || [];
  }

  if (dataList.length > 0) {
    // list 长度大于1 可存入服务表
    ElNotification({
      title: '提示',
      message: `该待生效期间服务已存在确认收入确认表（确认表编号：${dataList[0].incomeSn}），请勿重复添加!`,
      type: 'warning'
    });
  } else {
    emit('exportObject', unref(row));
    cancelClick();
  }
};
const cancelClick = () => {
  dialogVisible.value = false;
  row.value = void 0;
  cData.value = [];
};
defineExpose({
  show
});
</script>

<style scoped>
@import url('@/apps/erm/config/table_disabled.css');
</style>
