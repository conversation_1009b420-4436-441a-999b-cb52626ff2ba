<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-03 16:37:42
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-02-29 16:44:29
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/component/income/baseInfo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <GroupTitle title="基本信息"></GroupTitle>
    <funi-form :schema="schema" @get-form="setForm" :rules="rules" :col="3" :border="false"></funi-form>
    <GroupTitle isSymbol title="服务内容">
      <div v-if="isEdit">
        <el-button type="primary" @click="addFunc">添加</el-button>
      </div>
    </GroupTitle>
    <funi-curd :rowSelection="false" :columns="sColumns" :data="cData" :pagination="false">
      <template #a>
        <div class="musterW"><span>*</span>签约类型</div>
      </template>
    </funi-curd>
    <GroupTitle title="确收信息"></GroupTitle>
    <funi-form :schema="schema2" @get-form="setForm2" :rules="rules2" :col="3" :border="false"></funi-form>
    <ChooseCollection ref="chooseModal" @exportObject="setCollection"></ChooseCollection>
    <div v-if="['info', 'audit'].includes(type) && cData && cData.length">
      <GroupTitle title="确收明细表"></GroupTitle>
      <funi-curd
        :rowSelection="false"
        :useSearchV2="false"
        :isShowSearch="true"
        :columns="dColumns"
        :loading="loading"
        :searchConfig="searchConfig"
        :lodaData="lodaData"
      >
      </funi-curd>
    </div>
    <div v-if="['info', 'audit'].includes(type) && businessId">
      <GroupTitle title="要件信息"> </GroupTitle>
      <FuniFileTable :onlyShow="type == 'info'" :params="{ businessId }"></FuniFileTable>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, inject, nextTick, watch, onMounted, unref, onBeforeMount } from 'vue';
import {
  useFormSchema,
  useServeColumns,
  useDetailColumns,
  useFormSchema2,
  useRule,
  useRule2,
  setAmount
} from './../../hooks/income/baseInfo.jsx';
import { collectionContractInfoHttp } from './../../hooks/collection/api.js';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import {
  incomeConfirmInfoNewHttp,
  incomeConfirmInfoHttp,
  queryIncomeDetailListHttp,
  creatBusHttp
} from './../../hooks/income/api.js';
import { openBusiness } from '@/apps/erm/config/business.js';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import ChooseCollection from './modal/chooseCollection.vue';
import { baseInfoProps } from '@/apps/erm/config/config.jsx';
import { ElNotification } from 'element-plus';
import { useAppStore } from '@/stores/useAppStore';

const props = defineProps({
  ...baseInfoProps,
  cId: String,
  cName: String,
  reason: String,
  isAuditEdit: Boolean
});
const appStore = useAppStore();
const form = ref();
const form2 = ref();
const emit = defineEmits(['updateID', 'infoUpdate', 'otherUpdate']);
const loadingStatus = inject('loadingStatus');
const setForm = e => (form.value = e);
const setForm2 = e => (form2.value = e);
const collectionInfo = ref({});
const cData = ref([]);
const chooseModal = ref();
const loading = ref(false);
const contactRef = {};
const sealTypeList = ref([]);
const serviceTotalAmount = ref(0);
const totalIncomeConfirmAmount = ref(0);
const contractList = ref([]);
const isOpenClause = computed(() => {
  if (cData.value.length) {
    return cData.value[0].isOpenClause;
  }
  return true;
});
const dicConfirmModeCode = computed(() => {
  if (cData.value.length) {
    return cData.value[0].dicConfirmModeCode;
  }
  return '';
});
let infoServeSn = '';
const classType = {
  add: {
    code: 'ERM_INCOME_CONFIRM_ADD',
    name: '收入确认表新增'
  },
  change_add: {
    code: 'ERM_INCOME_CONFIRM_CHANGE',
    name: '收入确认表变更'
  }
};

onMounted(() => {
  getSealType();
  if (props.id) {
    getIncomeInfo();
  }

  if (props.type == 'add') {
    setDefaultContract();
  }
});

const setDefaultContract = () => {
  if (props.cId && props.cName) {
    unref(form).setValues({
      contractObj: {
        id: props.cId,
        name: props.cName
      }
    });
    getcontractInfo({ id: props.cId });
  }
};

const schema = computed(() =>
  useFormSchema({ isEdit: props.isEdit, getcontractInfo, sealTypeList: sealTypeList.value, type: props.type })
);
const schema2 = computed(() =>
  useFormSchema2({
    isEdit: props.isEdit,
    serve: cData.value.length ? cData.value[0] : void 0,
    form: form2,
    type: props.type,
    isAuditEdit: props.isAuditEdit
  })
);
const sColumns = computed(() => useServeColumns(props.isEdit, contactRef, delFunc));
const dColumns = computed(() => useDetailColumns());

const rules = computed(() => {
  return useRule(props.isEdit, props.type);
});
const rules2 = computed(() => {
  return useRule2(props.isEdit, {
    serviceTotalAmount: serviceTotalAmount.value,
    totalIncomeConfirmAmount: totalIncomeConfirmAmount.value,
    isOpenClause: isOpenClause.value,
    type: props.type,
    isAuditEdit: props.isAuditEdit
  });
});
const delFunc = () => {
  cData.value = [];
  setForm2Info();
};
const searchConfig = computed(() => {
  let obj = {
    schema: [
      {
        label: '月度',
        component: 'el-date-picker',
        prop: 'issueTime',
        props: {
          type: 'monthrange',
          'range-separator': '-',
          'start-placeholder': '请选择开始月份',
          'end-placeholder': '请选择结束月份',
          'value-format': 'YYYY-MM-DD HH:mm:ss',
          style: {
            width: '100%',
            'box-sizing': 'border-box'
          }
        }
      }
    ]
  };
  return obj;
});

const getSealType = () => {
  $http.fetch(ermGlobalApi.dictList, { typeCode: 'seal_type', clientId: appStore.system?.clientId }).then(res => {
    sealTypeList.value = res;
  });
};
/**
 *
 * @param {*} page
 * @param {*} params
 */
const lodaData = async (page, params) => {
  try {
    Object.assign(params, { issueStartTime: params.issueTime?.[0], issueEndTime: params.issueTime?.[1] });
    loading.value = true;
    const resData = await queryIncomeDetailListHttp({
      incomeId: props.id,
      ...page,
      ...params,
      issueTime: undefined
    });
    return resData;
  } finally {
    loading.value = false;
  }
};
const addFunc = () => {
  chooseModal.value.show(collectionInfo.value.collectionServiceList, infoServeSn);
};

const setCollection = async (e, bool = true) => {
  cData.value = [e];

  if (bool) {
    setForm2Info();
  }
};

const getIncomeInfo = async () => {
  loadingStatus.value.status = true;
  let data = {};
  if (props.type == 'change_add') {
    data = await creatBusHttp({ id: props.id, dicBusinessTypeCode: classType[props.type].code }).catch(() => {
      loadingStatus.value.status = false;
    });
  } else {
    data = await incomeConfirmInfoHttp({ incomeConfirmInfoId: props.id }).catch(() => {
      loadingStatus.value.status = false;
    });
  }

  let {
    incomeSn,
    dicIncomeStatusName,
    collectionContract,
    service,
    createTime,
    takeEffectTime,
    employeeId,
    employeeName,
    isSeal,
    dicSealTypeCode,
    dicSealTypeCodeList,
    dicSealTypeName
  } = data;

  if (props.type === 'add') {
    incomeSn = void 0;
    dicIncomeStatusName = void 0;
    createTime = void 0;
    takeEffectTime = void 0;
  }
  let params = {
    isSeal,
    dicSealTypeCode: dicSealTypeCodeList,
    dicSealTypeName
  };
  if (props.isEdit) {
    params.employeeObj = {
      id: employeeId,
      name: employeeName
    };
  } else {
    params.employeeName = employeeName;
  }

  form.value.setValues({
    incomeSn,
    createTime,
    takeEffectTime,
    dicIncomeStatusName,
    contractName: collectionContract.contractName,
    contractObj: {
      id: collectionContract.id,
      name: `${collectionContract.contractName}(${collectionContract.contractSn})`,
      title: collectionContract.contractName
    },
    ...params
  });
  await getcontractInfo(collectionContract, false);
  console.log('service', service);
  cData.value = service ? [service] : [];
  infoServeSn = service?.serviceSn;
  await nextTick();
  let obj = {};
  schema2.value.map(item => {
    if (item.hidden !== true) {
      obj[item.prop] = data[item.prop];
    }
  });
  obj['dicCollTypeCode'] = data['dicCollTypeCode'];
  obj['dicCollTypeName'] = data['dicCollTypeName'];
  form2.value.setValues(obj);
  if (props.type == 'add') {
    data.id = void 0;
    data.businessId = void 0;
  }
  emit('infoUpdate', data);
  await nextTick();
  form2.value.clearValidate();
};

const getcontractInfo = async ({ contractSn, id }, bool = true) => {
  loadingStatus.value.status = true;
  let params = {
    collectionContractId: contractSn ? void 0 : id,
    collectionContractSn: contractSn ? contractSn : void 0
  };

  const data = await collectionContractInfoHttp(params).finally(() => {
    loadingStatus.value.status = false;
  });
  collectionInfo.value = data;
  let obj = {};
  let exclude = [
    'incomeSn',
    'dicIncomeStatusName',
    'contractObj',
    'createTime',
    'takeEffectTime',
    'isSeal',
    'dicSealTypeCode',
    'dicSealTypeName'
  ];

  if (props.type !== 'add' && !bool) {
    exclude.push(...['employeeObj', 'employeeName']);
  }
  schema.value.forEach(element => {
    if (exclude.indexOf(element.prop) < 0) {
      if (element.prop == 'employeeObj') {
        obj[element.prop] = {
          id: collectionInfo.value.employeeId,
          name: collectionInfo.value.employeeName
        };
      } else if (element.prop == 'partA') {
        let o = collectionInfo.value.multipartyInfoList.find(item => item.dicMultipartyTypeCode == '1');
        if (o)
          obj[element.prop] = {
            name: o.customerName,
            id: o.customerId
          };
      } else if (element.prop == 'projectObj') {
        obj[element.prop] = {
          name: collectionInfo.value.projectName,
          id: collectionInfo.value.projectId
        };
      } else {
        obj[element.prop] = collectionInfo.value[element.prop];
      }
    }
  });
  serviceTotalAmount.value = data.serviceTotalAmount;
  totalIncomeConfirmAmount.value = data.totalIncomeConfirmAmount;

  form.value.setValues(obj);
  if (bool) {
    cData.value = [];
    setForm2Info();
  }
};

const setForm2Info = async () => {
  form2.value.resetFields();
  await nextTick();
  form2.value.setValues({
    dicCollTypeCode: collectionInfo.value.dicCollTypeCode
  });
  if (cData.value && cData.value.length && cData.value[0].dicConfirmModeCode === '3') {
    let { excludeTaxAmount, taxRate, includeTaxAmount, dicConfirmModeCode } = cData.value[0];

    form2.value.setValues({
      includeTaxAmount
    });
    setAmount({
      preTaxAmount: includeTaxAmount || 0,
      form: form2,
      taxRate
    });
  }
  await nextTick();
  form2.value.clearValidate();
};

/**
 * @description 数据保存函数
 * **/
const saveDate = async type => {
  let data = false;
  data = await getData(type);

  if (!data) return Promise.reject();
  if (!props.id) {
    let resData = await openBusiness(classType[props.type].code, data, props.reason, classType[props.type].name);
    let { businessData } = resData;
    let { id, businessId, contractSn } = businessData;
    emit('infoUpdate', {
      ...businessData,
      id,
      businessId: businessId || props.businessId
    });
    form.value.setValues({
      contractSn
    });
  } else if (props.id && type === 'ts') {
    await incomeConfirmInfoNewHttp({
      ...data,
      id: props.id,
      isSubmit: false
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject();
  if (props.id && !type)
    await incomeConfirmInfoNewHttp({
      ...data,
      id: props.id,
      isSubmit: true
    });

  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  emit('otherUpdate', {
    companyName: data.companyName,
    deptName: data.deptName,
    contractName: collectionInfo.value.contractName
  });
  return Promise.resolve({});
};

const getData = async type => {
  let v = false;
  let v2 = false;

  if (['change_add', 'change_edit'].includes(props.type)) {
    if (!type) {
      let isV = await form.value.validate();
      v = isV.isValid;
    }
    let { isValid } = type == 'ts' ? { isValid: true } : { isValid: v };
    if (!isValid) return;
    let { acceptanceDesc, includeTaxAmount, taxAmount, excludeTaxAmount } = form2.value.getValues();
    let { isSeal, dicSealTypeCode } = form.value.getValues();
    return {
      isSeal,
      dicSealTypeCode: dicSealTypeCode ? dicSealTypeCode.join(',') : '',
      acceptanceDesc,
      lastId: props.lastId || void 0,
      dicBusinessTypeCode: 'ERM_INCOME_CONFIRM_CHANGE',
      includeTaxAmount,
      taxAmount,
      excludeTaxAmount
    };
  } else {
    if (!type) {
      let isV = await form.value.validate();
      let isV2 = await form2.value.validate();
      v = isV.isValid;
      v2 = isV2.isValid;
    }

    let { isValid } = type == 'ts' ? { isValid: true } : { isValid: v && v2 };
    if (type == 'ts' || (isValid && verificationTable())) {
      let formData = form.value.getValues();
      let formData2 = form2.value.getValues();
      let { companyId, companyName, deptId, deptName, contractSn } = collectionInfo.value;
      let { id: serviceId, serviceSn, dicSignTypeCode } = cData.value && cData.value.length ? cData.value[0] : {};
      let requestParams = {
        contractId: formData?.contractObj?.id,
        employeeId: formData?.employeeObj?.id,
        isSeal: formData.isSeal,
        dicSealTypeCode: formData.dicSealTypeCode ? formData.dicSealTypeCode.join(',') : '',
        serviceId,
        serviceSn,
        dicSignTypeCode
      };

      return {
        ...requestParams,
        companyId,
        companyName,
        deptId,
        deptName,
        contractSn,
        ...formData2
      };
    }
  }

  return false;
};

const verificationTable = () => {
  if (cData.value.length === 0) {
    ElNotification({
      title: '提示',
      message: '请选择服务内容',
      type: 'warning'
    });
    return false;
  } else {
    return verificationTableForm(contactRef);
  }
};
const verificationTableForm = obj => {
  let v_l = [];
  for (let key in obj) {
    let v = obj[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};
/**
 * @description 下一步 提交数据
 * **/
const nextStep = () => {
  return saveDate();
};
const ts = () => {
  return saveDate('ts');
};

/**
 *审核是的时候校验数据
 * **/

const authFun = async () => {
  let {
    isValid,
    values: { dicCollTypeCode, acceptanceTime, includeTaxAmount, taxAmount, excludeTaxAmount }
  } = await form2.value.validate();
  if (isValid) {
    return Promise.resolve({
      dicCollTypeCode,
      acceptanceTime,
      includeTaxAmount,
      taxAmount,
      excludeTaxAmount,
      id: props.id,
      businessId: props.businessId
    });
  }
  return Promise.reject();
};

defineExpose({
  nextStep,
  ts,
  authFun
});
</script>
<style scoped>
.musterW {
  overflow: hidden;
  width: 100%;
  height: 26px;
  padding-left: 5px;
}

.musterW > span {
  color: red;
  display: inline-block;
  /* margin: 3px 3px 0 0; */
  position: relative;
  top: 4px;
  right: 3px;
}

.beasInfo_banner {
  display: flex;
  flex-direction: column;
}
</style>
