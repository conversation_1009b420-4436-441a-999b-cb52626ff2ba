<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-03 11:11:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-11-09 16:19:54
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/component/payment/baseInfo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <GroupTitle title="基本信息"></GroupTitle>
    <funi-form :schema="schema" @get-form="setForm" :rules="rules" :col="3" :border="false"></funi-form>
    <ContractRelated ref="contract_related" :is-edit="isEdit" :list="crData" isSymbol v-if="isMultipartyContract">
    </ContractRelated>
    <ChangCompareTable v-if="crData_change !== false">
      <div>
        <ContractRelated :is-edit="isEdit" :list="crData_change"></ContractRelated>
      </div>
    </ChangCompareTable>
    <template v-if="purchaseProject && purchaseProject.id">
      <GroupTitle title="关联采购标段" isSymbol>
        <div v-if="isEdit && Array.isArray(sectionList) && sectionList.length > 1">
          <el-button type="primary" @click="addSectionFunc">选择标段</el-button>
        </div>
      </GroupTitle>
      <funi-curd
        :rowSelection="false"
        rowKey="sectionId"
        :columns="seColumns"
        :data="currentSelectedSection ? [currentSelectedSection] : []"
        :pagination="false"
      >
      </funi-curd>
    </template>
    <GroupTitle title="服务明细" isSymbol>
      <div v-if="isEdit">
        <el-button type="primary" @click="addFunc">添加</el-button>
      </div>
    </GroupTitle>
    <funi-curd :rowSelection="false" :columns="sColumns" :data="cData" :pagination="false">
      <template #a>
        <div class="musterW"><span>*</span>产品名称</div>
      </template>
      <template #b>
        <div class="musterW"><span>*</span>是否开放性条款</div>
      </template>
      <template #c>
        <div class="musterW"><span>*</span>单价(元)</div>
      </template>
      <template #d>
        <div class="musterW"><span>*</span>数量</div>
      </template>
      <template #e>
        <div class="musterW"><span>*</span>税率</div>
      </template>
    </funi-curd>
    <ChangCompareTable v-if="cData_change !== false">
      <div>
        <funi-curd :rowSelection="false" :columns="sColumns" :data="cData_change" :pagination="false">
          <template #a>
            <div class="musterW"><span>*</span>产品名称</div>
          </template>
          <template #b>
            <div class="musterW"><span>*</span>是否开放性条款</div>
          </template>
          <template #c>
            <div class="musterW"><span>*</span>单价(元)</div>
          </template>
          <template #d>
            <div class="musterW"><span>*</span>数量</div>
          </template>
          <template #e>
            <div class="musterW"><span>*</span>税率</div>
          </template>
        </funi-curd>
      </div>
    </ChangCompareTable>
    <GroupTitle title="付款计划" isSymbol>
      <div v-if="isEdit">
        <el-button type="primary" @click="addFuncOne">添加</el-button>
      </div>
    </GroupTitle>
    <funi-curd :rowSelection="false" :columns="sfColumns" :data="cfData" :pagination="false">
      <template #a>
        <div class="musterW"><span>*</span>付款条件</div>
      </template>
      <template #b>
        <div class="musterW"><span>*</span>付款金额(元)</div>
      </template>
      <template #c>
        <div class="musterW"><span>*</span>预计付款日期</div>
      </template>
    </funi-curd>
    <ChangCompareTable v-if="cfData_change !== false">
      <div>
        <funi-curd :rowSelection="false" :columns="sfColumns" :data="cfData_change" :pagination="false">
          <template #a>
            <div class="musterW"><span>*</span>付款条件</div>
          </template>
          <template #b>
            <div class="musterW"><span>*</span>付款金额(元)</div>
          </template>
          <template #c>
            <div class="musterW"><span>*</span>预计付款日期</div>
          </template>
        </funi-curd>
      </div>
    </ChangCompareTable>
    <div v-if="['info', 'audit'].includes(type) && businessId">
      <GroupTitle title="要件信息"> </GroupTitle>
      <FuniFileTable :onlyShow="type == 'info'" ref="funiFileTable" :params="{ businessId }"></FuniFileTable>
    </div>

    <funi-dialog v-model="dialogVisible" title="选择标段" @confirm="setCurrentSelectedSection">
      <div>
        <funi-curd
          @current-change="currentChange"
          :columns="[
            {
              type: 'radio',
              width: '55px',
              fixed: 'left'
            },
            ...seColumns
          ]"
          highlight-current-row
          rowKey="sectionId"
          :data="sectionList"
          :pagination="false"
        >
        </funi-curd>
      </div>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, reactive, onMounted, nextTick, inject, unref, onBeforeMount } from 'vue';
import {
  useFormSchema,
  serveColumns,
  payPlanColumns,
  useRule,
  gerScDataFields,
  getBDataFields,
  sectionColumn
} from './../../hooks/payment/baseInfo.jsx';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import ContractRelated from './../contractRelated/index.vue';
import ChangCompareTable from '@/apps/erm/component/changCompareTable/index.vue';
import { baseInfoProps, ermGlobalApi } from '@/apps/erm/config/config.jsx';
import {
  queryCustomerListByCurrentEmployeeHttp,
  findCompanyByNameHttp,
  paymentContractNewHttp,
  paymentContractInfoHttp,
  paymentContractHttp,
  queryCollectionServiceListp,
  queryPaymentValidSectionListHttp,
  verifyWorkflowNodeHttp
} from './../../hooks/payment/api.js';
import { openBusiness } from '@/apps/erm/config/business.js';
import { ElNotification } from 'element-plus';
import { useAppStore } from '@/stores/useAppStore';
import ermHooks from '@/apps/erm/hooks/index.js';
const loadingStatus = inject('loadingStatus');

const form = ref();
const contactRef = {};
const contactRefOne = {};
const appStore = useAppStore();
const detailObj = ref({});
const cData = ref([]);
const cData_change = ref(false);
const crData = ref([]);
const crData_change = ref(false);
const cfData = ref([]);
const cfData_change = ref(false);
const props = defineProps({
  ...baseInfoProps,
  reason: String
});
const contract_related = ref();
const sealTypeList = ref([]); // 用印类型
const customerListByCurrent = ref([]);
const companyList = ref([]);
const isIncludeOpenTerm = ref(true);
const isMultipartyContract = ref(true);
const collectionService = ref([]); //服务内容
const collectionContractId = ref(null);
const dicBusinessTypeCode = ref('');
let collectionServiceId = '';
const lastDataInfo = ref(void 0);
const funiFileTable = ref();
const isSealUsed = ref(false);
const otherParams = reactive({
  collectionContractSn: void 0,
  projectSn: void 0
});

const sectionList = ref([]);
const purchaseProject = ref(void 0);
const dialogVisible = ref(false);
const currentSection = ref(void 0);
const currentSelectedSection = ref(void 0);
const verifyWorkflow = ref(false);

/**
 * 合同开始/结束时间
 */
const dateObj = reactive({
  startDate: null,
  endDate: null
});

const setDate = (key, value) => {
  dateObj[key] = value;
};
const disabledStartDate = time => {
  return dateObj.endDate && time.getTime() > $utils.Date(dateObj.endDate);
};
const disabledEndDate = time => {
  return dateObj.startDate && time.getTime() < $utils.Date(dateObj.startDate);
};
const setCollectionContractId = e => {
  collectionContractId.value = e;
};
/**
 * 设置field值
 * @param {*} e
 */
const setFieldVal = (key, val) => {
  otherParams[key] = val;
};

const setCollectionService = async (contractId, init = true) => {
  setCollectionContractId(contractId);
  let res = await queryCollectionServiceListp({ contractId });
  collectionService.value = res.list.map(item => {
    let { id, dicServiceSourceName, dicServiceContentName, includeTaxAmount, serviceSn } = item;
    return {
      name: `${dicServiceSourceName || '--'}-${dicServiceContentName || '--'}(${includeTaxAmount || '--'}元)`,
      value: id,
      serviceSn
    };
  });
  if (init) {
    form.value.setValues({
      collectionServiceId: void 0
    });
  }
};
const setForm = e => {
  form.value = e;
};
const emit = defineEmits(['updateID', 'infoUpdate', 'otherUpdate']);
let deptName = '';
const schema = computed(() => {
  return useFormSchema({
    setPartA,
    getCompanyId,
    setIsIncludeOpenTerm,
    setisMultipartyContract,
    setDeptName,
    isEdit: props.isEdit,
    sealTypeList: sealTypeList.value,
    customerListByCurrent: customerListByCurrent.value,
    companyList: companyList.value,
    type: props.type,
    form: form.value,
    depts: [],
    setDate,
    disabledStartDate,
    disabledEndDate,
    setCollectionService,
    setFieldVal,
    collectionService: collectionService.value,
    collectionContractId: collectionContractId.value,
    dicBusinessTypeCode: dicBusinessTypeCode.value,
    lastDataInfo: unref(lastDataInfo),
    isSealUsed,
    projectSn: otherParams.projectSn,
    purchaseProjectChange
  });
});

const classType = {
  add: {
    code: 'ERM_PAYMENT_CONTRACT_ADD',
    name: '付款合同新增'
  },
  change_add: {
    code: 'ERM_PAYMENT_CONTRACT_CHANGE',
    name: '付款合同变更'
  },
  end_add: {
    code: 'ERM_PAYMENT_CONTRACT_TERMINATE',
    name: '付款合同终止'
  },
  cancel_add: {
    code: 'ERM_PAYMENT_CONTRACT_CANCEL',
    name: '付款合同作废'
  }
};

onMounted(async () => {
  getSealType();
  if (props.isEdit) {
    addFunc();
  }
  if (props.type == 'add') {
    setDefaultUser();
  }
  await getOwnpartyA();
  if (props.id) {
    getPaymentInfo();
  }
});

const setDefaultUser = async () => {
  let userInfo = await ermHooks.getUserInfo();
  if (userInfo.userId && userInfo.username)
    unref(form).setValues({
      employeeObj: {
        id: userInfo.userId,
        name: userInfo.username
      }
    });
};

const iptChange = (index, key, e) => {
  cData.value[index][key] = e;
};
const iptChangeOne = (index, key, e) => {
  cfData.value[index][key] = e;
};
const delFunc_c = index => {
  cData.value.splice(index, 1);
};
const delFunc_f = index => {
  cfData.value.splice(index, 1);
};

const sColumns = computed(() => {
  return serveColumns({
    isEdit: props.isEdit,
    iptChange,
    contactRef,
    isIncludeOpenTerm: isIncludeOpenTerm.value,
    delFunc: delFunc_c
  });
});
const seColumns = computed(() => {
  return sectionColumn();
});
const sfColumns = computed(() => {
  return payPlanColumns({
    isEdit: props.isEdit,
    iptChange: iptChangeOne,
    contactRef: contactRefOne,
    delFunc: delFunc_f
  });
});
// 设置setPartA
const setPartA = async (v, bool = true) => {
  let a = customerListByCurrent.value.filter(item => item.id === v);
  let data = await findCompanyByNameHttp({ companyName: a[0]?.name });
  if (data && data.id && data.companyName) {
    companyList.value = [
      {
        name: data.companyName,
        value: data.id
      }
    ];
    await nextTick();
    form.value.setValues({
      companyId: data.id,
      sealCompanyId: data.id
    });
  } else {
    form.value.setValues({
      companyId: void 0,
      sealCompanyId: void 0
    });
  }
  if (bool) {
    form.value.setValues({
      deptId: void 0
    });
  }
};

const rules = computed(() => {
  return useRule(
    props.isEdit,
    props.type,
    dicBusinessTypeCode.value,
    verifyWorkflow.value,
    currentSelectedSection.value
  );
});

// 获取公司id
const getCompanyId = () => {
  return companyList.value && companyList.value.length ? companyList.value[0]?.value : '';
};
const addSectionFunc = () => {
  dialogVisible.value = true;
};

/**
 * @description 获取当前公司对应的客商
 * **/
const getOwnpartyA = async () => {
  let { list } = await queryCustomerListByCurrentEmployeeHttp();

  customerListByCurrent.value = list.map(item => {
    return {
      id: item.id,
      name: item.customerName
    };
  });

  // 根据当前员工归属公司 默认选中甲方 isDefaultCustomer：true
  if (!props.id && props.type == 'add') {
    let partAList = list.filter(item => item.isDefaultCustomer)[0];
    form.value.setValues({ partA: partAList.id || '' });
    // 默认选中归属部门
    setPartA(partAList.id, false);
  }
};

/**
 * @description 获取用印类型
 * **/
const getSealType = () => {
  $http.fetch(ermGlobalApi.dictList, { typeCode: 'seal_type', clientId: appStore.system?.clientId }).then(res => {
    sealTypeList.value = res;
  });
};

// 设置是否是开放性条款
const setIsIncludeOpenTerm = (e, bool = true) => {
  isIncludeOpenTerm.value = e;
  if (bool) {
    cData.value = cData.value.map(item => {
      return {
        ...item,
        isOpenTerm: void 0
      };
    });
  }
};

// 设置是否是多方合同
const setisMultipartyContract = e => {
  isMultipartyContract.value = e;
};

/**
 * @description 新增服务明细
 * **/
const addFunc = () => {
  cData.value.push(gerScDataFields());
};
/**
 * @description 付款计划
 * **/
const addFuncOne = () => {
  cfData.value.push(getBDataFields());
};
const setDeptName = v => {
  deptName = v;
};

/**
 * @description 获取详情
 * **/
const getPaymentInfo = async () => {
  let data = void 0;
  let change_end = ['add', 'change_add', 'end_add', 'cancel_add'].includes(props.type);
  let httpName = void 0;
  let params = {};

  if (!change_end) {
    httpName = paymentContractInfoHttp;
    params = {
      paymentContractId: props.id
    };
  } else {
    httpName = paymentContractHttp;
    params = {
      id: props.id,
      dicBusinessTypeCode: classType[props.type]['code']
    };
  }
  loadingStatus.value.status = true;
  data = await httpName(params).finally(() => {
    loadingStatus.value.status = false;
  });

  if (!props.isEdit && props.type === 'audit' && data?.businessId) {
    verifyWorkflowNodeHttp({ businessId: data?.businessId }).then(res => {
      verifyWorkflow.value = res ?? false;
    });
  }
  if (data.collectionContractId) {
    await setCollectionService(data.collectionContractId, false);
  }
  dateObj.startDate = data.startDate || null;
  dateObj.endDate = data.endDate || null;
  let obj = {};
  let filed = ['projectObj', 'employeeObj'];
  deptName = data?.deptName;
  isSealUsed.value = data.isSealUsed;
  schema.value.forEach(item => {
    if (filed.indexOf(item.prop) > -1) {
      let str = item.prop.slice(0, -3);
      obj[item.prop] = {
        id: data[`${str}Id`],
        name: data[`${str}Name`]
      };
    } else if (item.prop === 'partA' || item.prop === 'partA_object') {
      let o = data?.payConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '1';
      });
      if (o && o.length) {
        obj['partA'] = o[0].customerId;
        obj['partA_object'] = {
          name: o[0].customerName,
          id: o[0].customerId
        };
      }
    } else if (item.prop === 'partB' || item.prop === 'partBName') {
      let o = data?.payConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '2';
      });

      if (o && o.length) {
        obj['partB'] = {
          id: o[0].customerId,
          name: o[0].customerName
        };
        obj['partBName'] = o[0].customerName;
      }
    } else if (item.prop === 'dicSealTypeCode') {
      /**
       * 合同终止时不继承终止之前的合同用印类型
       */
      if (props.type === 'end_add') return;
      obj[item.prop] = data[item.prop] ? data[item.prop].split(',') : void 0;
    } else if (item.prop === 'contractObj') {
      let name = data.collectionContractName || '';
      if (data.collectionContractSn) {
        name += `(${data.collectionContractSn})`;
      }
      obj[item.prop] = {
        id: data.collectionContractId,
        name,
        collectionContractSn: data.collectionContractSn
      };
    } else if (item.prop === 'purchaseProject') {
      let name = data.purchaseProjectName || '';
      if (data.purchaseProjectSn) {
        name += `(${data.purchaseProjectSn})`;
      }
      obj[item.prop] = {
        id: data.purchaseProjectId,
        name,
        sn: data.purchaseProjectSn,
        purchaseProjectSn: data.purchaseProjectSn,
        title: data.purchaseProjectName
      };
    } else if (item.prop === 'collectionServiceName') {
      let contract_object = collectionService.value.find(el => el.value === data.collectionServiceId);
      obj[item.prop] = contract_object?.name;
    } else {
      obj[item.prop] = data[item.prop];
    }
    if (props.type === 'add' && item.copy === false) {
      obj[item.prop] = void 0;
    }
  });

  obj['dicChangeTypeCode'] = data['dicChangeTypeCode'];
  obj['dicChangeTypeName'] = data['dicChangeTypeName'];
  obj['dicSealTypeCode'] =
    props.type === 'end_add' ? [] : data['dicSealTypeCode'].split(',') || data['dicSealTypeCodeList'];
  obj['dicSealTypeName'] = data['dicSealTypeName'];
  obj['contractCount'] = data['contractCount'];
  obj['isSealUsed'] = data['isSealUsed'];
  collectionServiceId = data['collectionServiceId'];

  cData.value = data.payConServiceListVoList.map(item => {
    return {
      ...item,
      uuId: $utils.guid()
    };
  });
  cfData.value = data.payConPlanListVoList.map(item => {
    return {
      ...item,
      uuId: $utils.guid()
    };
  });

  crData.value = data.payConMultipartyInfoVoList.filter(
    item => item.dicMultipartyTypeCode !== '1' && item.dicMultipartyTypeCode !== '2'
  );
  props.isEdit && setPartA(obj.partA, false);
  setIsIncludeOpenTerm(obj.isIncludeOpenTerm, false);
  setisMultipartyContract(obj.isMultipartyContract, false);
  form.value.setValues(obj);
  dicBusinessTypeCode.value = data.dicBusinessTypeCode;
  emit('infoUpdate', data);
  detailObj.value = data;
  setFieldVal('collectionContractSn', data.collectionContractSn);
  setFieldVal('projectSn', data.projectSn);

  if (obj?.['purchaseProject']?.id) {
    purchaseProjectChange(obj?.['purchaseProject'], true);
  }
  if (Array.isArray(data.purchaseSectionInfoListVoList) && data.purchaseSectionInfoListVoList.length) {
    currentChange(data.purchaseSectionInfoListVoList[0]);
    setCurrentSelectedSection();
  }

  /**
   * 判断现在是否是变更审核  变更审核需要做数据对比
   * 所以需要查询上一笔件
   */
  if (props.type == 'audit' && data.dicBusinessTypeCode == 'ERM_PAYMENT_CONTRACT_CHANGE') {
    getLastPaymentInfo(data.lastId);
  }
};
/**
 * @description 获取详情
 * **/
const getLastPaymentInfo = async id => {
  let data = await paymentContractInfoHttp({ paymentContractId: id });
  let obj = {};
  let filed = ['projectObj', 'employeeObj'];
  deptName = data?.deptName;
  schema.value.forEach(item => {
    if (filed.indexOf(item.prop) > -1) {
      let str = item.prop.slice(0, -3);
      obj[item.prop] = {
        id: data[`${str}Id`],
        name: data[`${str}Name`]
      };
    } else if (item.prop === 'partA' || item.prop === 'partA_object') {
      let o = data?.payConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '1';
      });
      if (o && o.length) {
        obj['partA'] = o[0].customerId;
        obj['partA_object'] = {
          name: o[0].customerName,
          id: o[0].customerId
        };
      }
    } else if (item.prop === 'partB' || item.prop === 'partBName') {
      let o = data?.payConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '2';
      });

      if (o && o.length) {
        obj['partB'] = {
          id: o[0].customerId,
          name: o[0].customerName
        };
        obj['partBName'] = o[0].customerName;
      }
    } else if (item.prop === 'dicSealTypeCode') {
      obj[item.prop] = data[item.prop] ? data[item.prop].split(',') : void 0;
    } else if (item.prop === 'contractObj') {
      obj[item.prop] = {
        id: data.collectionContractId,
        name: data.collectionContractName
      };
    } else if (item.prop === 'collectionServiceName') {
      let contract_object = collectionService.value.find(el => el.value === data.collectionServiceId);
      obj[item.prop] = contract_object?.name;
    } else {
      obj[item.prop] = data[item.prop];
    }
    if (props.type === 'add' && item.copy === false) {
      obj[item.prop] = void 0;
    }
  });

  lastDataInfo.value = obj;
  cData_change.value = data.payConServiceListVoList.map(item => {
    Reflect.deleteProperty(item, 'uuId');
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  let _cData = $utils.clone(cData.value, true).map(item => {
    Reflect.deleteProperty(item, 'uuId');
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  if (JSON.stringify(cData_change.value) == JSON.stringify(_cData)) {
    cData_change.value = false;
  }

  cfData_change.value = data.payConPlanListVoList.map(item => {
    Reflect.deleteProperty(item, 'uuId');
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  let _cfData = $utils.clone(cfData.value, true).map(item => {
    Reflect.deleteProperty(item, 'uuId');
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  if (JSON.stringify(cfData_change.value) == JSON.stringify(_cfData)) {
    cfData_change.value = false;
  }

  crData_change.value = data.payConMultipartyInfoVoList
    .filter(item => item.dicMultipartyTypeCode !== '1' && item.dicMultipartyTypeCode !== '2')
    .map(item => {
      Reflect.deleteProperty(item, 'uuId');
      Reflect.deleteProperty(item, 'id');
      Reflect.deleteProperty(item, 'contractId');
      return item;
    });
  let _crData = $utils.clone(crData.value, true).map(item => {
    Reflect.deleteProperty(item, 'uuId');
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  if (JSON.stringify(crData_change.value) == JSON.stringify(_crData)) {
    crData_change.value = false;
  }
};
const saveDate = async type => {
  let data = await getData(type);

  if (!data) return Promise.reject();
  let resData;
  if (!props.id) {
    resData = await openBusiness(classType[props.type]['code'], data, props.reason, classType[props.type]['name']);
    let { businessData } = resData;
    let { id, businessId, contractSn } = businessData;
    emit('infoUpdate', {
      ...businessData,
      id,
      businessId: businessId || props.businessId
    });
    form.value.setValues({
      contractSn
    });
  } else if (props.id && type === 'ts') {
    await paymentContractNewHttp({
      ...data,
      id: props.id,
      isSubmit: false
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject();
  if (props.id && !type)
    await paymentContractNewHttp({
      ...data,
      id: props.id,
      isSubmit: true
    });

  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  emit('otherUpdate', {
    companyName: data.companyName,
    deptName: data.deptName,
    contractName: data.contractName
  });
  return Promise.resolve({});
};
const authFun = async () => {
  // console.log(funiFileTable.value);

  if (funiFileTable.value) {
    let flag = true;
    flag = unref(funiFileTable).verification();
    if (!flag) {
      ElNotification({
        title: '请上传必传要件',
        type: 'warning'
      });
      return Promise.inject();
    }
  }

  let { signDate } = form.value.getValues();
  return Promise.resolve({
    id: props.id,
    isAudit: true,
    signDate
  });
};

const purchaseProjectChange = (obj, bool = false) => {
  purchaseProject.value = obj ?? void 0;
  if (obj && obj.id) getPaymentValidSectionList(bool);
  else {
    sectionList.value = [];
    currentChange();
    setCurrentSelectedSection();
  }
};
const getPaymentValidSectionList = async (bool = false) => {
  if (purchaseProject?.value?.id && props.isEdit) {
    const { list } = await queryPaymentValidSectionListHttp({ purchaseId: purchaseProject?.value?.id });
    sectionList.value = list ?? [];
    if (bool) return;
    if (Array.isArray(sectionList.value) && sectionList.value.length === 1) {
      currentChange(sectionList.value[0]);
      setCurrentSelectedSection();
    } else {
      currentChange();
      setCurrentSelectedSection();
    }
  }
};
const currentChange = e => {
  currentSection.value = e;
};
const setCurrentSelectedSection = () => {
  currentSelectedSection.value = currentSection.value;
  dialogVisible.value = false;
  console.log('currentSelectedSection====>', currentSelectedSection.value);
};

const getData = async type => {
  let isValid = true;
  let needForm = props.type.indexOf('end') < 0 && props.type.indexOf('cancel') < 0;
  if (type !== 'ts' && needForm) {
    let { isValid: v } = await form.value.validate();
    isValid =
      v &&
      (!contract_related.value || (contract_related.value && contract_related.value.verificationTable())) &&
      verificationTable() &&
      verificationTableOne() &&
      verificationTableTow();
  }
  if (!needForm && props.type.indexOf('end') > -1) {
    let { isValid: endV } = await form.value.validateField(['isSealUsed', 'dicSealTypeCode']);
    if (type !== 'ts' && !endV) return Promise.reject();
    let { afterAdjustTaxAmount, afterAdjustContractAmount } = form.value.getValues();
    const { dicSealTypeCode, isSealUsed } = form.value.getValues();
    return {
      afterAdjustTaxAmount,
      afterAdjustContractAmount,
      id: ['新建', 'add'].includes(props.bizName) ? void 0 : props.id,
      lastId: props.lastId || void 0,
      dicSealTypeCode: Array.isArray(dicSealTypeCode) ? dicSealTypeCode.join(',') : void 0,
      isSealUsed
    };
  } else if (!needForm && props.type.indexOf('cancel') > -1) {
    return {
      id: ['新建', 'add'].includes(props.bizName) ? void 0 : props.id,
      lastId: props.lastId || void 0
    };
  }
  if (type == 'ts' || isValid) {
    let formData = form.value.getValues();
    formData = JSON.parse(JSON.stringify(formData));
    if (formData?.projectObj?.id) {
      formData.projectId = formData?.projectObj?.id;
      formData.projectName = formData?.projectObj?.name;
    }
    if (formData?.employeeObj?.id) {
      formData.employeeId = formData?.employeeObj?.id;
      formData.employeeName = formData?.employeeObj?.name;
    }
    if (formData?.contractObj?.id) {
      formData.collectionContractId = formData?.contractObj?.id;
      formData.collectionContractName = formData?.contractObj?.name;
    }

    if (formData?.purchaseProject?.id) {
      console.log('formData?.purchaseProject', formData?.purchaseProject);
      formData.purchaseProjectId = formData?.purchaseProject?.id;
      formData.purchaseProjectSn = formData?.purchaseProject?.sn;
      formData.purchaseProjectName = formData?.purchaseProject?.name;
    }
    if (formData?.collectionServiceId || collectionServiceId) {
      const { serviceSn } =
        collectionService.value.find(
          item => item.value == formData.collectionServiceId || item.value == collectionServiceId
        ) || {};
      formData.collectionServiceSn = serviceSn;
    }
    Reflect.deleteProperty(formData, 'projectObj');
    Reflect.deleteProperty(formData, 'employeeObj');
    Reflect.deleteProperty(formData, 'contractObj');
    Reflect.deleteProperty(formData, 'purchaseProject');

    let multipartyInfoRequestList = [];
    if (formData?.partA) {
      let index = customerListByCurrent.value.findIndex(el => el.id === formData?.partA);
      multipartyInfoRequestList.push({
        dicMultipartyTypeCode: '1',
        customerId: formData?.partA,
        customerName: customerListByCurrent.value[index].name
      });
    }
    if (formData?.partB) {
      multipartyInfoRequestList.push({
        dicMultipartyTypeCode: '2',
        customerId: formData?.partB?.id,
        customerName: formData?.partB?.name
      });
    }
    multipartyInfoRequestList.push(...(contract_related.value ? contract_related.value.getData() : []));
    let payConServiceRequestList = cData.value;
    let payConPlanRequestList = cfData.value;
    Reflect.deleteProperty(formData, 'partB');
    Reflect.deleteProperty(formData, 'partA');
    return {
      id: props.id || '',
      ...formData,
      collectionServiceId: formData?.collectionServiceId || collectionServiceId,
      dicSealTypeCode: formData.dicSealTypeCode
        ? formData.dicSealTypeCode.join(',')
        : detailObj.value?.dicSealTypeCode || null,
      lastId: props.lastId || void 0,
      multipartyInfoRequestList,
      payConServiceRequestList,
      payConPlanRequestList,
      payPurchaseSectionInfoRequestList: currentSelectedSection.value ? [currentSelectedSection.value] : void 0,
      deptName,
      companyName: companyList.value && companyList.value.length ? companyList.value[0]?.name : void 0,
      sealCompanyName: companyList.value && companyList.value.length ? companyList.value[0]?.name : void 0,
      ...otherParams
    };
  } else {
    return false;
  }
};

const verificationTable = () => {
  if (cData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '服务明细必填',
      type: 'warning'
    });
    return false;
  } else {
    return verificationTableForm(contactRef);
  }
};

const verificationTableOne = () => {
  if (cfData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '付款计划必填',
      type: 'warning'
    });
    return false;
  } else {
    return verificationTableForm(contactRefOne);
  }
};
const verificationTableTow = () => {
  if (purchaseProject.value && !currentSelectedSection.value) {
    ElNotification({
      title: '提示',
      message: '请选择关联采购标段',
      type: 'warning'
    });
    return false;
  }
  return true;
};
const verificationTableForm = obj => {
  let v_l = [];
  for (let key in obj) {
    let v = obj[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  console.log('d', d);
  if (d.length > 0) return false;
  return true;
};

/**
 * @description 下一步 提交数据
 * **/
const nextStep = () => {
  return saveDate();
};
const ts = () => {
  return saveDate('ts');
};

defineExpose({
  nextStep,
  ts,
  authFun
});
</script>
<style scope>
.musterW {
  overflow: hidden;
  width: 100%;
  height: 26px;
  padding-left: 5px;
}

.musterW > span {
  color: red;
  display: inline-block;
  /* margin: 3px 3px 0 0; */
  position: relative;
  top: 4px;
  right: 3px;
}
</style>
