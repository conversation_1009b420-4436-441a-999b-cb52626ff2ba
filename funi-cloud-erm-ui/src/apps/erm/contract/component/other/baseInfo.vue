<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-03 11:11:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-15 10:20:04
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/component/other/baseInfo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <GroupTitle title="基本信息"></GroupTitle>
    <funi-form :schema="schema" @get-form="setForm" :rules="rules" :col="3" :border="false"></funi-form>
    <ContractRelated
      ref="contract_related"
      :is-edit="isEdit"
      :list="crData"
      v-if="isMultipartyContract"
      isSymbol
    ></ContractRelated>
    <div v-if="['info', 'audit'].includes(type) && businessId">
      <GroupTitle title="要件信息"> </GroupTitle>
      <FuniFileTable ref="funiFileTable" :onlyShow="type == 'info'" :params="{ businessId }"></FuniFileTable>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onMounted, inject, nextTick, unref } from 'vue';
import { useFormSchema, useRule } from './../../hooks/other/baseInfo.jsx';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import ContractRelated from './../contractRelated/index.vue';
import { baseInfoProps, ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { otherContractSealNewHttp, otherContractSealInfoHttp } from './../../hooks/other/api.js';
import { ElNotification } from 'element-plus';
import { openBusiness } from '@/apps/erm/config/business.js';
import { useAppStore } from '@/stores/useAppStore';
import { useRouter } from 'vue-router';
const appStore = useAppStore();
const router = useRouter();
const form = ref();
const companyId = ref('');
let companyName = '';
let deptName = '';
const sealTypeList = ref([]);
const isMultipartyContract = ref(true);
const isTemporaryContract = ref(true);
const contract_related = ref(null);
const emit = defineEmits(['updateID', 'infoUpdate', 'otherUpdate']);
const loadingStatus = inject('loadingStatus');
const crData = ref([]);
const companyList = ref([]);
const dicBusinessTypeCode = ref();
const funiFileTable = ref()
let classType = {
  add: {
    code: 'ERM_OTHER_CONTRACT_SEAL_ADD',
    name: '其他合同用印申请新增'
  },
  cancel_add: {
    code: 'ERM_OTHER_CONTRACT_SEAL_CANCEL',
    name: '其他合同用印申请作废'
  }
};

const props = defineProps({
  ...baseInfoProps,
  reason: String
});
onMounted(() => {
  getSealType();

  if (props.id) {
    getOtherInfo();
  }
  if (props.type == 'add') {
    setEmployee();
  }
});

const setForm = e => (form.value = e);
const rules = computed(() => {
  return useRule(props.isEdit, isTemporaryContract.value);
});
const schema = computed(() => {
  return useFormSchema({
    isEdit: props.isEdit,
    getCompanyId,
    setCompany,
    setDept,
    sealTypeList: sealTypeList.value,
    resetDeptId,
    setisMultipartyContract,
    setisTemporaryContract,
    isTemporaryContract: isTemporaryContract.value,
    companyList: companyList.value,
    router,
    form: form.value,
    type: props.type,
    dicBusinessTypeCode: dicBusinessTypeCode.value
  });
});
const setEmployee = () => {
  if (props.type === 'add' && props.isEdit) {
    nextTick(() => {
      form.value.setValues({
        employeeObj: {
          name: appStore.user?.username,
          id: appStore.user?.userId
        }
      });
    });
  }
};
const setDept = ({ id, name }) => {
  deptName = name;
};
const setCompany = ({ id, name }) => {
  companyId.value = id;
  companyName = name;
  if (id && name) {
    companyList.value = [
      {
        name,
        value: id
      }
    ];
    nextTick(() => {
      form.value.setValues({
        sealCompanyId: id
      });
    });
  }
};
const resetDeptId = () => {
  form.value.setValues({
    deptId: void 0
  });
};

// 设置是否是多方合同
const setisMultipartyContract = e => {
  isMultipartyContract.value = e;
};
// 设置是否是临时合同
const setisTemporaryContract = async e => {
  isTemporaryContract.value = e;
  if (e == 1) {
    await nextTick();
    unref(form).clearValidate(['partA']);
  }
};
const getCompanyId = e => companyId.value;
const getSealType = () => {
  $http.fetch(ermGlobalApi.dictList, { typeCode: 'seal_type', clientId: appStore.system?.clientId }).then(res => {
    sealTypeList.value = res;
  });
};

const getOtherInfo = async () => {
  loadingStatus.value.status = true;
  let data = await otherContractSealInfoHttp({ otherContractSealId: props.id }).finally(() => {
    loadingStatus.value.status = false;
  });

  let obj = {};
  schema.value.forEach(item => {
    if (item.prop == 'dicSealTypeCode') {
      obj[item.prop] = data.otherContractSealVo[item.prop] ? data.otherContractSealVo[item.prop].split(',') : [];
    } else if (['employeeObj', 'employeeName'].indexOf(item.prop) > -1) {
      obj['employeeObj'] = {
        id: data.otherContractSealVo['employeeId'],
        name: data.otherContractSealVo['employeeName']
      };
      obj['employeeName'] = data.otherContractSealVo['employeeName'];
    } else if (['partA', 'partAName', 'partB', 'partBName'].indexOf(item.prop) > -1) {
      let a = data?.sealConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '1';
      });
      let b = data?.sealConMultipartyInfoVoList?.filter(el => {
        return el.dicMultipartyTypeCode === '2';
      });

      if (a && a.length) {
        obj['partA'] = {
          id: a[0].customerId,
          name: a[0].customerName
        };
        obj['partAName'] = a[0].customerName;
      }
      if (b && b.length) {
        obj['partB'] = {
          id: b[0].customerId,
          name: b[0].customerName
        };
        obj['partBName'] = b[0].customerName;
      }
      setCompany({
        id: data.otherContractSealVo.companyId,
        name: data.otherContractSealVo.companyName
      });
    } else {
      obj[item.prop] = data.otherContractSealVo[item.prop];
    }
    if (props.type === 'add' && item.copy === false) {
      obj[item.prop] = void 0;
    }
  });
  setisMultipartyContract(data.otherContractSealVo.isMultipartyContract);
  setisTemporaryContract(data.otherContractSealVo.isTemporaryContract);
  crData.value = data?.sealConMultipartyInfoVoList?.filter(el => {
    return el.dicMultipartyTypeCode !== '2' && el.dicMultipartyTypeCode !== '1';
  });

  form.value.setValues(obj);
  dicBusinessTypeCode.value = data.otherContractSealVo.dicBusinessTypeCode;
  emit('infoUpdate', {
    ...data.otherContractSealVo,
    id: ['cancel_add', 'add'].includes(props.type) ? void 0 : data.otherContractSealVo.id
  });
};

const saveDate = async type => {
  let data = await getData(type);
  if (!data) return Promise.reject();
  let resData;
  if (!props.id) {
    resData = await openBusiness(classType[props.type]['code'], data, props.reason, classType[props.type]['name']);
    let { businessData } = resData;
    let { id, businessId, contractSn } = businessData.otherContractSealVo;
    emit('infoUpdate', {
      ...businessData.otherContractSealVo,
      id,
      businessId: businessId || props.businessId
    });
    form.value.setValues({
      contractSn
    });
  } else if (props.id && type === 'ts') {
    await otherContractSealNewHttp({
      ...data,
      id: props.id,
      isSubmit: false
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject();
  if (props.id && !type)
    await otherContractSealNewHttp({
      ...data,
      id: props.id,
      isSubmit: true
    });

  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  emit('otherUpdate', {
    companyName: data.companyName,
    deptName: data.deptName,
    contractName: data.contractName
  });
  return Promise.resolve({});
};

const authFun = async () => {
  if (funiFileTable.value) {
    let flag = true;
    flag = unref(funiFileTable).verification();
    if (!flag) {
      ElNotification({
        title: '请上传必传要件',
        type: 'warning'
      });
      return Promise.reject();
    }
  }

  let { signDate } = form.value.getValues();
  return Promise.resolve({
    id: props.id,
    isAudit: true,
    signDate
  });
};

const getData = async type => {
  // TODO 新建退票
  let needForm = props.type.indexOf('cancel') < 0;
  if (!needForm) {
    return {
      id: ['新建', 'add'].includes(props.bizName) ? void 0 : props.id,
      lastId: props.lastId || void 0
    };
  }

  let { isValid } = type == 'ts' ? { isValid: true } : await form.value.validate();
  if (
    type == 'ts' ||
    (isValid && (!contract_related.value || (contract_related.value && contract_related.value.verificationTable())))
  ) {
    let fromData = form.value.getValues();
    let sealConMultipartyInfoRequestList = [];
    if (fromData?.partA?.id) {
      sealConMultipartyInfoRequestList.push({
        dicMultipartyTypeCode: '1',
        customerId: fromData?.partA?.id,
        customerName: fromData?.partA?.name
      });
    }
    if (fromData?.partB?.id) {
      sealConMultipartyInfoRequestList.push({
        dicMultipartyTypeCode: '2',
        customerId: fromData?.partB?.id,
        customerName: fromData?.partB?.name
      });
    }
    sealConMultipartyInfoRequestList.push(...(contract_related.value ? contract_related.value.getData() : []));

    return {
      id: props.id || '',
      ...fromData,
      companyName,
      deptName,
      employeeId: fromData?.employeeObj?.id,
      employeeName: fromData?.employeeObj?.name,
      dicSealTypeCode: fromData.dicSealTypeCode ? fromData.dicSealTypeCode.join(',') : '',
      sealConMultipartyInfoRequestList
    };
  } else {
    return false;
  }
};

/**
 * @description 下一步 提交数据
 * **/
const nextStep = () => {
  return saveDate();
};
const ts = () => {
  return saveDate('ts');
};

defineExpose({
  nextStep,
  ts,
  authFun
});
</script>
