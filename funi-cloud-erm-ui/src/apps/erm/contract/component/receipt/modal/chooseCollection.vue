<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="large" title="选择服务" @close="cancelClick">
      <div>
        <FuniCurd
          ref="funiCurd"
          @get-curd="
            e => {
              funi_curd = e;
            }
          "
          max-height="calc(50vh - 40px)"
          :columns="columnsProject"
          :useSearchV2="false"
          :isShowSearch="true"
          @row-click="getData"
          @select="getDatas"
          @select-all="getDatas"
          :row-class-name="tableRowClassName"
          size="small"
          :searchConfig="searchConfig"
          :lodaData="lodaData"
          :pagination="false"
        >
        </FuniCurd>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" :disabled="rows.length === 0" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref } from 'vue';
import { dicCode } from '@/apps/erm/config/config.jsx';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
const funi_curd = ref(null);
const dialogVisible = ref(false);
const funiCurd = ref(void 0);
const rows = ref([]);
const cData = ref([]);
const isSpecialProject = ref(false);
const columnsProject = computed(() => {
  return [
    {
      type: 'selection',
      width: '55px',
      fixed: 'left',
      selectable: (row, index) => {
        return !row.itemCheckId;
      }
    },
    {
      label: '来源',
      prop: 'dicServiceSourceName'
    },
    {
      label: '服务内容',
      prop: 'dicServiceContentName'
    },
    {
      label: '确收方式',
      prop: 'dicConfirmModeName'
    },
    ...(isSpecialProject
      ? []
      : [
          {
            label: '金额',
            prop: 'includeTaxAmount',
            align: 'right'
          }
        ]),
    {
      label: '税率',
      prop: 'taxRate',
      align: 'right'
    },

    {
      label: '已确收(元)',
      prop: 'confirmReceipt',
      align: 'right'
    },
    ...(isSpecialProject
      ? []
      : [
          {
            label: '未确收(元)',
            prop: 'unconfirmReceipt',
            align: 'right'
          }
        ])
  ];
});
const searchConfig = computed(() => {
  let obj = {
    schema: [
      {
        label: '服务内容',
        component: () => <ErmSelect />,
        prop: 'dicServiceContentCode',
        props: {
          placeholder: '请选择',
          code: dicCode.service_content
        }
      },
      {
        label: '确收方式',
        component: () => <ErmSelect />,
        prop: 'dicConfirmModeCode',
        props: {
          placeholder: '请选择',
          code: dicCode.confirm_mode
        }
      }
    ]
  };
  return obj;
});
const emit = defineEmits(['exportArray']);

const getData = ({ selection }) => {
  rows.value = selection;
};

const getDatas = v => {
  rows.value = v;
};
/**
 *
 * @param {*} page
 * @param {*} params
 */
const lodaData = async (page, params) => {
  let list = cData.value
    .filter(item => (params.dicServiceContentCode ? params.dicServiceContentCode == item.dicServiceContentCode : true))
    .filter(item => (params.dicConfirmModeCode ? params.dicConfirmModeCode == item.dicConfirmModeCode : true));
  return { list };
};
const show = async (data, isSpecialProject) => {
  cData.value = data;
  dialogVisible.value = true;
  isSpecialProject.value = isSpecialProject;
  await nextTick();
};
const tableRowClassName = ({ row }) => {
  if (row.itemCheckId) {
    return 'disabled-row';
  }
  return '';
};

const confirmFunc = () => {
  // console.log(unref(rows.value));
  emit('exportArray', rows.value);
  cancelClick();
};
const cancelClick = () => {
  dialogVisible.value = false;
  rows.value = [];
  cData.value = [];
};
defineExpose({
  show
});
</script>
<style scoped>
@import url('@/apps/erm/config/table_disabled.css');
</style>
