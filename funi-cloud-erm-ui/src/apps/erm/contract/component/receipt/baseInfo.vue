<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-03 11:11:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-15 10:19:01
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/component/receipt/baseInfo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <GroupTitle title="收款信息"> </GroupTitle>
    <funi-form :schema="schema2" @get-form="setForm2" :rules="rules2" :col="3" :border="false"></funi-form>
    <ChooseCollection ref="chooseModal" @exportArray="setCollection"></ChooseCollection>
    <GroupTitle title="基本信息"></GroupTitle>
    <funi-form
      :schema="schema"
      @get-form="setForm"
      :rules="type == 'audit' ? rules : void 0"
      :col="3"
      :border="false"
    ></funi-form>
    <GroupTitle isSymbol title="服务内容">
      <div v-if="type == 'audit' || isEdit">
        <el-button type="primary" @click="addFunc">添加</el-button>
      </div>
    </GroupTitle>
    <funi-curd :rowSelection="false" :columns="sColumns" :data="cData" :pagination="false">
      <template #a>
        <div class="musterW"><span v-show="type == 'audit'">*</span>本次收款金额(元)</div>
      </template>
    </funi-curd>

    <div v-if="['info', 'audit'].includes(type) && businessId">
      <GroupTitle title="要件信息"> </GroupTitle>
      <FuniFileTable :onlyShow="type == 'info'" :params="{ businessId }"></FuniFileTable>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, inject, nextTick, onMounted, onBeforeMount } from 'vue';
import {
  useFormSchema,
  serveColumns,
  useFormSchema2,
  useRule,
  useRule2,
  setForm2Money
} from './../../hooks/receipt/baseInfo.jsx';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import { baseInfoProps, queryIncomeConfirmInfoByServiceIdHttp } from '@/apps/erm/config/config.jsx';
import ChooseCollection from './modal/chooseCollection.vue';
import { collectionContractInfoHttp } from './../../hooks/collection/api.js';
import { receiptNewHttp, receiptInfoHttp, creatBusHttp } from './../../hooks/receipt/api.js';
import { ElNotification } from 'element-plus';
import { openBusiness } from '@/apps/erm/config/business.js';
import { getContractListForNoContractFapiaoHttp } from './../../hooks/invoice/api.js';

const loadingStatus = inject('loadingStatus');

const form = ref();
const form2 = ref();
const contactRef = {};
const cData = ref([]);
const collectionInfo = ref({});
const chooseModal = ref();
const collectionContractId = ref(void 0);
const incomeContractList = ref([]);
const isOpenClause = ref(false);
const contractList = ref([]);
let receiptInfo = void 0;
const projectID = ref('');
const classType = {
  add: {
    code: 'ERM_RECEIPT_ADD',
    name: '收款单新增'
  },
  change_add: {
    code: 'ERM_RECEIPT_CHANGE',
    name: '收款单变更'
  }
};
const receiptCompany = ref({
  id: void 0,
  name: void 0
});
const props = defineProps({
  ...baseInfoProps,
  reason: String
});

onMounted(() => {
  if (props.id) {
    getReceiptInfo();
  }
});
onBeforeMount(async () => {
  let { list } = await getContractListForNoContractFapiaoHttp();
  contractList.value = list;
});

const emit = defineEmits(['updateID', 'infoUpdate']);
const setForm = e => (form.value = e);
const setForm2 = e => (form2.value = e);
const rules = computed(() => {
  return useRule();
});
const rules2 = computed(() => {
  return useRule2(props.type);
});
const schema = computed(() => {
  let isSpecialProject = contractList.value.some(item => item.projectId === projectID.value);
  return useFormSchema({ isEdit: props.type == 'audit' ? true : props.isEdit, getcontractInfo, isSpecialProject });
});

const schema2 = computed(() => {
  return useFormSchema2({
    isEdit: props.isEdit,
    setReceiptCompany,
    incomeContractList: incomeContractList.value,
    isOpenClause: isOpenClause.value,
    type: props.type
  });
});
const iptChange = (index, key, e) => {
  cData.value[index][key] = e;
  if (key === 'currentCollectionAmount') {
    setForm2Money({
      cData,
      form: form2
    });
  }
};

const sColumns = computed(() => {
  let isSpecialProject = contractList.value.some(item => item.projectId === projectID.value);
  return serveColumns({
    isEdit: props.type == 'audit' ? true : props.isEdit,
    iptChange,
    contactRef,
    delFunc,
    type: props.type,
    isSpecialProject
  });
});

const setReceiptCompany = obj => {
  receiptCompany.value = obj;
};

const addFunc = () => {
  let isSpecialProject = contractList.value.some(item => item.projectId === projectID.value);
  chooseModal.value.show(
    collectionInfo.value.collectionServiceList.map(item => {
      let index = cData.value.findIndex(el => el.id === item.id);
      return {
        ...item,
        itemCheckId: index > -1
      };
    }, isSpecialProject)
  );
};

const delFunc = index => {
  cData.value.splice(index, 1);
  let obj = cData.value.find(item => item.isOpenClause);
  isOpenClause.value = obj?.isOpenClause || false;
  form2.value.setValues({ incomeContractSn: void 0 });
};
const setCollection = e => {
  e.map(item => {
    cData.value.push({
      ...item,
      uuId: $utils.guid()
    });
  });
  getIncomeContractList();
};

const getReceiptInfo = async () => {
  let data = {};
  loadingStatus.value.status = true;
  if (props.type == 'change_add') {
    data = await creatBusHttp({ id: props.id, dicBusinessTypeCode: classType[props.type].code }).catch(() => {
      loadingStatus.value.status = false;
    });
  } else {
    data = await receiptInfoHttp({ receiptId: props.id }).finally(() => {
      loadingStatus.value.status = false;
    });
  }

  receiptInfo = data;
  let { incomeSn, id: incomeId, contractName: incomeName, includeTaxAmount } = data?.incomeConfirmInfoVo || {};
  let incomeContractName = `${incomeSn || '--'}（${includeTaxAmount || '--'}元）`;
  data.incomeContractName = incomeContractName;

  let {
    receiptSn,
    dicReceiptStatusName,
    contractId,
    contractName,
    employeeId,
    employeeName,
    customerId,
    customerName,
    createTime,
    takeEffectTime,
    contractSn
  } = data;

  if (props.type == 'add') {
    receiptSn = void 0;
    dicReceiptStatusName = void 0;
    createTime = void 0;
    takeEffectTime = void 0;
  }

  cData.value = data.receiptServiceList;
  getIncomeContractList(false);
  contractId && (await getcontractInfo({ id: contractId, contractSn }, false));
  form.value.setValues({
    receiptSn,
    dicReceiptStatusName,
    employeeObj: {
      id: employeeId,
      name: employeeName
    },
    partA: {
      id: customerId,
      name: customerName
    },
    createTime,
    takeEffectTime,
    ...(!props.isEdit
      ? {}
      : {
          contractObj: {
            id: contractId,
            name: contractName ? `${contractName}(${contractSn || '--'})` : '--',
            title: contractName
          }
        })
  });
  let obj = {};
  schema2.value.forEach(item => {
    if (item.prop == 'receiptSignerId') {
      let ids = data.receiptSignerId ? data.receiptSignerId.split(',') : [];
      let names = data.receiptSignerName ? data.receiptSignerName.split(',') : [];
      obj[item.prop] = ids.map((el, index) => {
        return {
          type: 'user',
          id: el,
          name: names[index]
        };
      });
    } else if (item.prop == 'incomeContractObj') {
      obj[item.prop] = {
        id: incomeId,
        name: data.incomeContractName,
        title: incomeName
      };
    } else {
      obj[item.prop] = data[item.prop];
    }
  });
  form2.value.setValues(obj);

  if (props.type == 'add') {
    data.id = void 0;
    data.businessId = void 0;
  }
  emit('infoUpdate', data);
  setReceiptCompany({
    id: data.receiptCompanyId,
    name: data.receiptCompanyName
  });
};

const getcontractInfo = async ({ contractSn, id }, bool = true) => {
  collectionContractId.value = id;
  loadingStatus.value.status = true;
  let params = {
    collectionContractId: contractSn ? void 0 : id,
    collectionContractSn: contractSn ? contractSn : void 0
  };
  const data = await collectionContractInfoHttp(params).finally(() => {
    loadingStatus.value.status = false;
  });
  collectionInfo.value = data;
  let obj = {};
  let exclude = ['receiptSn', 'dicReceiptStatusName', 'createTime', 'takeEffectTime'];
  projectID.value = data.projectId;
  schema.value.forEach(element => {
    if (exclude.indexOf(element.prop) < 0) {
      if (element.prop == 'employeeObj') {
        obj[element.prop] = {
          id: collectionInfo.value.employeeId,
          name: collectionInfo.value.employeeName
        };
      } else if (element.prop == 'projectObj') {
        obj[element.prop] = {
          id: collectionInfo.value.projectId,
          name: collectionInfo.value.projectName
        };
      } else if (element.prop == 'partA') {
        let a = collectionInfo.value.multipartyInfoList.filter(item => item.dicMultipartyTypeCode == '1');
        obj[element.prop] = a.length
          ? {
              id: a[0].customerId,
              name: `${a[0].customerName}(${a[0].suCreditCode || '--'})`
            }
          : void 0;
      } else if (element.prop == 'contractObj') {
        let { contractName, contractSn } = collectionInfo.value;
        obj[element.prop] = {
          id: collectionInfo.value.id,
          name: contractName ? `${contractName}(${contractSn || '--'})` : '--',
          title: contractName
        };
      } else {
        obj[element.prop] = collectionInfo.value[element.prop];
      }
    }
  });
  console.log('obj', obj);
  form.value.setValues(obj);
  if (bool) {
    cData.value = [];
    // setForm2Info();
  }
};

const getIncomeContractList = async (bool = true) => {
  let obj = cData.value.find(item => item.isOpenClause);
  isOpenClause.value = obj?.isOpenClause || false;
  bool && form2.value.setValues({ incomeContractSn: void 0 });
  let serviceId = obj?.serviceId || obj?.id;
  if (!serviceId) return;
  let { list } = await queryIncomeConfirmInfoByServiceIdHttp({
    serviceId
  });
  incomeContractList.value = list;
};

const saveDate = async type => {
  let data = await getData(type);
  if (!data) return Promise.reject();

  if (!props.id) {
    let resData = await openBusiness(classType[props.type].code, data, props.reason, classType[props.type].name);
    let { businessData } = resData;
    let { id, businessId, receiptSn } = businessData;
    emit('updateID', {
      id,
      businessId: businessId || props.businessId
    });
    form.value.setValues({
      receiptSn
    });
  } else if (props.id && type === 'ts') {
    if (props.type == 'audit') loadingStatus.value.status = true;
    await receiptNewHttp({
      ...data,
      id: props.id,
      isSubmit: false,
      isAudit: false
    }).finally(() => {
      if (props.type == 'audit') loadingStatus.value.status = false;
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject();
  if (props.id && !type)
    await receiptNewHttp({
      ...data,
      id: props.id,
      isSubmit: true
    });

  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }

  return Promise.resolve({});
};

const getData = async type => {
  let v = false;
  let v2 = false;
  if (['change_add', 'change_edit'].includes(props.type)) {
    let { remark } = form2.value.getValues();
    return {
      remark,
      lastId: props.lastId || void 0,
      dicBusinessTypeCode: 'ERM_RECEIPT_CHANGE'
    };
  } else {
    if (!type) {
      let isV = await form.value.validate();
      let isV2 = await form2.value.validate();
      v = isV.isValid;
      v2 = isV2.isValid;
    }

    let { isValid } = type == 'ts' ? { isValid: true } : { isValid: v && v2 };
    if (type == 'ts' || (isValid && verificationTable() && verificationMoney())) {
      let formData = form.value.getValues();
      let formData2 = getForm2Value();
      let { companyId, companyName, deptId, deptName, contractSn } = collectionInfo.value;
      let requestParams = {
        contractId: formData?.contractObj?.id,
        contractName: formData?.contractObj?.name,
        employeeId: formData?.employeeObj?.id,
        employeeName: formData?.employeeObj?.name,
        customerId: formData?.partA?.id,
        customerName: formData?.partA?.name,
        contractSn,
        receiptServiceList: cData.value.map(item => {
          return {
            serviceId: item?.serviceId || item?.id,
            serviceSn: item?.serviceSn,
            currentCollectionAmount: item.currentCollectionAmount
          };
        })
      };

      let incomeContractId = void 0;
      let object = void 0;

      if (formData2?.incomeContractSn) {
        object = incomeContractList.value.find(item => item.incomeSn == formData2?.incomeContractSn);
      }
      incomeContractId = object?.incomeContractId || object?.id;
      return {
        ...requestParams,
        companyId,
        companyName,
        receiptCompanyName: receiptCompany.value.name,
        deptId,
        deptName,
        ...formData2,
        receiptSignerId:
          formData2?.receiptSignerId && formData2?.receiptSignerId instanceof Array
            ? formData2?.receiptSignerId.map(item => item.id).join(',')
            : formData2?.receiptSignerId,
        incomeContractId
      };
    }
  }
};

const verificationTable = () => {
  if (props.type !== 'audit') return true;
  if (cData.value.length === 0) {
    ElNotification({
      title: '提示',
      message: '请选择服务内容',
      type: 'warning'
    });
    return false;
  }

  return verificationTableForm(contactRef);
};

const verificationMoney = () => {
  if (props.type !== 'audit') return true;
  let num = 0;
  let { preReceiptAmount } = form2.value.getValues();
  cData.value.forEach(item => {
    num = $utils.round(num + item.currentCollectionAmount * 1, 2);
  });
  if (num !== Number(preReceiptAmount)) {
    ElNotification({
      title: '提示',
      message: '服务内容本次收款金额不等于收款信息的本次收款金额',
      type: 'warning'
    });
    return false;
  }
  return true;
};
const verificationTableForm = obj => {
  let v_l = [];
  for (let key in obj) {
    let v = obj[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};

const getForm2Value = () => {
  if (props.type == 'audit') {
    let { preReceiptAmount, collectionCompanyName, receiptCompanyId, receiptSignerId, remark } = receiptInfo;
    let { taxAmount, sufReceiptAmount, incomeContractSn } = form2.value.getValues();
    return {
      preReceiptAmount,
      collectionCompanyName,
      receiptCompanyId,
      receiptSignerId,
      remark,
      taxAmount,
      sufReceiptAmount,
      incomeContractSn
    };
  }
  return form2.value.getValues();
};

/**
 * @description 下一步 提交数据
 * **/
const nextStep = () => {
  return saveDate();
};
const ts = () => {
  return saveDate('ts');
};
const authFun = async () => {
  let data = await getData();
  if (data) {
    return Promise.resolve({
      ...data,
      id: props.id,
      isAudit: true
    });
  }
  return Promise.reject();
};

defineExpose({
  nextStep,
  ts,
  authFun
});
</script>
<style scoped>
.musterW {
  overflow: hidden;
  width: 100%;
  height: 26px;
  padding-left: 5px;
}
.musterW > span {
  color: red;
  display: inline-block;
  /* margin: 3px 3px 0 0; */
  position: relative;
  top: 4px;
  right: 3px;
}
</style>
