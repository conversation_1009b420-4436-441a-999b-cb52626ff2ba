<template>
  <FuniCurdV2
    ref="curdRef"
    :isShowSearch="true"
    :queryFields="queryFields"
    :sortFields="sortFields"
    :columnFilters="columnFilters"
    :columns="columns"
    :loading="loading"
    :lodaData="lodaData"
  >
  </FuniCurdV2>
</template>

<script setup lang="jsx">
import { ref, computed, watch, onMounted, reactive } from 'vue';
import { queryReceiptListByContractHttp } from '../../hooks/collection/api';
import { useAppStore } from '@/stores/useAppStore';
import { useRouter } from 'vue-router';
const props = defineProps({
  contractSn: {
    type: String,
    default: ''
  }
});
const curdRef = ref();
const queryFields = ref([]);
const sortFields = ref([]);
const columnFilters = ref([]);
const appStore = useAppStore();
const loading = ref(false);
const router = useRouter();
const searchConfig = reactive({
  pageCode: 'ermContCollectReceiptList'
});
const columns = ref([
  {
    label: '收款单编号',
    prop: 'receiptSn',
    render: ({ row, index }) => {
      return (
        <el-button type="primary" link onClick={() => goDetail(row)}>
          {row.receiptSn}
        </el-button>
      );
    }
  },
  {
    label: '收款单状态',
    prop: 'dicReceiptStatusName'
  },
  {
    label: '生效时间',
    prop: 'takeEffectTime'
  },

  {
    label: '本次收款金额含税(元)',
    prop: 'preReceiptAmount'
  },
  {
    label: '收款时间',
    prop: 'receiptTime'
  },
  {
    label: '负责人',
    prop: 'employeeName'
  },
  {
    label: '归属公司',
    prop: 'companyName'
  },
  {
    label: '归属部门',
    prop: 'deptName'
  },
  {
    label: '项目名称',
    prop: 'projectName'
  },
  {
    label: '客户名称',
    prop: 'customerName'
  },
  {
    label: '服务内容',
    prop: 'dicServiceContentName'
  },
  {
    label: '确收方式',
    prop: 'dicConfirmModeName'
  },
  {
    label: '税率',
    prop: 'taxRate'
  }
]);

onMounted(() => {
  fetchAdvancedQueryConfig();
});
watch(
  () => props.contractSn,
  () => {
    curdRef.value.reload({ resetPage: true });
  }
);
/**
 * 跳转详情
 */
const goDetail = row => {
  router.push({
    name: 'ermContractReceiptInfo',
    query: {
      title: row.contractName || '收款单',
      bizName: '详情',
      type: 'info',
      id: row.id,
      tab: ['收款单', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
/**
 *
 * @param {*} page
 * @param {*} params
 */
const lodaData = async (page, params) => {
  try {
    if (!props.contractSn) {
      return { list: [] };
    }
    loading.value = true;
    const resData = await queryReceiptListByContractHttp({
      contractSn: props.contractSn,
      ...page,
      ...params
    });
    return resData;
  } finally {
    loading.value = false;
  }
};
/**
 * 查询高级查询、列排序配置
 */
function fetchAdvancedQueryConfig() {
  const advQueryURL = `${searchConfig.service || appStore.service}/advQueryList/getAdvancedQueryList`;
  $http.fetch(advQueryURL, { pageCode: searchConfig.pageCode }).then(res => {
    queryFields.value = (res.list || []).sort((a, b) => a.sort - b.sort);
    sortFields.value = res.sort || [];
    columnFilters.value = res.column || [];
  });
}
</script>

<style lang="scss" scoped></style>
