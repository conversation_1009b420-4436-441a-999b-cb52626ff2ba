<template>
  <funi-dialog
    v-model="dialogVisible"
    title="调整合同相关方"
    size="small"
    :close-on-click-modal="true"
    :hideFooter="null"
  >
    <div style="min-height: 180px">
      <FuniForm :schema="schema" @get-form="setForm" :border="false" :rules="rules" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="default" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="onOk">确定</el-button>
      </span>
    </template>
  </funi-dialog>
</template>

<script setup lang="jsx">
import { ref, computed, unref, nextTick } from 'vue';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import { apiUrl } from '../../hooks/collection/api';
import { queryPayConMultipartyInfoListHttp, changeContractRelatedPartiesHttp } from '../../hooks/collection/api';
const emit = defineEmits(['output']);
const dialogVisible = ref(false);
const loading = ref(false);
const form = ref();
const currentRow = ref();
const dicMultipartyTypeList = ref([]);
const dicMultipartyTypeCode = ref();
const customerNameValue = ref('');
const setForm = e => {
  form.value = e;
};

// 表单的  schema
const schema = computed(() => {
  return [
    {
      label: '请选择类型',
      component: 'funi-select',
      prop: 'dicMultipartyTypeCode',
      props: {
        clearable: true,
        placeholder: '请选择',
        options: dicMultipartyTypeList.value
      },
      on: {
        change: val => {
          dicMultipartyTypeCode.value = void 0;
          setTimeout(async () => {
            dicMultipartyTypeCode.value = val;
            const { customerId, customerName, suCreditCode } =
              dicMultipartyTypeList.value.find(item => item.dicMultipartyTypeCode === val) || {};
            await nextTick();
            customerNameValue.value = customerName;
            form.value.setValues({
              customerObj: {
                id: customerId,
                name: `${customerName}(${suCreditCode})`
              }
            });
          });
        }
      }
    },
    {
      label: '请调整相关方',
      component: () =>
        dicMultipartyTypeCode.value ? (
          <InfiniteSelect
            api={apiUrl.queryCustomerList}
            defaultProps={{
              keyWord: 'keyword',
              name: 'customerName',
              id: 'id',
              sn: 'suCreditCode',
              nameEval: "item[defaultProps.value.name]+'('+item[defaultProps.value.sn]+')'"
            }}
            otherParams={{
              dicCustomerTypeCode: dicMultipartyTypeCode.value === '1' ? '1' : void 0
            }}
            onChange={(id, obj) => {
              customerNameValue.value = obj?.customerName;
            }}
          ></InfiniteSelect>
        ) : (
          <el-select></el-select>
        ),
      prop: 'customerObj'
    }
  ];
});
const rules = unref({
  dicMultipartyTypeCode: [{ required: true, message: '必填', trigger: 'blur' }],
  customerObj: [{ required: true, message: '必填', trigger: 'blur' }]
});

const show = row => {
  dicMultipartyTypeCode.value = void 0;
  currentRow.value = row;
  dialogVisible.value = true;
  init(row);
};
const init = row => {
  queryPayConMultipartyInfoListHttp({
    contractId: row.id
  }).then(res => {
    let arr = res.list
      .filter(item => item.dicMultipartyTypeCode !== '2')
      .map(item => {
        return {
          label: item.dicMultipartyTypeName,
          value: item.dicMultipartyTypeCode,
          ...item
        };
      });
    dicMultipartyTypeList.value = arr.sort((a, b) => Number(a.dicMultipartyTypeCode) - Number(b.dicMultipartyTypeCode));
  });
};
async function onOk() {
  try {
    let { isValid } = await form.value.validate();
    if (!isValid) return;
    const { customerObj, dicMultipartyTypeCode } = form.value.getValues();
    const { id } = dicMultipartyTypeList.value.find(item => item.dicMultipartyTypeCode === dicMultipartyTypeCode) || {};
    loading.value = true;
    await changeContractRelatedPartiesHttp({
      dicMultipartyTypeCode,
      contractId: currentRow.value?.id,
      customerId: customerObj.id,
      customerName: customerNameValue.value,
      id
    });
    dialogVisible.value = false;
    emit('output', true);
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
}

defineExpose({ show });
</script>

<style lang="scss" scoped></style>
