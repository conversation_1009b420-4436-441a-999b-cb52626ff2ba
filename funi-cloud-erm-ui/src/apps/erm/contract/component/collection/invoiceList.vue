<template>
  <FuniCurdV2
    ref="curdRef"
    :isShowSearch="true"
    :queryFields="queryFields"
    :sortFields="sortFields"
    :columnFilters="columnFilters"
    :columns="columns"
    :loading="loading"
    :lodaData="lodaData"
  >
  </FuniCurdV2>
</template>

<script setup lang="jsx">
import { ref, computed, watch, onMounted, reactive } from 'vue';
import { queryFapiaoListByContractSnHttp } from '../../hooks/collection/api';
import { useAppStore } from '@/stores/useAppStore';
import { useRouter } from 'vue-router';
const props = defineProps({
  contractSn: {
    type: String,
    default: ''
  }
});
const curdRef = ref();
const queryFields = ref([]);
const sortFields = ref([]);
const columnFilters = ref([]);
const appStore = useAppStore();
const loading = ref(false);
const router = useRouter();
const searchConfig = reactive({
  pageCode: 'ermContCollectInvoiceList'
});
const columns = ref([
  {
    label: '开票申请编号',
    prop: 'applySn',
    render: ({ row, index }) => {
      return (
        <el-button type="primary" link onClick={() => goDetail(row)}>
          {row.applySn}
        </el-button>
      );
    }
  },
  {
    label: '状态',
    prop: 'dicFapiaoAppStatusName'
  },
  {
    label: '开票金额(元)',
    prop: 'fapiaoAmount'
  },
  {
    label: '生效时间',
    prop: 'takeEffectTime'
  },

  {
    label: '负责人',
    prop: 'employeeName'
  },
  {
    label: '归属公司',
    prop: 'companyName'
  },
  {
    label: '归属部门',
    prop: 'deptName'
  },
  {
    label: '服务项目',
    prop: 'projectName'
  },
  {
    label: '乙方',
    prop: 'secondCustomerName'
  },
  {
    label: '(客户名称)甲方',
    prop: 'firstCustomerName'
  }
]);

onMounted(() => {
  fetchAdvancedQueryConfig();
});
watch(
  () => props.contractSn,
  () => {
    curdRef.value.reload({ resetPage: true });
  }
);
/**
 * 跳转详情
 */
const goDetail = row => {
  router.push({
    name: 'ermContractInvoiceInfo',
    query: {
      title: row.contractName || '开票申请',
      bizName: '详情',
      type: 'info',
      tab: ['开票申请', row.contractName || '', '详情'].join('-'),
      id: row.dicFapiaoAppDoingBusCode ? row.lastId || row.id : row.id,
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
/**
 *
 * @param {*} page
 * @param {*} params
 */
const lodaData = async (page, params) => {
  try {
    if (!props.contractSn) {
      return { list: [] };
    }
    loading.value = true;
    const resData = await queryFapiaoListByContractSnHttp({
      contractSn: props.contractSn,
      ...page,
      ...params
    });
    return resData;
  } finally {
    loading.value = false;
  }
};
/**
 * 查询高级查询、列排序配置
 */
function fetchAdvancedQueryConfig() {
  const advQueryURL = `${searchConfig.service || appStore.service}/advQueryList/getAdvancedQueryList`;
  $http.fetch(advQueryURL, { pageCode: searchConfig.pageCode }).then(res => {
    queryFields.value = (res.list || []).sort((a, b) => a.sort - b.sort);
    sortFields.value = res.sort || [];
    columnFilters.value = res.column || [];
  });
}
</script>

<style lang="scss" scoped></style>
