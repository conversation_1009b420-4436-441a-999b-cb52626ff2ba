<template>
  <div>
    <funi-list-page-v2 ref="pageList" :cardTab="useCardTab" />
    <checkSection v-model="visible" @close="close" @confirm="confirm" :api="queryValidRecruitmentSchemeListHttp" />
    <uplaodFileModal ref="uplaodFileModalRef" />
    <CancelConfirmModal ref="cancelConfirmModalRef" @confirm="handleCancelConfirm" @cancel="handleCancelCancel" />
  </div>
</template>

<script lang="jsx" setup>
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElNotification } from 'element-plus';
import checkSection from '@/apps/erm/purchase/evaluation_result/component/checkSection.vue';
import uplaodFileModal from '@/apps/erm/purchase/evaluation_result/component/uploadFileModal/index.vue';
import CancelConfirmModal from '@/apps/erm/purchase/component/CancelConfirmModal.vue';
import CancelConfirmModal from '@/apps/erm/purchase/component/CancelConfirmModal.vue';
import { useColumns, useBtnsConfig } from '@/apps/erm/purchase/evaluation_result/hooks/useEvaluationList.jsx';
import {
  queryRecruitmentResultListHttp,
  queryValidRecruitmentSchemeListHttp,
  apiUrl,
  queryDownstreamBusinessHttp,
  batchCancelBusinessHttp,
  checkCancelConditionHttp
} from '@/apps/erm/purchase/evaluation_result/hooks/useEvaluationApi.jsx';
import { submitBusiness } from '@/apps/erm/config/business.js';
import { expotrFunction } from '@/apps/erm/config/config.jsx';

const router = useRouter();
let rowData = ref(void 0);
let pageList = ref();
const queryData = ref(void 0);
let visible = ref(false);
let uplaodFileModalRef = ref();
const cancelConfirmModalRef = ref();
let cancelDisabled = ref(true);

const deleFn = async row => {
  if (row) {
    await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
    ElNotification({
      title: '删除成功',
      type: 'success'
    });
  }
  pageList.value.reload({ resetPage: false });
};

const uploadFn = row => {
  uplaodFileModalRef.value.show(row.businessId, row.takeEffectTime);
};

const editFn = row => {
  router.push({
    name: 'ermPurchaseEvaluationAdd',
    query: {
      bizName: '编辑',
      recruitmentId: row.managementId,
      type: 'edit',
      tab: ['选聘方案', row.sectionName, '编辑'].join('-'),
      businessId: row.businessId,
      title: row.sectionName || '选聘结果审批',
      dicRecruitmentTypeCode: row.dicRecruitmentTypeCode,
      id: row.id
    }
  });
};
const examineFn = row => {
  router.push({
    name: 'ermPurchaseEvaluationInfo',
    query: {
      bizName: '审核',
      title: row.sectionName || '选聘结果审批',
      type: 'audit',
      recruitmentId: row.managementId,
      id: row.id,
      tab: ['选聘结果审批', row.sectionName, '审核'].join('-'),
      businessId: row.businessId
    }
  });
};
const seeBusinessInfo = row => {
  router.push({
    name: 'ermPurchaseEvaluationInfo',
    query: {
      bizName: '审核',
      title: row.sectionName || '选聘结果审批',
      type: 'info',
      id: row.id,
      tab: ['选聘结果审批', row.sectionName, '详情'].join('-'),
      businessId: row.businessId
    }
  });
};
const addFn = () => {
  visible.value = true;
};
//导出
const exportFn = () => {
  expotrFunction({ url: apiUrl.queryRecruitmentResultListExport, params: queryData.value, FileName: '选聘结果审批' });
};
const loadData = async (params, queryParams) => {
  rowData.value = void 0;
  queryData.value = queryParams;
  pageList.value.activeCurd.resetCurrentRow();
  let resData = await queryRecruitmentResultListHttp({ ...params, ...queryParams });
  return resData;
};

const confirm = row => {
  visible.value = false;
  router.push({
    name: 'ermPurchaseEvaluationAdd',
    query: {
      title: row.sectionName || '选聘结果审批',
      bizName: '新建',
      type: 'add',
      tab: '选聘结果审批-新建',
      recruitmentId: row.recruitmentSchemeId,
      dicRecruitmentTypeCode: row.dicRecruitmentTypeCode,
      sectionId: row.id
    }
  });
};
const seeDetails = row => {
  router.push({
    name: 'ermPurchaseEvaluationInfo',
    query: {
      bizName: '详情',
      title: '选聘结果审批-详情',
      type: 'info',
      id: row.id,
      tab: ['选聘结果审批', row.sectionName, '详情'].join('-'),
      businessId: row.businessId
    }
  });
};

const close = () => {
  visible.value = false;
};

// 新增作废功能
const cancelFn = async () => {
  try {
    // 1. 检查作废前置条件
    const conditionCheck = await checkCancelConditionHttp({
      businessId: rowData.value.businessId,
      businessType: rowData.value.dicBusinessTypeCode
    });

    if (!conditionCheck.canCancel) {
      ElNotification({
        message: conditionCheck.message || '当前业务不满足作废条件',
        type: 'error'
      });
      return;
    }

    // 2. 查询下游关联业务
    const downstreamResult = await queryDownstreamBusinessHttp({
      businessId: rowData.value.businessId,
      businessType: rowData.value.dicBusinessTypeCode
    });

    // 3. 显示作废确认弹窗
    cancelConfirmModalRef.value.show(rowData.value, downstreamResult.downstreamList || []);
  } catch (error) {
    ElNotification({
      message: '检查作废条件失败',
      type: 'error'
    });
  }
};

// 处理作废确认
const handleCancelConfirm = async (cancelData) => {
  try {
    // 调用批量作废接口
    await batchCancelBusinessHttp({
      mainBusinessId: cancelData.mainBusiness.businessId,
      businessList: [
        {
          businessId: cancelData.mainBusiness.businessId,
          businessType: cancelData.mainBusiness.dicBusinessTypeCode,
          cancelReason: cancelData.cancelReason
        },
        ...cancelData.downstreamList.map(item => ({
          businessId: item.businessId,
          businessType: item.businessType,
          cancelReason: cancelData.cancelReason
        }))
      ]
    });

    ElNotification({
      message: '作废流程已发起',
      type: 'success'
    });

    cancelConfirmModalRef.value.close();
    pageList.value.reload({ resetPage: false });
  } catch (error) {
    ElNotification({
      message: '作废失败',
      type: 'error'
    });
  }
};

// 处理作废取消
const handleCancelCancel = () => {
  // 取消作废操作
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: useColumns({ seeDetails, deleFn, editFn, examineFn, seeBusinessInfo, uploadFn }),
        btns: useBtnsConfig({ addFn, exportFn, cancelFn, cancelDisabled: cancelDisabled.value }),
        lodaData: loadData,
        fixedButtons: true,
        checkOnRowClick: true,
        reloadOnActive: true,
        on: {
          rowClick: selection => {
            rowData.value = selection.row;

            // 设置作废按钮状态：当前业务状态为"有效"且没有正在办理中的业务流程时可作废
            cancelDisabled.value = !(
              selection.row.dicRecruitmentResultStatusCode == '1' &&
              !selection.row.dicRecruitmentResultDoingBusCode
            );
          }
        }
      }
    }
  ];
});
</script>
<style lang="scss" scoped></style>
