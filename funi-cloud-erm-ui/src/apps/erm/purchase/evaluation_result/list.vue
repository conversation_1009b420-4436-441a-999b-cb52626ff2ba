<template>
  <div>
    <funi-list-page-v2 ref="pageList" :cardTab="useCardTab" />
    <checkSection v-model="visible" @close="close" @confirm="confirm" :api="queryValidRecruitmentSchemeListHttp" />
    <uplaodFileModal ref="uplaodFileModalRef" />
    <CancelConfirmModal ref="cancelConfirmModalRef" @confirm="handleCancelConfirm" @cancel="handleCancelCancel" />
  </div>
</template>

<script lang="jsx" setup>
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElNotification } from 'element-plus';
import checkSection from '@/apps/erm/purchase/evaluation_result/component/checkSection.vue';
import uplaodFileModal from '@/apps/erm/purchase/evaluation_result/component/uploadFileModal/index.vue';
import CancelConfirmModal from '@/apps/erm/purchase/component/CancelConfirmModal.vue';
import { useColumns, useBtnsConfig } from '@/apps/erm/purchase/evaluation_result/hooks/useEvaluationList.jsx';
import {
  queryRecruitmentResultListHttp,
  queryValidRecruitmentSchemeListHttp,
  apiUrl,
  queryDownstreamBusinessHttp,
  batchCancelBusinessHttp,
  checkCancelConditionHttp
} from '@/apps/erm/purchase/evaluation_result/hooks/useEvaluationApi.jsx';
import { submitBusiness } from '@/apps/erm/config/business.js';
import { expotrFunction } from '@/apps/erm/config/config.jsx';

const router = useRouter();
let rowData = ref(void 0);
let pageList = ref();
const queryData = ref(void 0);
let visible = ref(false);
let uplaodFileModalRef = ref();

const deleFn = async row => {
  if (row) {
    await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
    ElNotification({
      title: '删除成功',
      type: 'success'
    });
  }
  pageList.value.reload({ resetPage: false });
};

const uploadFn = row => {
  uplaodFileModalRef.value.show(row.businessId, row.takeEffectTime);
};

const editFn = row => {
  router.push({
    name: 'ermPurchaseEvaluationAdd',
    query: {
      bizName: '编辑',
      recruitmentId: row.managementId,
      type: 'edit',
      tab: ['选聘方案', row.sectionName, '编辑'].join('-'),
      businessId: row.businessId,
      title: row.sectionName || '选聘结果审批',
      dicRecruitmentTypeCode: row.dicRecruitmentTypeCode,
      id: row.id
    }
  });
};
const examineFn = row => {
  router.push({
    name: 'ermPurchaseEvaluationInfo',
    query: {
      bizName: '审核',
      title: row.sectionName || '选聘结果审批',
      type: 'audit',
      recruitmentId: row.managementId,
      id: row.id,
      tab: ['选聘结果审批', row.sectionName, '审核'].join('-'),
      businessId: row.businessId
    }
  });
};
const seeBusinessInfo = row => {
  router.push({
    name: 'ermPurchaseEvaluationInfo',
    query: {
      bizName: '审核',
      title: row.sectionName || '选聘结果审批',
      type: 'info',
      id: row.id,
      tab: ['选聘结果审批', row.sectionName, '详情'].join('-'),
      businessId: row.businessId
    }
  });
};
const addFn = () => {
  visible.value = true;
};
//导出
const exportFn = () => {
  expotrFunction({ url: apiUrl.queryRecruitmentResultListExport, params: queryData.value, FileName: '选聘结果审批' });
};
const loadData = async (params, queryParams) => {
  rowData.value = void 0;
  queryData.value = queryParams;
  pageList.value.activeCurd.resetCurrentRow();
  let resData = await queryRecruitmentResultListHttp({ ...params, ...queryParams });
  return resData;
};

const confirm = row => {
  visible.value = false;
  router.push({
    name: 'ermPurchaseEvaluationAdd',
    query: {
      title: row.sectionName || '选聘结果审批',
      bizName: '新建',
      type: 'add',
      tab: '选聘结果审批-新建',
      recruitmentId: row.recruitmentSchemeId,
      dicRecruitmentTypeCode: row.dicRecruitmentTypeCode,
      sectionId: row.id
    }
  });
};
const seeDetails = row => {
  router.push({
    name: 'ermPurchaseEvaluationInfo',
    query: {
      bizName: '详情',
      title: '选聘结果审批-详情',
      type: 'info',
      id: row.id,
      tab: ['选聘结果审批', row.sectionName, '详情'].join('-'),
      businessId: row.businessId
    }
  });
};

const close = () => {
  visible.value = false;
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: useColumns({ seeDetails, deleFn, editFn, examineFn, seeBusinessInfo, uploadFn }),
        btns: useBtnsConfig({ addFn, exportFn }),
        lodaData: loadData,
        fixedButtons: true,
        checkOnRowClick: true,
        reloadOnActive: true,
        on: {
          rowClick: selection => {
            rowData.value = selection.row;
          }
        }
      }
    }
  ];
});
</script>
<style lang="scss" scoped></style>
