export const apiUrl = {
  queryRecruitmentResultList: '/erm/recruitmentResultList/queryRecruitmentResultList',
  queryRecruitmentResultListExport: '/erm/recruitmentResultList/queryRecruitmentResultListExport',
  queryValidRecruitmentSchemeList: '/erm/recruitmentSchemeList/queryValidRecruitmentSchemeList',
  recruitmentResultInfo: '/erm/recruitmentResult/info',
  recruitmentResultNew: '/erm/recruitmentResult/new',
  querySupplierInfoListByRecruitmentSchemeId: '/erm/supplierInfoList/querySupplierInfoListByRecruitmentSchemeId',
  verifyWorkflowNode: '/erm/recruitmentResult/verifyWorkflowNode',
  // 新增作废相关接口
  queryDownstreamBusiness: '/erm/recruitmentResult/queryDownstreamBusiness',
  batchCancelBusiness: '/erm/recruitmentResult/batchCancelBusiness',
  checkCancelCondition: '/erm/recruitmentResult/checkCancelCondition'
};

export const queryRecruitmentResultListHttp = params => {
  return $http.post(apiUrl.queryRecruitmentResultList, params);
};
export const queryValidRecruitmentSchemeListHttp = params => {
  return $http.post(apiUrl.queryValidRecruitmentSchemeList, params);
};
export const recruitmentResultInfoHttp = params => {
  return $http.fetch(apiUrl.recruitmentResultInfo, params);
};
export const recruitmentResultNewHttp = params => {
  return $http.post(apiUrl.recruitmentResultNew, params);
};
export const querySupplierInfoListHttp = params => {
  return $http.post(apiUrl.querySupplierInfoListByRecruitmentSchemeId, params);
};
export const verifyWorkflowNodeHttp = params => {
  return $http.fetch(apiUrl.verifyWorkflowNode, params);
};

// 新增作废相关API函数
export const queryDownstreamBusinessHttp = params => {
  return $http.post(apiUrl.queryDownstreamBusiness, params);
};

export const batchCancelBusinessHttp = params => {
  return $http.post(apiUrl.batchCancelBusiness, params);
};

export const checkCancelConditionHttp = params => {
  return $http.post(apiUrl.checkCancelCondition, params);
};
