import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';
import ermHooks from '@/apps/erm/hooks/index.js';
import { erm_intl } from '@/apps/erm/hooks/intl';

const useColumns = ({ seeDetails, deleFn, editFn, examineFn, seeBusinessInfo, uploadFn }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '标段名称',
      prop: 'sectionName',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => seeDetails(row)}
            auth={'ERM_PURCHASE_EVALUATION_INFO'}
            text={row.sectionName}
          ></HyperlinkInfo>
        );
      }
    },
    {
      label: '采购项目名称',
      prop: 'purchaseName'
    },
    {
      label: '流程节点',
      prop: 'businessNode'
    },
    {
      label: '状态',
      prop: 'dicRecruitmentResultStatusName'
    },
    {
      label: '在办业务',
      prop: 'dicRecruitmentResultDoingBusName'
    },
    {
      label: '所属公司',
      prop: 'companyName'
    },
    {
      label: '选聘负责人',
      prop: 'recruitmentPersonName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '中标供应商',
      prop: 'winSuppliers'
    },
    {
      label: '选聘方案简述',
      prop: 'recruitmentSchemeResume',
      width: 400
    },
    {
      label: '成交金额',
      prop: 'tradingAmount',
      render: ({ row }) => {
        return erm_intl(row.tradingAmount);
      }
    },
    {
      label: '开标时间',
      prop: 'openSectionDate'
    },
    {
      label: '挂网时间',
      prop: 'spreadNetTime'
    },
    {
      label: '中标通知书发放时间',
      prop: 'sectionGrantDate'
    },
    {
      label: '实施方式',
      prop: 'dicImplementTypeName'
    },
    {
      label: '选聘方式',
      prop: 'dicRecruitmentTypeName'
    },
    {
      label: '项目名称',
      prop: 'projectName'
    },
    {
      label: '操作',
      prop: 'operationBtn',
      align: 'center',
      fixed: 'right',
      render: ({ row, index }) => {
        let operationBtn = {
          DEL: (
            <el-popconfirm
              title="确定删除？"
              width="220"
              onConfirm={() => {
                deleFn(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_PURCHASE_EVALUATION_DELE'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => editFn(row)}
              auth={'ERM_PURCHASE_EVALUATION_EDIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => examineFn(row)}
              auth={'ERM_PURCHASE_EVALUATION_AUDUT'}
              text={'审核'}
            ></Hyperlink>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={() => seeBusinessInfo(row)}
              auth={'ERM_PURCHASE_EVALUATION_BUZ_INFO'}
              text={'业务详情'}
            ></Hyperlink>
          ),
          UPLOADfILE: (
            <Hyperlink
              row={row}
              index={index}
              func={() => uploadFn(row)}
              auth={'ERM_PURCHASE_EVALUATION_UPLOADfILE'}
              text={'补件'}
            ></Hyperlink>
          )
        };
        let btnList = [];
        btnList = $utils.clone(row.buttonList, true);
        if (!$utils.isNil(row.dicRecruitmentResultDoingBusCode) && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO');
        }
        if (row.dicRecruitmentResultStatusCode == '1') {
          btnList.push('UPLOADfILE');
        }
        return (
          <div style="display:flex;justify-content: space-around;align-items: center;width: 120px">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

const useBtnsConfig = ({ addFn, exportFn, cancelFn, cancelDisabled = true }) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_PURCHASE_EVALUATION_ADD" onClick={addFn} type="primary">
          新建
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_PURCHASE_EVALUATION_CANCEL" onClick={cancelFn} type="primary" disabled={cancelDisabled}>
          作废
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_PURCHASE_EVALUATION_EXPORT" onClick={exportFn} type="primary">
          导出
        </el-button>
      )
    }
  ];
};

export { useColumns, useBtnsConfig };
