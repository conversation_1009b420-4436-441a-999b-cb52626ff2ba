<!-- 资本化成本分摊 -->
<template>
  <div>
    <funi-list-page-v2 ref="pageList" :cardTab="useCardTab" />
    <documentSealDialog v-model="visible" @close="close" ref="documentSealRef" />
    <checkSection v-model="approvalVisible" @close="closeCheckApproval" @confirm="confirmCheckApproval" />
    <CancelConfirmModal ref="cancelConfirmModalRef" @confirm="handleCancelConfirm" @cancel="handleCancelCancel" />
  </div>
</template>
<script lang="jsx" setup>
import { computed, ref, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
import { useColumns, useBtnsConfig } from '@/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentList.jsx';
import {
  queryRecruitmentSchemeListHttp,
  queryValidPurchaseSectionListHttp,
  apiUrl,
  queryDownstreamBusinessHttp,
  batchCancelBusinessHttp,
  checkCancelConditionHttp
} from '@/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentApi.jsx';

import documentSealDialog from '@/apps/erm/purchase/recruitment_plan/component/documentSealDialog.vue';
import checkSection from '@/apps/erm/purchase/recruitment_plan/component/checkSection.vue';
import CancelConfirmModal from '@/apps/erm/purchase/component/CancelConfirmModal.vue';
import { submitBusiness } from '@/apps/erm/config/business.js';
import { ElNotification } from 'element-plus';

let pageList = ref();
let rowData = ref(void 0);
let visible = ref(false);
let approvalVisible = ref(false);
const queryData = ref(void 0);
const router = useRouter();
let isDisable = ref(false);
let documentSealRef = ref();

const loadData = async (params, queryParams) => {
  rowData.value = void 0;
  queryData.value = queryParams;
  pageList.value.activeCurd.resetCurrentRow();
  let resData = await queryRecruitmentSchemeListHttp({ ...params, ...queryParams });
  return resData;
};

//导出
const exportFn = () => {
  expotrFunction({ url: apiUrl.queryRecruitmentSchemeListExport, params: queryData.value, FileName: '选聘方案' });
};
const handleSeal = async () => {
  visible.value = true;
  await nextTick();
  documentSealRef.value.getRowData(rowData.value);
};
const addPlan = () => {
  approvalVisible.value = true;
};

const close = () => {
  visible.value = false;
};

const closeCheckApproval = () => {
  approvalVisible.value = false;
};

const confirmCheckApproval = rowData => {
  if (rowData) {
    approvalVisible.value = false;
    router.push({
      name: 'ermPurchaseRecruitmentAdd',
      query: {
        title: '选聘方案-新建',
        bizName: '新建',
        type: 'add',
        tab: '选聘方案-新建',
        operation: 'addPlan',
        sectionId: rowData.id,
        schemeId: rowData.recruitmentResultId,
        sealType: '4'
      }
    });
  } else {
    ElNotification({
      title: '请选择选择采购立项信息',
      type: 'warning'
    });
  }
};

//删除
const deleFn = async row => {
  if (row) {
    await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
    ElNotification({
      title: '删除成功',
      type: 'success'
    });
  }
  pageList.value.reload({ resetPage: false });
};

const editFn = row => {
  router.push({
    name: 'ermPurchaseRecruitmentAdd',
    query: {
      title: row.sectionName || '选聘方案',
      bizName: '编辑',
      type: 'edit',
      id: row.id,
      tab: ['选聘方案', row.sectionName, '编辑'].join('-'),
      businessId: row.businessId,
      operation: row.dicBusinessTypeCode == 'ERM_RECRUITMENT_SCHEME_SEAL' ? 'seal' : 'addPlan',
      dicUseSealTypeCode: row.dicUseSealTypeCode
    }
  });
};

const examineFn = row => {
  router.push({
    name: 'ermPurchaseRecruitmentInfo',
    query: {
      bizName: '审核',
      dicUseSealTypeCode: row.dicUseSealTypeCode,
      title: row.sectionName || '选聘方案',
      type: 'audit',
      id: row.id,
      tab: ['选聘方案', row.sectionName, '审核'].join('-'),
      businessId: row.businessId,
      operation: row.dicBusinessTypeCode == 'ERM_RECRUITMENT_SCHEME_ADD' ? 'addPlan' : 'seal'
    }
  });
};

const seeBusinessInfo = row => {
  router.push({
    name: 'ermPurchaseRecruitmentInfo',
    query: {
      bizName: '审核',
      title: row.sectionName || '选聘方案',
      dicUseSealTypeCode: row.dicUseSealTypeCode,
      type: 'info',
      id: row.id,
      tab: ['选聘方案', row.sectionName, '详情'].join('-'),
      businessId: row.businessId,
      operation: row.dicBusinessTypeCode == 'ERM_RECRUITMENT_SCHEME_ADD' ? 'addPlan' : 'seal'
    }
  });
};

const seeDetails = row => {
  router.push({
    name: 'ermPurchaseRecruitmentInfo',
    query: {
      bizName: '详情',
      dicUseSealTypeCode: row.dicUseSealTypeCode,
      title: row.sectionName || '选聘方案',
      type: 'info',
      id: row.id,
      tab: ['选聘方案', row.sectionName, '详情'].join('-'),
      businessId: row.businessId,
      operation: row.dicBusinessTypeCode == 'ERM_RECRUITMENT_SCHEME_ADD' ? 'addPlan' : 'seal'
    }
  });
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: useColumns({ deleFn, editFn, examineFn, seeBusinessInfo, seeDetails }),
        btns: useBtnsConfig({ exportFn, handleSeal, addPlan, isEnable: !rowData.value, isDisable: !isDisable.value }),
        lodaData: loadData,
        fixedButtons: true,
        checkOnRowClick: true,
        reloadOnActive: true,
        on: {
          rowClick: selection => {
            rowData.value = selection.row;
            isDisable.value =
              (selection.row.dicUseSealTypeCode == '2' || selection.row.dicRecruitmentStageCode == '1') &&
              selection.row.dicRecruitmentStatusCode == '1';
          }
        }
      }
    }
  ];
});
</script>
<style lang="scss" scoped></style>
