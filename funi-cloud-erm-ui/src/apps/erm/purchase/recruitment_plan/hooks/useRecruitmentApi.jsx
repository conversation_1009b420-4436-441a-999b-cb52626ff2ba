export const apiUrl = {
  queryRecruitmentSchemeList: '/erm/recruitmentSchemeList/queryRecruitmentSchemeList',
  queryRecruitmentSchemeListExport: '/erm/recruitmentSchemeList/queryRecruitmentSchemeListExport',
  recruitmentSchemeNew: '/erm/recruitmentScheme/new',
  recruitmentSchemeInfo: '/erm/recruitmentScheme/info',
  recruitmentSchemeDelete: '/erm/recruitmentScheme/delete',
  queryValidPurchaseSectionList: '/erm/purchaseSectionList/queryValidPurchaseSectionList',
  creatBus: '/erm/recruitmentScheme/creatBus',
  schemeOrResultAdd: '/erm/purchaseSection/schemeOrResultAdd',
  // 新增作废相关接口
  queryDownstreamBusiness: '/erm/recruitment/queryDownstreamBusiness',
  batchCancelBusiness: '/erm/recruitment/batchCancelBusiness',
  checkCancelCondition: '/erm/recruitment/checkCancelCondition'
};

export const queryRecruitmentSchemeListHttp = params => {
  return $http.post(apiUrl.queryRecruitmentSchemeList, params);
};
export const recruitmentSchemeNewHttp = params => {
  return $http.post(apiUrl.recruitmentSchemeNew, params);
};
export const recruitmentSchemeInfoHttp = params => {
  return $http.fetch(apiUrl.recruitmentSchemeInfo, params);
};
export const recruitmentSchemeDeleteHttp = params => {
  return $http.fetch(apiUrl.recruitmentSchemeDelete, params);
};
export const queryValidPurchaseSectionListHttp = params => {
  return $http.post(apiUrl.queryValidPurchaseSectionList, params);
};
export const creatSealBusHttp = params => {
  return $http.post(apiUrl.creatBus, params);
};
export const schemeOrResultAddHttp = params => {
  return $http.post(apiUrl.schemeOrResultAdd, params);
};

// 新增作废相关API函数
export const queryDownstreamBusinessHttp = params => {
  return $http.post(apiUrl.queryDownstreamBusiness, params);
};

export const batchCancelBusinessHttp = params => {
  return $http.post(apiUrl.batchCancelBusiness, params);
};

export const checkCancelConditionHttp = params => {
  return $http.post(apiUrl.checkCancelCondition, params);
};
