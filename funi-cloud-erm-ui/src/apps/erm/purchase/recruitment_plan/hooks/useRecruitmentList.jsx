import HyperlinkInfo from '@/apps/erm/component/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/erm/component/hyperlinkTable/index.vue';
import { moreBtnRender } from '@/apps/erm/config/config.jsx';
import ermHooks from '@/apps/erm/hooks/index.js';
const useColumns = ({ deleFn, editFn, examineFn, seeBusinessInfo, seeDetails }) => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '标段名称',
      prop: 'sectionName',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => seeDetails(row)}
            auth={'ERM_RECRUITMENT_INFO'}
            text={row.sectionName}
          ></HyperlinkInfo>
        );
      }
    },
    {
      label: '采购项目名称',
      prop: 'purchaseName'
    },
    {
      label: '流程节点',
      prop: 'businessNode'
    },
    {
      label: '业务阶段',
      prop: 'dicRecruitmentStageName'
    },
    {
      label: '状态',
      prop: 'dicRecruitmentStatusName'
    },
    {
      label: '在办业务',
      prop: 'dicRecruitmentSchemeDoingBusName'
    },
    {
      label: '所属公司',
      prop: 'companyName'
    },
    {
      label: '选聘负责人',
      prop: 'recruitmentPersonName'
    },
    {
      label: '归属部门',
      prop: 'deptName'
    },
    {
      label: '选聘方案简述',
      prop: 'recruitmentSchemeResume',
      width: '400'
    },
    {
      label: '操作',
      prop: 'operationBtn',
      align: 'center',
      fixed: 'right',
      render: ({ row, index }) => {
        let operationBtn = {
          DEL: (
            <el-popconfirm
              title="确定删除？"
              width="220"
              onConfirm={() => {
                deleFn(row);
              }}
            >
              {{
                reference: () => <Hyperlink auth={'ERM_RECRUITMENT_DEL'} text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => editFn(row)}
              auth={'ERM_RECRUITMENT_EDIT'}
              text={'编辑'}
            ></Hyperlink>
          ),
          AUDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={() => examineFn(row)}
              auth={'ERM_RECRUITMENT_AUDIT'}
              text={'审核'}
            ></Hyperlink>
          ),
          INFO: (
            <Hyperlink
              row={row}
              index={index}
              func={() => seeBusinessInfo(row)}
              auth={'ERM_RECRUITMENT_BUZ_INFO'}
              text={'业务详情'}
            ></Hyperlink>
          )
        };
        let btnList = [];
        btnList = $utils.clone(row.buttonList, true);
        if (!$utils.isNil(row.dicRecruitmentSchemeDoingBusCode) && ermHooks.canAddInfoBtn(btnList)) {
          btnList.push('INFO');
        }
        return (
          <div style="display:flex;justify-content: space-around;align-items: center;width: 120px">
            {moreBtnRender(btnList, operationBtn)}
          </div>
        );
      }
    }
  ];
};

const useBtnsConfig = ({
  handleSeal = () => {},
  addPlan = () => {},
  exportFn = () => {},
  cancelFn = () => {},
  isEnable = false,
  isDisable = false,
  cancelDisabled = true
}) => {
  return [
    {
      component: () => (
        <el-button v-auth="ERM_RECRUITMENT_SEAL" onClick={handleSeal} type="primary" disabled={isDisable}>
          招标文件用印
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_RECRUITMENT_ADD_PLAN" onClick={addPlan} type="primary">
          新建方案
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_RECRUITMENT_CANCEL" onClick={cancelFn} type="primary" disabled={cancelDisabled}>
          作废
        </el-button>
      )
    },
    {
      component: () => (
        <el-button v-auth="ERM_RECRUITMENT_EXPORT" onClick={exportFn} type="primary">
          导出
        </el-button>
      )
    }
  ];
};

export { useColumns, useBtnsConfig };
