export const apiUrl = {
  queryPurchaseManagementList: '/erm/purchaseManagementList/queryPurchaseManagementList',
  queryPurchaseManagementListExport: '/erm/purchaseManagementList/queryPurchaseManagementListExport',
  purchaseManagementNew: '/erm/purchaseManagement/new',
  queryValidProjectList: '/erm/projectManagementList/queryAllValidProjectListByName',
  purchaseManagementInfo: '/erm/purchaseManagement/info',
  queryEmployeeList: '/erm/employeeList/queryEmployeeList',
  creatBus: '/erm/purchaseManagement/creatBus',
  changeRecruitmentPerson: '/erm/purchaseManagement/changeRecruitmentPerson',
  upWindow: '/erm/purchaseManagement/upWindow',
  // 新增作废相关接口
  queryDownstreamBusiness: '/erm/purchase/queryDownstreamBusiness',
  batchCancelBusiness: '/erm/purchase/batchCancelBusiness',
  checkCancelCondition: '/erm/purchase/checkCancelCondition'
};

export const queryPurchaseManagementListHttp = params => {
  return $http.post(apiUrl.queryPurchaseManagementList, params);
};
export const purchaseManagementNewHttp = params => {
  return $http.post(apiUrl.purchaseManagementNew, params);
};
export const purchaseManagementInfoHttp = params => {
  return $http.fetch(apiUrl.purchaseManagementInfo, params);
};
export const creatBusHttp = params => {
  return $http.post(apiUrl.creatBus, params);
};
export const changeRecruitmentPersonHttp = params => {
  return $http.fetch(apiUrl.changeRecruitmentPerson, params);
};
export const upWindowHttp = params => {
  return $http.fetch(apiUrl.upWindow, params);
};

// 新增作废相关API函数
export const queryDownstreamBusinessHttp = params => {
  return $http.post(apiUrl.queryDownstreamBusiness, params);
};

export const batchCancelBusinessHttp = params => {
  return $http.post(apiUrl.batchCancelBusiness, params);
};

export const checkCancelConditionHttp = params => {
  return $http.post(apiUrl.checkCancelCondition, params);
};
