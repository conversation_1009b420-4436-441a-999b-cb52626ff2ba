<!-- 资本化成本分摊 -->
<template>
  <div>
    <funi-list-page-v2 ref="pageList" :cardTab="useCardTab" />
    <editDirectorDialog v-model="visible" @close="close" @refresh="refresh" ref="editDirectorRef" />
    <AuditDialog v-model="showDialog" :callback="dialogCallBack" />
    <NullifyTipModal ref="nullifyTipModalRef" @confirm="confirm" />
    <CancelConfirmModal ref="cancelConfirmModalRef" @confirm="handleCancelConfirm" @cancel="handleCancelCancel" />
  </div>
</template>

<script lang="jsx" setup>
import { computed, ref, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import editDirectorDialog from '@/apps/erm/purchase/project_approval/component/editDirectorDialog.vue';
import { expotrFunction } from '@/apps/erm/config/config.jsx';
import AuditDialog from '@/apps/erm/component/auditDialog/index.vue';
import { useColumns, useBtnsConfig } from '@/apps/erm/purchase/project_approval/hooks/useProjectApproval.jsx';
import {
  apiUrl,
  queryPurchaseManagementListHttp,
  upWindowHttp,
  queryDownstreamBusinessHttp,
  batchCancelBusinessHttp,
  checkCancelConditionHttp
} from '@/apps/erm/purchase/project_approval/hooks/useProjectApprovalApi.jsx';
import NullifyTipModal from '@/apps/erm/purchase/project_approval/component/NullifyTipModal.vue';
import CancelConfirmModal from '@/apps/erm/purchase/component/CancelConfirmModal.vue';

import { submitBusiness } from '@/apps/erm/config/business.js';
import { ElNotification } from 'element-plus';

let pageList = ref();
let rowData = ref(void 0);
let visible = ref(false);
const nullifyTipModalRef = ref();
const cancelConfirmModalRef = ref();
const queryData = ref(void 0);
let editDirectorRef = ref();
const router = useRouter();
let isEnable = ref(false);
let dialogCallBack = ref();
let showDialog = ref(false);

let cancelDisabled = ref(true);

const loadData = async (params, queryParams) => {
  rowData.value = void 0;
  queryData.value = queryParams;
  pageList.value.activeCurd.resetCurrentRow();
  let resData = await queryPurchaseManagementListHttp({ ...params, ...queryParams });
  return resData;
};

//导出
const exportFn = () => {
  expotrFunction({ url: apiUrl.queryPurchaseManagementListExport, params: queryData.value, FileName: '采购立项' });
};

//立项
const approvalFn = () => {
  //判断是否允许立项
  if (!rowData.value.isAllowNextStage) {
    ElNotification({
      message: rowData.value.notAllowNextStageMessage,
      type: 'error'
    });
    return;
  }
  router.push({
    name: 'ermPurchaseApprovalAdd',
    query: {
      title: '采购立项-立项',
      bizName: '新建',
      type: 'add',
      tab: '采购立项-立项',
      operation: 'approval',
      isCancel: '0',
      id: rowData.value.id,
      dicBusinessTypeCode: 'ERM_LX_COMMON'
    }
  });
};

//新建询价函
const addInquiry = () => {
  router.push({
    name: 'ermPurchaseApprovalAdd',
    query: {
      title: '采购立项-新建询价函',
      bizName: '新建',
      type: 'add',
      tab: '采购立项-新建询价函',
      operation: 'inquiry',
      isCancel: '0'
      //   dicBusinessTypeCode: 'ERM_PURCHASE_MANAGEMENT_XJH'
    }
  });
};
//调整负责人
const editDirector = async () => {
  visible.value = true;
  await nextTick();
  editDirectorRef.value.getPerson(rowData.value);
};

const close = () => {
  visible.value = false;
};

//删除
const deleFn = async row => {
  if (row) {
    await submitBusiness(row.dicBusinessTypeCode, row.businessId, 'REMOVE_PROCESS');
    ElNotification({
      title: '删除成功',
      type: 'success'
    });
  }
  pageList.value.reload({ resetPage: false });
};

const editFn = row => {
  router.push({
    name: 'ermPurchaseApprovalAdd',
    query: {
      title: row.purchaseName || '采购立项',
      bizName: '编辑',
      type: 'edit',
      id: row.id,
      operation: ['ERM_PURCHASE_MANAGEMENT_XJH', 'ERM_PURCHASE_MANAGEMENT_XJH_CANCEL'].includes(row.dicBusinessTypeCode)
        ? 'inquiry'
        : 'approval',
      dicBusinessTypeCode: row.dicBusinessTypeCode,
      tab: ['采购立项', row.purchaseName, '编辑'].join('-'),
      businessId: row.businessId
    }
  });
};

const examineFn = row => {
  router.push({
    name: 'ermPurchaseApprovalInfo',
    query: {
      bizName: '审核',
      title: row.purchaseName || '采购立项',
      type: 'audit',
      operation: ['ERM_PURCHASE_MANAGEMENT_XJH', 'ERM_PURCHASE_MANAGEMENT_XJH_CANCEL'].includes(row.dicBusinessTypeCode)
        ? 'inquiry'
        : 'approval',
      id: row.id,
      tab: ['采购立项', row.purchaseName, '审核'].join('-'),
      businessId: row.businessId
    }
  });
};

const confirm = () => {
  console.log(1111);
  router.push({
    name: 'ermPurchaseApprovalAdd',
    query: {
      title: rowData.value.purchaseName || '采购立项',
      bizName: '编辑',
      type: 'add',
      id: rowData.value.id,
      operation: ['ERM_PURCHASE_MANAGEMENT_XJH', 'ERM_PURCHASE_MANAGEMENT_XJH_CANCEL'].includes(
        rowData.value.dicBusinessTypeCode
      )
        ? 'inquiry'
        : 'approval',
      tab: ['采购立项', rowData.value.purchaseName, '作废'].join('-'),
      businessId: rowData.value.businessId,
      isCancel: '1',
      dicBusinessTypeCode:
        rowData.value.dicBusinessTypeCode == 'ERM_PURCHASE_MANAGEMENT_XJH'
          ? 'ERM_PURCHASE_MANAGEMENT_XJH_CANCEL'
          : 'ERM_PURCHASE_MANAGEMENT_LX_CANCEL'
    }
  });
};

// 新的作废逻辑
const cancelFn = async () => {
  try {
    // 1. 检查作废前置条件
    const conditionCheck = await checkCancelConditionHttp({
      businessId: rowData.value.businessId,
      businessType: rowData.value.dicBusinessTypeCode
    });

    if (!conditionCheck.canCancel) {
      ElNotification({
        message: conditionCheck.message || '当前业务不满足作废条件',
        type: 'error'
      });
      return;
    }

    // 2. 查询下游关联业务
    const downstreamResult = await queryDownstreamBusinessHttp({
      businessId: rowData.value.businessId,
      businessType: rowData.value.dicBusinessTypeCode
    });

    // 3. 显示作废确认弹窗
    cancelConfirmModalRef.value.show(rowData.value, downstreamResult.downstreamList || []);
  } catch (error) {
    ElNotification({
      message: '检查作废条件失败',
      type: 'error'
    });
  }
};

// 处理作废确认
const handleCancelConfirm = async (cancelData) => {
  try {
    // 调用批量作废接口
    await batchCancelBusinessHttp({
      mainBusinessId: cancelData.mainBusiness.businessId,
      businessList: [
        {
          businessId: cancelData.mainBusiness.businessId,
          businessType: cancelData.mainBusiness.dicBusinessTypeCode,
          cancelReason: cancelData.cancelReason
        },
        ...cancelData.downstreamList.map(item => ({
          businessId: item.businessId,
          businessType: item.businessType,
          cancelReason: cancelData.cancelReason
        }))
      ]
    });

    ElNotification({
      message: '作废流程已发起',
      type: 'success'
    });

    cancelConfirmModalRef.value.close();
    pageList.value.reload({ resetPage: false });
  } catch (error) {
    ElNotification({
      message: '作废失败',
      type: 'error'
    });
  }
};

// 处理作废取消
const handleCancelCancel = () => {
  // 取消作废操作
};

const seeBusinessInfo = row => {
  router.push({
    name: 'ermPurchaseApprovalInfo',
    query: {
      bizName: '审核',
      title: row.purchaseName || '采购立项',
      operation: ['ERM_PURCHASE_MANAGEMENT_XJH', 'ERM_PURCHASE_MANAGEMENT_XJH_CANCEL'].includes(row.dicBusinessTypeCode)
        ? 'inquiry'
        : 'approval',
      type: 'info',
      id: row.id,
      tab: ['采购立项', row.purchaseName, '详情'].join('-'),
      businessId: row.businessId
    }
  });
};

const seeDetails = row => {
  router.push({
    name: 'ermPurchaseApprovalInfo',
    query: {
      bizName: '详情',
      title: row.purchaseName || '采购立项',
      type: 'info',
      id: row.id,
      operation: ['ERM_PURCHASE_MANAGEMENT_XJH', 'ERM_PURCHASE_MANAGEMENT_XJH_CANCEL'].includes(row.dicBusinessTypeCode)
        ? 'inquiry'
        : 'approval',
      tab: ['采购立项', row.purchaseName, '详情'].join('-'),
      businessId: row.businessId
    }
  });
};

const refresh = () => {
  visible.value = false;
  pageList.value.reload({ resetPage: false });
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        columns: useColumns({ deleFn, editFn, examineFn, seeBusinessInfo, seeDetails }),
        btns: useBtnsConfig({
          approvalFn,
          addInquiry,
          editDirector,
          exportFn,
          cancelFn,
          cancelDisabled,
          isEnable: !isEnable.value,
          isDisAble: !rowData.value
        }),
        lodaData: loadData,
        fixedButtons: true,
        checkOnRowClick: true,
        reloadOnActive: true,
        on: {
          rowClick: selection => {
            rowData.value = selection.row;
            isEnable.value = selection.row?.dicPurchaseStatusCode == '1' && selection.row?.dicPurchaseStageCode == '1';

            if (selection.row.isCancel) cancelDisabled.value = false;
            else cancelDisabled.value = true;
          }
        }
      }
    }
  ];
});
</script>
<style lang="scss" scoped></style>
