<template>
  <FuniDialog
    v-model="visible"
    title="作废确认"
    size="large"
    :onConfirm="onConfirm"
    :onCancel="onCancel"
    @close="onCancel"
    :append-to-body="true"
    :confirmLoading="loading"
  >
    <div class="cancel-confirm-content">
      <div class="warning-message">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <span>检测到以下关联业务，作废当前业务将同时作废所有关联业务：</span>
      </div>
      
      <div class="business-list" v-if="downstreamList.length > 0">
        <el-table :data="downstreamList" border>
          <el-table-column prop="businessName" label="业务名称" />
          <el-table-column prop="businessTypeName" label="业务类型" />
          <el-table-column prop="statusName" label="当前状态" />
          <el-table-column prop="stageName" label="业务阶段" />
        </el-table>
      </div>

      <div class="reason-input">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item label="作废原因" prop="cancelReason" required>
            <el-input
              v-model="form.cancelReason"
              type="textarea"
              :rows="4"
              placeholder="请输入作废原因"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
  </FuniDialog>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Warning } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const visible = ref(false);
const loading = ref(false);
const formRef = ref();
const downstreamList = ref([]);
const mainBusiness = ref({});

const form = reactive({
  cancelReason: ''
});

const rules = {
  cancelReason: [
    { required: true, message: '请输入作废原因', trigger: 'blur' },
    { min: 10, message: '作废原因至少10个字符', trigger: 'blur' }
  ]
};

const emit = defineEmits(['confirm', 'cancel']);

const show = (businessData, downstreamData = []) => {
  mainBusiness.value = businessData;
  downstreamList.value = downstreamData;
  form.cancelReason = '';
  visible.value = true;
};

const onConfirm = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;
    
    const cancelData = {
      mainBusiness: mainBusiness.value,
      downstreamList: downstreamList.value,
      cancelReason: form.cancelReason
    };
    
    emit('confirm', cancelData);
  } catch (error) {
    ElMessage.error('请完善作废信息');
  } finally {
    loading.value = false;
  }
};

const onCancel = () => {
  visible.value = false;
  emit('cancel');
};

const close = () => {
  visible.value = false;
};

defineExpose({
  show,
  close
});
</script>

<style lang="scss" scoped>
.cancel-confirm-content {
  .warning-message {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px;
    background-color: #fef0f0;
    border: 1px solid #fde2e2;
    border-radius: 4px;
    color: #f56c6c;

    .warning-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }

  .business-list {
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
  }

  .reason-input {
    margin-top: 20px;
  }
}
</style>
