{"file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/router/routes/components.js": {"language": "JavaScript", "code": 44, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/router/routes/demo.js": {"language": "JavaScript", "code": 40, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/router/routes/index.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/urgentManager/list.vue": {"language": "vue", "code": 98, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/project_budget/add.vue": {"language": "vue", "code": 46, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/project_budget/info.vue": {"language": "vue", "code": 43, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/project_budget/list.vue": {"language": "vue", "code": 96, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/router/index.js": {"language": "JavaScript", "code": 8, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/finance/list.vue": {"language": "vue", "code": 100, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/supervision/add.vue": {"language": "vue", "code": 56, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/supervision/info.vue": {"language": "vue", "code": 61, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/finance/add.vue": {"language": "vue", "code": 42, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/finance/baseInfo.vue": {"language": "vue", "code": 779, "comment": 0, "blank": 21}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/supervision/list.vue": {"language": "vue", "code": 267, "comment": 0, "blank": 19}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/project_budget/modal/chooseBudget.vue": {"language": "vue", "code": 197, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/project_budget/baseInfo.vue": {"language": "vue", "code": 1238, "comment": 0, "blank": 88}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/project/info.vue": {"language": "vue", "code": 50, "comment": 8, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/project/add.vue": {"language": "vue", "code": 52, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/financeInfo.jsx": {"language": "JavaScript JSX", "code": 1009, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/baseInfoBudget.jsx": {"language": "JavaScript JSX", "code": 907, "comment": 13, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/finance.jsx": {"language": "JavaScript JSX", "code": 87, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/urgentManagerApi.jsx": {"language": "JavaScript JSX", "code": 10, "comment": 2, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/getInfo.js": {"language": "JavaScript", "code": 10, "comment": 12, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/project.jsx": {"language": "JavaScript JSX", "code": 95, "comment": 14, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/supervision/chosePreson.vue": {"language": "vue", "code": 121, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/supervision/commentDialog.vue": {"language": "vue", "code": 202, "comment": 0, "blank": 20}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/supervisionApi.jsx": {"language": "JavaScript JSX", "code": 39, "comment": 6, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/baseInfo.jsx": {"language": "JavaScript JSX", "code": 197, "comment": 16, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/router/routes/common.js": {"language": "JavaScript", "code": 16, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/supervisonInfo.jsx": {"language": "JavaScript JSX", "code": 258, "comment": 40, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/urgentManager.jsx": {"language": "JavaScript JSX", "code": 71, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/financeApi.js": {"language": "JavaScript", "code": 35, "comment": 4, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/api.js": {"language": "JavaScript", "code": 83, "comment": 29, "blank": 21}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/projectBudget.jsx": {"language": "JavaScript JSX", "code": 128, "comment": 15, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/hooks/supervision.jsx": {"language": "JavaScript JSX", "code": 279, "comment": 26, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/project/list.vue": {"language": "vue", "code": 146, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/financeDialog/index.vue": {"language": "vue", "code": 136, "comment": 9, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/project/baseInfo.vue": {"language": "vue", "code": 141, "comment": 8, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/supervision/commentList.vue": {"language": "vue", "code": 494, "comment": 3, "blank": 56}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/supervision/baseInfo.vue": {"language": "vue", "code": 199, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/hooks/moreBtn.jsx": {"language": "JavaScript JSX", "code": 19, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/hooks/changCompare.jsx": {"language": "JavaScript JSX", "code": 7, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/staff_infomation/list.vue": {"language": "vue", "code": 72, "comment": 0, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/staff_infomation/add.vue": {"language": "vue", "code": 47, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/hooks/getUserInfo.js": {"language": "JavaScript", "code": 4, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/hooks/index.js": {"language": "JavaScript", "code": 8, "comment": 17, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/hooks/dateHooks.js": {"language": "JavaScript", "code": 20, "comment": 8, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/hooks/Print.js": {"language": "JavaScript", "code": 130, "comment": 12, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/staff_infomation/info.vue": {"language": "vue", "code": 60, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/hooks/exportFile.js": {"language": "JavaScript", "code": 12, "comment": 8, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/hooks/intl.js": {"language": "JavaScript", "code": 19, "comment": 15, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/component/project_manpower/ymSelect.vue": {"language": "vue", "code": 44, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/component/project_manpower/baseInfo.vue": {"language": "vue", "code": 110, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/hooks/merge.jsx": {"language": "JavaScript JSX", "code": 21, "comment": 4, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/component/staff_infomation/baseInfo.vue": {"language": "vue", "code": 316, "comment": 0, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/supervision/comment.vue": {"language": "vue", "code": 104, "comment": 1, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/component/staff_infomation/formTable.vue": {"language": "vue", "code": 52, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/list.vue": {"language": "vue", "code": 137, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/component/flieTable.vue": {"language": "vue", "code": 59, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/print.vue": {"language": "vue", "code": 154, "comment": 3, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/info.vue": {"language": "vue", "code": 82, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/hooks/useEvaluationList.jsx": {"language": "JavaScript JSX", "code": 176, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/hooks/useEvaluationApi.jsx": {"language": "JavaScript JSX", "code": 23, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/hooks/useEvaluationInfo.jsx": {"language": "JavaScript JSX", "code": 450, "comment": 3, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/project_manpower/list.vue": {"language": "vue", "code": 68, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/project_approval/info.vue": {"language": "vue", "code": 112, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/supervision/select.vue": {"language": "vue", "code": 134, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/print.vue": {"language": "vue", "code": 159, "comment": 3, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/add.vue": {"language": "vue", "code": 75, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/component/checkSection.vue": {"language": "vue", "code": 154, "comment": 0, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/hooks/useStaffList.jsx": {"language": "JavaScript JSX", "code": 94, "comment": 8, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/component/baseInfo.vue": {"language": "vue", "code": 310, "comment": 0, "blank": 24}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/evaluation_result/component/selectSuppler.vue": {"language": "vue", "code": 121, "comment": 0, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/hooks/staffInfoApi.jsx": {"language": "JavaScript JSX", "code": 59, "comment": 11, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/budget/commponent/supervision/urgentTargetList.vue": {"language": "vue", "code": 102, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/hooks/dynamicApi.jsx": {"language": "JavaScript JSX", "code": 36, "comment": 3, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentApi.jsx": {"language": "JavaScript JSX", "code": 27, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/project_manpower/add.vue": {"language": "vue", "code": 40, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/hooks/projectManpowerApi.jsx": {"language": "JavaScript JSX", "code": 21, "comment": 1, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/project_approval/print.vue": {"language": "vue", "code": 129, "comment": 3, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/hooks/useManpowerInfo.jsx": {"language": "JavaScript JSX", "code": 216, "comment": 1, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/component/baseInfo.vue": {"language": "vue", "code": 267, "comment": 0, "blank": 20}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentList.jsx": {"language": "JavaScript JSX", "code": 152, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/list.vue": {"language": "vue", "code": 165, "comment": 1, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/component/documentSealDialog.vue": {"language": "vue", "code": 59, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/hooks/useManpowerList.jsx": {"language": "JavaScript JSX", "code": 91, "comment": 7, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/project_approval/list.vue": {"language": "vue", "code": 180, "comment": 1, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/info.vue": {"language": "vue", "code": 125, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/project_approval/add.vue": {"language": "vue", "code": 84, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/project_approval/hooks/useProjectApproval.jsx": {"language": "JavaScript JSX", "code": 168, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/hooks/useStaffInfo.jsx": {"language": "JavaScript JSX", "code": 625, "comment": 22, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/personnel/project_manpower/info.vue": {"language": "vue", "code": 42, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/component/checkSection.vue": {"language": "vue", "code": 146, "comment": 0, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/project_approval/component/editDirectorDialog.vue": {"language": "vue", "code": 55, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentInfo.jsx": {"language": "JavaScript JSX", "code": 228, "comment": 4, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/epibolyCost/info.vue": {"language": "vue", "code": 109, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/project_approval/hooks/useProjectApprovalInfo.jsx": {"language": "JavaScript JSX", "code": 522, "comment": 10, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/epibolyCost/print.vue": {"language": "vue", "code": 96, "comment": 9, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/bootstrap.jsx": {"language": "JavaScript JSX", "code": 23, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/project_approval/hooks/useProjectApprovalApi.jsx": {"language": "JavaScript JSX", "code": 25, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/component/checkSection.vue": {"language": "vue", "code": 163, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/submit_success/index.vue": {"language": "vue", "code": 46, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/ermSelect/labelIndex.vue": {"language": "vue", "code": 76, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/project_approval/component/baseInfo.vue": {"language": "vue", "code": 350, "comment": 1, "blank": 27}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/infiniteSelect/index.vue": {"language": "vue", "code": 360, "comment": 0, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/complete/index.vue": {"language": "vue", "code": 281, "comment": 9, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/inputMoneyNumber/index.vue": {"language": "vue", "code": 164, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/upload/upload.css": {"language": "CSS", "code": 70, "comment": 0, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/title/index.vue": {"language": "vue", "code": 105, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/profit/index.vue": {"language": "vue", "code": 119, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/refund/index.vue": {"language": "vue", "code": 95, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/table/index.vue": {"language": "vue", "code": 95, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/sign/index.vue": {"language": "vue", "code": 95, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/printTable/index.vue": {"language": "vue", "code": 160, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/textarea/index.vue": {"language": "vue", "code": 27, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/adv_search/orgTree/index.vue": {"language": "vue", "code": 68, "comment": 10, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/upload/upload.vue": {"language": "vue", "code": 108, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/indicator/index.vue": {"language": "vue", "code": 79, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/upload/uploadUse.js": {"language": "JavaScript", "code": 12, "comment": 14, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/echarts/config/index.js": {"language": "JavaScript", "code": 39, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/echarts/index.vue": {"language": "vue", "code": 94, "comment": 0, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/epibolyCost/list.vue": {"language": "vue", "code": 166, "comment": 1, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/index.vue": {"language": "vue", "code": 143, "comment": 9, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/purchase/recruitment_plan/add.vue": {"language": "vue", "code": 85, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/cost_sharing/info.vue": {"language": "vue", "code": 144, "comment": 1, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/collection/index.vue": {"language": "vue", "code": 105, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/income/index.vue": {"language": "vue", "code": 95, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/hyperlinkTable/infoBtn.vue": {"language": "vue", "code": 45, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/hyperlinkTable/index.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/cost_sharing/list.vue": {"language": "vue", "code": 181, "comment": 1, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/cost_sharing/add.vue": {"language": "vue", "code": 109, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/wages_manager/list.vue": {"language": "vue", "code": 101, "comment": 1, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/printForm/index.vue": {"language": "vue", "code": 247, "comment": 2, "blank": 31}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/region/index.vue": {"language": "vue", "code": 260, "comment": 8, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/non_contractual/info.vue": {"language": "vue", "code": 137, "comment": 1, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/printAuditLog/index.vue": {"language": "vue", "code": 62, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/epibolyCost/add.vue": {"language": "vue", "code": 57, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/regularCost/baseInfo.vue": {"language": "vue", "code": 79, "comment": 1, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/wages_manager/useWagesManagerInfo.jsx": {"language": "JavaScript JSX", "code": 445, "comment": 5, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/wages_manager/info.vue": {"language": "vue", "code": 35, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/wages_manager/wagesManagerApi.jsx": {"language": "JavaScript JSX", "code": 28, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/simulatePPT/cost/index.vue": {"language": "vue", "code": 91, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/cost_sharing/baseInfo.vue": {"language": "vue", "code": 426, "comment": 0, "blank": 27}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/cost_sharing/projectShipList.vue": {"language": "vue", "code": 65, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/cost_sharing/sharingDetail.vue": {"language": "vue", "code": 47, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/non_contractual/baseInfo.vue": {"language": "vue", "code": 226, "comment": 0, "blank": 27}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/wages_manager/baseInfo.vue": {"language": "vue", "code": 60, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/wages_manager/sharModal.vue": {"language": "vue", "code": 157, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/non_contractual/formTable.vue": {"language": "vue", "code": 25, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/payment_contract/formTable.vue": {"language": "vue", "code": 24, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/payment_contract/baseInfo.vue": {"language": "vue", "code": 228, "comment": 0, "blank": 30}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/non_contractual/list.vue": {"language": "vue", "code": 128, "comment": 1, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/cost_manager/baseInfo.vue": {"language": "vue", "code": 153, "comment": 0, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/epibolyCost/personSelect.vue": {"language": "vue", "code": 217, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/non_contractual/add.vue": {"language": "vue", "code": 77, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/auditDialog/index.vue": {"language": "vue", "code": 79, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/performance_provision/ceartePerformance.vue": {"language": "vue", "code": 125, "comment": 0, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/epibolyCost/datePicker.vue": {"language": "vue", "code": 43, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/performance_provision/baseInfo.vue": {"language": "vue", "code": 343, "comment": 1, "blank": 25}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/payment_contract/usePaymentContractInfo.jsx": {"language": "JavaScript JSX", "code": 328, "comment": 24, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/payment_contract/usePaymentContract.jsx": {"language": "JavaScript JSX", "code": 181, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/cost_manager/useCostManagerInfo.jsx": {"language": "JavaScript JSX", "code": 136, "comment": 1, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/cost_manager/useCostManager.jsx": {"language": "JavaScript JSX", "code": 93, "comment": 19, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/epibolyCost/useEpibolyCost.jsx": {"language": "JavaScript JSX", "code": 135, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/epibolyCost/useEpiboyCostInfo.jsx": {"language": "JavaScript JSX", "code": 250, "comment": 25, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/epibolyCost/epibolyCostApi.jsx": {"language": "JavaScript JSX", "code": 24, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/epibolyCost/baseInfo.vue": {"language": "vue", "code": 384, "comment": 0, "blank": 21}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/ermSelect/index.vue": {"language": "vue", "code": 120, "comment": 9, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/ermTree/index.vue": {"language": "vue", "code": 127, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/regularCost/regularCostApi.jsx": {"language": "JavaScript JSX", "code": 27, "comment": 3, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/wages_manager/useWagesManager.jsx": {"language": "JavaScript JSX", "code": 101, "comment": 75, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/component/regularCost/handlerDialog.vue": {"language": "vue", "code": 303, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/non_contractual/useNonContractualInfo.jsx": {"language": "JavaScript JSX", "code": 446, "comment": 12, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/non_contractual/nonContractual.jsx": {"language": "JavaScript JSX", "code": 27, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/regularCost/useRegularCostInfo.jsx": {"language": "JavaScript JSX", "code": 145, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/performance_provision/perforProvisionApi.jsx": {"language": "JavaScript JSX", "code": 32, "comment": 4, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/performance_provision/usePerforProvisionInfo.jsx": {"language": "JavaScript JSX", "code": 2751, "comment": 23, "blank": 44}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/performance_provision/usePerforProvision.jsx": {"language": "JavaScript JSX", "code": 150, "comment": 7, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/payment_contract/list.vue": {"language": "vue", "code": 128, "comment": 1, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/payment_contract/add.vue": {"language": "vue", "code": 75, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/regularCost/useRegularCost.jsx": {"language": "JavaScript JSX", "code": 117, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/payment_contract/paymentContractApi.jsx": {"language": "JavaScript JSX", "code": 24, "comment": 5, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/verifica/index.vue": {"language": "vue", "code": 114, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/supplier/hooks/supplierApi.jsx": {"language": "JavaScript JSX", "code": 42, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/cost_manager/list.vue": {"language": "vue", "code": 56, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/non_contractual/useNonContractual.jsx": {"language": "JavaScript JSX", "code": 148, "comment": 6, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/supplier/hooks/useSupplierInfo.jsx": {"language": "JavaScript JSX", "code": 565, "comment": 16, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/department_info/add.vue": {"language": "vue", "code": 46, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/department_info/info.vue": {"language": "vue", "code": 43, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/supplier/hooks/useSupplierList.jsx": {"language": "JavaScript JSX", "code": 88, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/cost_manager/costManagerApi.jsx": {"language": "JavaScript JSX", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/cost_manager/info.vue": {"language": "vue", "code": 40, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/changCompareTable/index.vue": {"language": "vue", "code": 52, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/personChoose/index.vue": {"language": "vue", "code": 181, "comment": 9, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/regularCost/list.vue": {"language": "vue", "code": 104, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/payment_contract/info.vue": {"language": "vue", "code": 130, "comment": 1, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/regularCost/info.vue": {"language": "vue", "code": 143, "comment": 0, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/performance_provision/print.vue": {"language": "vue", "code": 129, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/performance_provision/add.vue": {"language": "vue", "code": 72, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/performance_provision/info.vue": {"language": "vue", "code": 88, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/performance_provision/list.vue": {"language": "vue", "code": 128, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/inputList/radio.vue": {"language": "vue", "code": 43, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/inputList/inputInstall.jsx": {"language": "JavaScript JSX", "code": 4, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/supplier/supplier/info.vue": {"language": "vue", "code": 45, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/department_info/list.vue": {"language": "vue", "code": 74, "comment": 8, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/inputList/dynamicSelect/index.js": {"language": "JavaScript", "code": 29, "comment": 1, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/hooks/company/useCompanyInfo.jsx": {"language": "JavaScript JSX", "code": 116, "comment": 44, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/cost_sharing/useCostSharingInfo.jsx": {"language": "JavaScript JSX", "code": 518, "comment": 33, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/cost_sharing/costharingApi.jsx": {"language": "JavaScript JSX", "code": 35, "comment": 8, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/supplier/supplier/list.vue": {"language": "vue", "code": 89, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/regularCost/print.vue": {"language": "vue", "code": 131, "comment": 9, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/hooks/company/api.js": {"language": "JavaScript", "code": 18, "comment": 4, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/supplier/supplier/add.vue": {"language": "vue", "code": 44, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/inputList/dynamicSelect/index.vue": {"language": "vue", "code": 70, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/hooks/department/useDepartmentList.jsx": {"language": "JavaScript JSX", "code": 53, "comment": 23, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/supplier/component/formTable.vue": {"language": "vue", "code": 55, "comment": 8, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/supplier/component/baseInfo.vue": {"language": "vue", "code": 223, "comment": 0, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/supplier/component/invoiceInfo.vue": {"language": "vue", "code": 200, "comment": 0, "blank": 18}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/component/department_info/baseInfo.vue": {"language": "vue", "code": 139, "comment": 0, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/component/company_info/baseInfo.vue": {"language": "vue", "code": 140, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/config/validate.js": {"language": "JavaScript", "code": 29, "comment": 5, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/config/config.jsx": {"language": "JavaScript JSX", "code": 212, "comment": 61, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/config/style.js": {"language": "JavaScript", "code": 40, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/projectManagerApi.jsx": {"language": "JavaScript JSX", "code": 84, "comment": 16, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/project/print.vue": {"language": "vue", "code": 163, "comment": 1, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/hooks/department/api.js": {"language": "JavaScript", "code": 18, "comment": 4, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/project/list.vue": {"language": "vue", "code": 286, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/project/add.vue": {"language": "vue", "code": 88, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/project/bulletinBoard.vue": {"language": "vue", "code": 165, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/useRelatList.jsx": {"language": "JavaScript JSX", "code": 46, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/useProjectInfo.jsx": {"language": "JavaScript JSX", "code": 965, "comment": 15, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/project/info.vue": {"language": "vue", "code": 319, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/bulletinBorder/payment.jsx": {"language": "JavaScript JSX", "code": 99, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/bulletinBorder/receipt.jsx": {"language": "JavaScript JSX", "code": 107, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/useProjectList.jsx": {"language": "JavaScript JSX", "code": 286, "comment": 13, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/hooks/company/useCompanyList.jsx": {"language": "JavaScript JSX", "code": 58, "comment": 38, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/formTable.vue": {"language": "vue", "code": 40, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/selectBuget.vue": {"language": "vue", "code": 57, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/relateTable.vue": {"language": "vue", "code": 50, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/bulletinBorder/borderBaseInfo.jsx": {"language": "JavaScript JSX", "code": 283, "comment": 8, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/bulletinBorder/cost.jsx": {"language": "JavaScript JSX", "code": 83, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/bulletinBorder/income.jsx": {"language": "JavaScript JSX", "code": 95, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/baseInfo.vue": {"language": "vue", "code": 503, "comment": 1, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/bulletinBorder/collection.jsx": {"language": "JavaScript JSX", "code": 121, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/bulletinBorder/invoice.jsx": {"language": "JavaScript JSX", "code": 87, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/hooks/bulletinBorder/statistic.jsx": {"language": "JavaScript JSX", "code": 117, "comment": 3, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/receipt/list.vue": {"language": "vue", "code": 157, "comment": 8, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/hooks/department/useDepartmentInfo.jsx": {"language": "JavaScript JSX", "code": 140, "comment": 2, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/receipt/info.vue": {"language": "vue", "code": 157, "comment": 8, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/invoice/list.vue": {"language": "vue", "code": 165, "comment": 8, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/invoice/add.vue": {"language": "vue", "code": 107, "comment": 8, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/cost/hooks/cost_sharing/useCostSharing.jsx": {"language": "JavaScript JSX", "code": 165, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/config/table_disabled.css": {"language": "CSS", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/invoice/info.vue": {"language": "vue", "code": 166, "comment": 8, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/other/baseInfo.vue": {"language": "vue", "code": 314, "comment": 8, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/income.vue": {"language": "vue", "code": 166, "comment": 0, "blank": 19}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/invoice/baseInfo.vue": {"language": "vue", "code": 871, "comment": 8, "blank": 54}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/contractRelated/index.vue": {"language": "vue", "code": 289, "comment": 8, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/skip.config.json": {"language": "JSON", "code": 438, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/invoice/modal/chooseCollection.vue": {"language": "vue", "code": 159, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/receipt/baseInfo.vue": {"language": "vue", "code": 505, "comment": 8, "blank": 32}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/invoice/api.js": {"language": "JavaScript", "code": 27, "comment": 15, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/receipt/modal/chooseCollection.vue": {"language": "vue", "code": 159, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/income/modal/chooseCollection.vue": {"language": "vue", "code": 185, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/invoice/baseInfo.jsx": {"language": "JavaScript JSX", "code": 803, "comment": 70, "blank": 24}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/income/baseInfo.vue": {"language": "vue", "code": 551, "comment": 8, "blank": 32}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/receipt/receipt.jsx": {"language": "JavaScript JSX", "code": 197, "comment": 8, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/receipt/api.js": {"language": "JavaScript", "code": 19, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/collection/incomeList.vue": {"language": "vue", "code": 147, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/collection/baseInfo.vue": {"language": "vue", "code": 1082, "comment": 8, "blank": 54}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/collection/invoiceList.vue": {"language": "vue", "code": 143, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/collection/addServeAmount.vue": {"language": "vue", "code": 192, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/receipt/baseInfo.jsx": {"language": "JavaScript JSX", "code": 573, "comment": 35, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/collection/changeEmployee.vue": {"language": "vue", "code": 87, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/receipt/add.vue": {"language": "vue", "code": 78, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/invoice/invoice.jsx": {"language": "JavaScript JSX", "code": 198, "comment": 8, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/config/business.js": {"language": "JavaScript", "code": 46, "comment": 19, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/payment/baseInfo.vue": {"language": "vue", "code": 836, "comment": 8, "blank": 36}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/collection/receiptList.vue": {"language": "vue", "code": 155, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/other/api.js": {"language": "JavaScript", "code": 19, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/income/api.js": {"language": "JavaScript", "code": 31, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/income/income.jsx": {"language": "JavaScript JSX", "code": 176, "comment": 8, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/other/other.jsx": {"language": "JavaScript JSX", "code": 182, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/other/baseInfo.jsx": {"language": "JavaScript JSX", "code": 360, "comment": 2, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/collection/collection.jsx": {"language": "JavaScript JSX", "code": 247, "comment": 14, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/collection/api.js": {"language": "JavaScript", "code": 87, "comment": 11, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/component/collection/changeContRelatedParties.vue": {"language": "vue", "code": 144, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/income/baseInfo.jsx": {"language": "JavaScript JSX", "code": 594, "comment": 25, "blank": 18}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/collection/baseInfo.jsx": {"language": "JavaScript JSX", "code": 1865, "comment": 69, "blank": 22}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/collection.vue": {"language": "vue", "code": 183, "comment": 0, "blank": 16}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/payment/api.js": {"language": "JavaScript", "code": 36, "comment": 10, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/payment/baseInfo.jsx": {"language": "JavaScript JSX", "code": 1154, "comment": 35, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/other/print.vue": {"language": "vue", "code": 164, "comment": 2, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/borderBaseInfo.vue": {"language": "vue", "code": 40, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/hooks/payment/payment.jsx": {"language": "JavaScript JSX", "code": 215, "comment": 8, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/other/add.vue": {"language": "vue", "code": 103, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/other/list.vue": {"language": "vue", "code": 161, "comment": 8, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/cost.vue": {"language": "vue", "code": 150, "comment": 8, "blank": 21}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/other/info.vue": {"language": "vue", "code": 161, "comment": 13, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/invoice.vue": {"language": "vue", "code": 156, "comment": 0, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/income/add.vue": {"language": "vue", "code": 95, "comment": 8, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/income/list.vue": {"language": "vue", "code": 156, "comment": 8, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/statistic.vue": {"language": "vue", "code": 223, "comment": 0, "blank": 27}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/income/info.vue": {"language": "vue", "code": 157, "comment": 8, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/payment/print.vue": {"language": "vue", "code": 208, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/payment/list.vue": {"language": "vue", "code": 206, "comment": 8, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/payment/info.vue": {"language": "vue", "code": 168, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/payment.vue": {"language": "vue", "code": 168, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/receipt.vue": {"language": "vue", "code": 178, "comment": 0, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/purchase.vue": {"language": "vue", "code": 8, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/collection/print.vue": {"language": "vue", "code": 170, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/collection/list.vue": {"language": "vue", "code": 222, "comment": 8, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/collection/add.vue": {"language": "vue", "code": 102, "comment": 8, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/projectManage/component/bulletinBorder/component/lineChart.vue": {"language": "vue", "code": 201, "comment": 0, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/collection/info.vue": {"language": "vue", "code": 228, "comment": 8, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/contract/payment/add.vue": {"language": "vue", "code": 109, "comment": 8, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/dynamicApps.js": {"language": "JavaScript", "code": 1, "comment": 3, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/component/groupTitle/index.vue": {"language": "vue", "code": 91, "comment": 10, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdPro/index.vue": {"language": "vue", "code": 99, "comment": 0, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/hooks/useTheme.js": {"language": "JavaScript", "code": 54, "comment": 25, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSelect/index.vue": {"language": "vue", "code": 137, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniIconSelect/index.css": {"language": "CSS", "code": 136, "comment": 3, "blank": 21}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/theme.scss": {"language": "SCSS", "code": 17, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniIconSelect/search.vue": {"language": "vue", "code": 86, "comment": 10, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniIconSelect/search.css": {"language": "CSS", "code": 166, "comment": 1, "blank": 34}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniTeleport/index.vue": {"language": "vue", "code": 23, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormRender/index.vue": {"language": "vue", "code": 41, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniIconSelect/index.vue": {"language": "vue", "code": 307, "comment": 11, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniIconSelect/assets/icons.json": {"language": "JSON", "code": 1397, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniShareAction/ShareSuccess.vue": {"language": "vue", "code": 62, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniShareAction/index.vue": {"language": "vue", "code": 205, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniShareAction/ShareDialog.vue": {"language": "vue", "code": 92, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniPickerMobile/index.vue": {"language": "vue", "code": 89, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/element/font.scss": {"language": "SCSS", "code": 21, "comment": 2, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniAuthButton/index.vue": {"language": "vue", "code": 3, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/api/api.js": {"language": "JavaScript", "code": 109, "comment": 9, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/bpmn/businessFunc.js": {"language": "JavaScript", "code": 119, "comment": 49, "blank": 16}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/bpmn/index.vue": {"language": "vue", "code": 689, "comment": 10, "blank": 41}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-dataFunc/funi-bpmn-dataFunc.js": {"language": "JavaScript", "code": 62, "comment": 17, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSvg/index.vue": {"language": "vue", "code": 15, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniImageView/index.vue": {"language": "vue", "code": 37, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/api/js-base64.js": {"language": "JavaScript", "code": 206, "comment": 90, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/index.vue": {"language": "vue", "code": 138, "comment": 8, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-dataFunc/index.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-operationData.js": {"language": "JavaScript", "code": 144, "comment": 38, "blank": 38}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-setNativeProperty.js": {"language": "JavaScript", "code": 52, "comment": 14, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/index.js": {"language": "JavaScript", "code": 3, "comment": 9, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-default-listener.js": {"language": "JavaScript", "code": 31, "comment": 13, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/extendInfo/listTable.vue": {"language": "vue", "code": 186, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/extendInfo/index.vue": {"language": "vue", "code": 145, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/VditorEditor.vue": {"language": "vue", "code": 64, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/TinyMceEditor.vue": {"language": "vue", "code": 226, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/bpmn/index.css": {"language": "CSS", "code": 83, "comment": 0, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOC/role.vue": {"language": "vue", "code": 180, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/index.vue": {"language": "vue", "code": 46, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/prevPerson/index.vue": {"language": "vue", "code": 48, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOC/org.vue": {"language": "vue", "code": 240, "comment": 9, "blank": 16}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOC/user.vue": {"language": "vue", "code": 338, "comment": 0, "blank": 19}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/function/index.vue": {"language": "vue", "code": 81, "comment": 10, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOC/index.vue": {"language": "vue", "code": 216, "comment": 9, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/btnGroup/iconSet/index.vue": {"language": "vue", "code": 136, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOC/style/itemBlock.css": {"language": "CSS", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/btnGroup/iconSet/icon.json": {"language": "JSON", "code": 122, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/function/index.css": {"language": "CSS", "code": 80, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/bpmn/previewXML.vue": {"language": "vue", "code": 34, "comment": 11, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/langs/zh-Hans.js": {"language": "JavaScript", "code": 406, "comment": 6, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/content/document/content.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/content/dark/content.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/content/document/content.css": {"language": "CSS", "code": 59, "comment": 13, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/content/writer/content.css": {"language": "CSS", "code": 55, "comment": 13, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/content/dark/content.css": {"language": "CSS", "code": 59, "comment": 13, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/content/writer/content.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/btnGroup/index.vue": {"language": "vue", "code": 321, "comment": 9, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/customizeItem/org.vue": {"language": "vue", "code": 61, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.shadowdom.css": {"language": "CSS", "code": 31, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/btnGroup/index.css": {"language": "CSS", "code": 78, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/content/default/content.css": {"language": "CSS", "code": 54, "comment": 13, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/content/default/content.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/company_info/list.vue": {"language": "vue", "code": 78, "comment": 8, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/company_info/info.vue": {"language": "vue", "code": 43, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/customizeItem/user.vue": {"language": "vue", "code": 176, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apps/erm/system/company_info/add.vue": {"language": "vue", "code": 46, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/content.mobile.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/content.inline.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.mobile.css": {"language": "CSS", "code": 643, "comment": 30, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.mobile.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.css": {"language": "CSS", "code": 697, "comment": 17, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/customizeItem/personAndRole.vue": {"language": "vue", "code": 133, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.mobile.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/content.css": {"language": "CSS", "code": 716, "comment": 16, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/content.mobile.css": {"language": "CSS", "code": 21, "comment": 8, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/content.inline.css": {"language": "CSS", "code": 710, "comment": 16, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/common.jsx": {"language": "JavaScript JSX", "code": 244, "comment": 11, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/customizeItem/role.vue": {"language": "vue", "code": 179, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.css": {"language": "CSS", "code": 3025, "comment": 22, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/customizeItem/index.css": {"language": "CSS", "code": 111, "comment": 1, "blank": 26}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/content.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormV2/index.vue": {"language": "vue", "code": 237, "comment": 11, "blank": 36}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVCurd/index.vue": {"language": "vue", "code": 49, "comment": 8, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniWrap/index.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFileTable/index.vue": {"language": "vue", "code": 463, "comment": 1, "blank": 26}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.shadowdom.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniOperationLog/index.vue": {"language": "vue", "code": 69, "comment": 1, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurd/useDraggable.jsx": {"language": "JavaScript JSX", "code": 27, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurd/useResetSummaryWidth.js": {"language": "JavaScript", "code": 25, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurd/index.vue": {"language": "vue", "code": 483, "comment": 19, "blank": 63}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniInputNumber/index.vue": {"language": "vue", "code": 182, "comment": 0, "blank": 18}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFileTable/upload.vue": {"language": "vue", "code": 131, "comment": 17, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniOrgSelect/config/config.jsx": {"language": "JavaScript JSX", "code": 419, "comment": 50, "blank": 38}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV2/searchFormItem.vue": {"language": "vue", "code": 126, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniProcessBottomBtn/index.vue": {"language": "vue", "code": 114, "comment": 3, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV2/cell-render.jsx": {"language": "JavaScript JSX", "code": 68, "comment": 3, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV2/cell/searchFormRegionItem.vue": {"language": "vue", "code": 32, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniImage/index.vue": {"language": "vue", "code": 33, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV2/cell/searchFormSelectItem.vue": {"language": "vue", "code": 27, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV2/cell/searchFormInputNumberItem.vue": {"language": "vue", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurd/Pagination.vue": {"language": "vue", "code": 62, "comment": 9, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniOrgSelect/index.vue": {"language": "vue", "code": 161, "comment": 9, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV2/cell/searchFormInputNumberRangeItem.vue": {"language": "vue", "code": 46, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniDetail/FuniSteps.vue": {"language": "vue", "code": 125, "comment": 1, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV2/cell/searchFormDateItem.vue": {"language": "vue", "code": 21, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/index.vue": {"language": "vue", "code": 537, "comment": 16, "blank": 70}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniDetail/FuniDetailHead.vue": {"language": "vue", "code": 106, "comment": 6, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniPageRender/index.vue": {"language": "vue", "code": 33, "comment": 9, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV2/index.vue": {"language": "vue", "code": 124, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniDetail/FuniDetailContent.vue": {"language": "vue", "code": 173, "comment": 10, "blank": 16}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV2/cell/searchFormInputItem.vue": {"language": "vue", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniDetail/index.vue": {"language": "vue", "code": 330, "comment": 1, "blank": 28}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRegion/utils.js": {"language": "JavaScript", "code": 46, "comment": 24, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRegion/amap_utils.js": {"language": "JavaScript", "code": 99, "comment": 15, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/hooks/useFullscreen.js": {"language": "JavaScript", "code": 7, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/components/CurdFilterPanel.vue": {"language": "vue", "code": 130, "comment": 0, "blank": 16}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRegion/useRequest.js": {"language": "JavaScript", "code": 12, "comment": 15, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRegion/Tmap.vue": {"language": "vue", "code": 236, "comment": 9, "blank": 23}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRegion/index.vue": {"language": "vue", "code": 320, "comment": 9, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/components/CurdColumnSetting.vue": {"language": "vue", "code": 102, "comment": 4, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/hooks/useResetSummaryWidth.js": {"language": "JavaScript", "code": 25, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/components/CurdSearch.vue": {"language": "vue", "code": 49, "comment": 9, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearch/FuniSearchItem.vue": {"language": "vue", "code": 120, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/components/CurdHeader.vue": {"language": "vue", "code": 97, "comment": 5, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/hooks/useRender.jsx": {"language": "JavaScript JSX", "code": 26, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/hooks/useSelection.js": {"language": "JavaScript", "code": 73, "comment": 49, "blank": 19}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/components/CurdPagination.vue": {"language": "vue", "code": 61, "comment": 9, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/hooks/useDraggable.jsx": {"language": "JavaScript JSX", "code": 27, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdV2/hooks/useColSetting.js": {"language": "JavaScript", "code": 36, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearch/index.vue": {"language": "vue", "code": 166, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSiteHeader/index.vue": {"language": "vue", "code": 44, "comment": 1, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniAuditButtomBtn/index.vue": {"language": "vue", "code": 51, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCimMapDialog/index.vue": {"language": "vue", "code": 113, "comment": 9, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniDialog/index.vue": {"language": "vue", "code": 114, "comment": 0, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniLabel/index.vue": {"language": "vue", "code": 81, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniInputNumberRange/index.vue": {"language": "vue", "code": 56, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSiteFooter/index.vue": {"language": "vue", "code": 52, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniIcon/index.vue": {"language": "vue", "code": 9, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/index.vue": {"language": "vue", "code": 509, "comment": 47, "blank": 69}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/hooks/config.js": {"language": "JavaScript", "code": 51, "comment": 9, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/select/index.vue": {"language": "vue", "code": 131, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/install.js": {"language": "JavaScript", "code": 81, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/style/userList.css": {"language": "CSS", "code": 24, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/user/oneself.vue": {"language": "vue", "code": 72, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.mobile.css": {"language": "CSS", "code": 21, "comment": 8, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/user/role_user.vue": {"language": "vue", "code": 158, "comment": 9, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/selectBpmn/index.vue": {"language": "vue", "code": 57, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBpmn/component/funiModule/index.vue": {"language": "vue", "code": 361, "comment": 9, "blank": 30}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.inline.css": {"language": "CSS", "code": 710, "comment": 16, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.css": {"language": "CSS", "code": 3025, "comment": 22, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/org/org.vue": {"language": "vue", "code": 166, "comment": 9, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/svg-icon/index.vue": {"language": "vue", "code": 47, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/user/org_user.vue": {"language": "vue", "code": 154, "comment": 9, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.shadowdom.css": {"language": "CSS", "code": 31, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/DialogDesigner.vue": {"language": "vue", "code": 450, "comment": 9, "blank": 61}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.mobile.css": {"language": "CSS", "code": 643, "comment": 30, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/org/oneself.vue": {"language": "vue", "code": 72, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/sfc-preview/demo.js": {"language": "JavaScript", "code": 12, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/sfc-preview/index.vue": {"language": "vue", "code": 33, "comment": 8, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/org/hooks/orgHooks.js": {"language": "JavaScript", "code": 56, "comment": 12, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/user/hooks/roleUser.js": {"language": "JavaScript", "code": 40, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/user/hooks/oneself.js": {"language": "JavaScript", "code": 21, "comment": 9, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/style/banner.css": {"language": "CSS", "code": 32, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.inline.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/user/hooks/orgUser.js": {"language": "JavaScript", "code": 74, "comment": 12, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/style/itemBlock.css": {"language": "CSS", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/widget-panel/widgetsConfig.js": {"language": "JavaScript", "code": 885, "comment": 103, "blank": 36}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/widget-panel/templatesConfig.js": {"language": "JavaScript", "code": 52, "comment": 9, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/toolbar-panel/index.vue": {"language": "vue", "code": 1079, "comment": 19, "blank": 128}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-iframe/index.vue": {"language": "vue", "code": 63, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-form/index.vue": {"language": "vue", "code": 133, "comment": 9, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/widget-panel/index.vue": {"language": "vue", "code": 476, "comment": 0, "blank": 61}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/form-setting1.vue": {"language": "vue", "code": 484, "comment": 46, "blank": 37}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/dialog-designer-dialog.vue": {"language": "vue", "code": 55, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/dialog-setting.vue": {"language": "vue", "code": 214, "comment": 6, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/validator-items-setting.vue": {"language": "vue", "code": 80, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-dialog/index.vue": {"language": "vue", "code": 91, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-show-curd/index.vue": {"language": "vue", "code": 544, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.mobile.min.css": {"language": "CSS", "code": 1, "comment": 6, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/index1.vue": {"language": "vue", "code": 463, "comment": 0, "blank": 54}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-dialog-render/index.vue": {"language": "vue", "code": 46, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-select/index.vue": {"language": "vue", "code": 300, "comment": 9, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-funi-log-dialog/service.js": {"language": "JavaScript", "code": 11, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/form-setting.vue": {"language": "vue", "code": 582, "comment": 43, "blank": 37}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/variable-setter.vue": {"language": "vue", "code": 109, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-upload/index.vue": {"language": "vue", "code": 231, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/org/hooks/oneself.js": {"language": "JavaScript", "code": 29, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-funi-log-dialog/sfc-funi-log-inner-dialog.vue": {"language": "vue", "code": 31, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-guid/index.vue": {"language": "vue", "code": 55, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-chart/index.vue": {"language": "vue", "code": 72, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/propertyRegister.js": {"language": "JavaScript", "code": 156, "comment": 71, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/style/checkbox.css": {"language": "CSS", "code": 15, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/option-items-setting.vue": {"language": "vue", "code": 287, "comment": 0, "blank": 30}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-form-item/index.vue": {"language": "vue", "code": 68, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/switch-fn-setting.vue": {"language": "vue", "code": 49, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/dialog/index.vue": {"language": "vue", "code": 160, "comment": 9, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-org/index.vue": {"language": "vue", "code": 138, "comment": 9, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-button/index.vue": {"language": "vue", "code": 42, "comment": 9, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-curd/index.vue": {"language": "vue", "code": 496, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-funi-log/index.vue": {"language": "vue", "code": 19, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor-factory.jsx": {"language": "JavaScript JSX", "code": 736, "comment": 3, "blank": 26}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/index.vue": {"language": "vue", "code": 466, "comment": 0, "blank": 54}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/validator-items-setting/validator-items-dialog.vue": {"language": "vue", "code": 156, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/validator-items-setting/validator-items-setting.vue": {"language": "vue", "code": 95, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-upload-dialog/sfc-upload-inner-dialog.vue": {"language": "vue", "code": 136, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-upload-dialog/service.js": {"language": "JavaScript", "code": 35, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-step/index.vue": {"language": "vue", "code": 504, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-gantt/index.vue": {"language": "vue", "code": 72, "comment": 9, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/submit-code-editor.vue": {"language": "vue", "code": 29, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-user/index.vue": {"language": "vue", "code": 138, "comment": 0, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-checkbox-group/index.vue": {"language": "vue", "code": 138, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-operation-log/index.vue": {"language": "vue", "code": 25, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/filterable-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/placeholder-editor.vue": {"language": "vue", "code": 21, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/funi-sfc-code/index.vue": {"language": "vue", "code": 82, "comment": 8, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/seting-curd-editor.vue": {"language": "vue", "code": 21, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-blank/index.vue": {"language": "vue", "code": 222, "comment": 9, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/withCredentials-editor.vue": {"language": "vue", "code": 19, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/rows-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-radio-group/index.vue": {"language": "vue", "code": 120, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-region/index.vue": {"language": "vue", "code": 48, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/dialog-items-setting.vue": {"language": "vue", "code": 83, "comment": 9, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/maxLength-editor.vue": {"language": "vue", "code": 39, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/min-editor.vue": {"language": "vue", "code": 36, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/labelAlign-editor.vue": {"language": "vue", "code": 40, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/minLength-editor.vue": {"language": "vue", "code": 39, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/required-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/defaultValue-editor.vue": {"language": "vue", "code": 47, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-file-table/index.vue": {"language": "vue", "code": 43, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/clearable-editor.vue": {"language": "vue", "code": 19, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/gantt-column-editor/gantt-column-editor.vue": {"language": "vue", "code": 40, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/customClass-editor.vue": {"language": "vue", "code": 42, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/user-org-scope-editor/user-org-scope-editor.vue": {"language": "vue", "code": 76, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-file-upload/file-upload-fileTypes-editor.vue": {"language": "vue", "code": 51, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/rate-max-editor.vue": {"language": "vue", "code": 23, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/labelHidden-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/allowCreate-editor.vue": {"language": "vue", "code": 20, "comment": 16, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-html-text/htmlContent-editor.vue": {"language": "vue", "code": 29, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/showScore-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/rate-defaultValue-editor.vue": {"language": "vue", "code": 25, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-checkbox/checkbox-defaultValue-editor.vue": {"language": "vue", "code": 15, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/columnWidth-editor.vue": {"language": "vue", "code": 37, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/allowHalf-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/showText-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/lowThreshold-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/readonly-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/name-editor.vue": {"language": "vue", "code": 200, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/highThreshold-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-sub-form/showBlankRow-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-defaultValue-editor.vue": {"language": "vue", "code": 79, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-sub-form/sub-form-labelAlign-editor.vue": {"language": "vue", "code": 33, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-time-range/time-range-format-editor.vue": {"language": "vue", "code": 28, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-type-editor.vue": {"language": "vue", "code": 56, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-format-editor.vue": {"language": "vue", "code": 38, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-sub-form/showRowNumber-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/showFileList-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-grid/colHeight-editor.vue": {"language": "vue", "code": 26, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-valueFormat-editor.vue": {"language": "vue", "code": 38, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/endPlaceholder-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/checkStrictly-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-grid/gutter-editor.vue": {"language": "vue", "code": 82, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/precision-editor.vue": {"language": "vue", "code": 22, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-select/select-defaultValue-editor.vue": {"language": "vue", "code": 15, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-tab/tab-customClass-editor.vue": {"language": "vue", "code": 122, "comment": 4, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/cascader-multiple-editor.vue": {"language": "vue", "code": 20, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/showAllLevels-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/validationHint-editor.vue": {"language": "vue", "code": 20, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/showWordLimit-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/disabled-editor.vue": {"language": "vue", "code": 31, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/event-editor-dialog.vue": {"language": "vue", "code": 114, "comment": 9, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/cascader-defaultValue-editor.vue": {"language": "vue", "code": 15, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-editor-dialog.vue": {"language": "vue", "code": 379, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/autoFullWidth-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-editor.vue": {"language": "vue", "code": 80, "comment": 9, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/validation-editor.vue": {"language": "vue", "code": 54, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/uploadTip-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/fieldVariablesMixins.js": {"language": "JavaScript", "code": 106, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-event-dialog.vue": {"language": "vue", "code": 169, "comment": 9, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-code-dialog.vue": {"language": "vue", "code": 71, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-rich-editor/rich-editor-contentHeight-editor.vue": {"language": "vue", "code": 22, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-color/color-defaultValue-editor.vue": {"language": "vue", "code": 22, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/mode-editor/mode-editor.vue": {"language": "vue", "code": 25, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/max-editor.vue": {"language": "vue", "code": 35, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/editable-editor.vue": {"language": "vue", "code": 19, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/startPlaceholder-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-number/number-defaultValue-editor.vue": {"language": "vue", "code": 31, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-number/controlsPosition-editor.vue": {"language": "vue", "code": 22, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-table-cell/cellHeight-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-time-range/time-range-defaultValue-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/gantt-column-editor/gantt-column-editor-dialog.vue": {"language": "vue", "code": 244, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/multiple-editor.vue": {"language": "vue", "code": 21, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-picture-upload/picture-upload-fileTypes-editor.vue": {"language": "vue", "code": 50, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/label-editor.vue": {"language": "vue", "code": 27, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-table-cell/cellWidth-editor.vue": {"language": "vue", "code": 20, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/labelTooltip-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/icon-editor/icon-editor.vue": {"language": "vue", "code": 134, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/column-code-dialog.vue": {"language": "vue", "code": 97, "comment": 12, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/column-editor.vue": {"language": "vue", "code": 64, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-pull-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-responsive-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/border-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-offset-editor.vue": {"language": "vue", "code": 22, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/appendButtonDisabled-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-span-editor.vue": {"language": "vue", "code": 51, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/form-dialog/form-dialog.vue": {"language": "vue", "code": 54, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/labelWidth-editor.vue": {"language": "vue", "code": 24, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/showPassword-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/code-dialog.vue": {"language": "vue", "code": 69, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-button/button-type-editor.vue": {"language": "vue", "code": 35, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/components/sfc-select-setting-dialog.vue": {"language": "vue", "code": 122, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-button/circle-editor.vue": {"language": "vue", "code": 20, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-valueFormat-editor.vue": {"language": "vue", "code": 32, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/activeText-editor.vue": {"language": "vue", "code": 19, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-defaultValue-editor.vue": {"language": "vue", "code": 31, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-push-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/column-editor-dialog.vue": {"language": "vue", "code": 806, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-type-editor.vue": {"language": "vue", "code": 44, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/type-editor.vue": {"language": "vue", "code": 27, "comment": 4, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-format-editor.vue": {"language": "vue", "code": 32, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-button/round-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/suffixIcon-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/fileMaxSize-editor.vue": {"language": "vue", "code": 22, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/prefixIcon-editor.vue": {"language": "vue", "code": 20, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/index.js": {"language": "JavaScript", "code": 7, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/remote-editor.vue": {"language": "vue", "code": 21, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/activeColor-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/multipleSelect-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/inactiveText-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-button/icon-editor.vue": {"language": "vue", "code": 20, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/switch-defaultValue-editor.vue": {"language": "vue", "code": 23, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/multipleLimit-editor.vue": {"language": "vue", "code": 22, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/switchWidth-editor.vue": {"language": "vue", "code": 20, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/inactiveColor-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/labelIconClass-editor.vue": {"language": "vue", "code": 25, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/limit-editor.vue": {"language": "vue", "code": 22, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/requiredHint-editor.vue": {"language": "vue", "code": 21, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-divider/contentPosition-editor.vue": {"language": "vue", "code": 26, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/urlPrefix-editor.vue": {"language": "vue", "code": 22, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-group-setting-dialog.vue": {"language": "vue", "code": 154, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/size-editor.vue": {"language": "vue", "code": 36, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/uploadURL-editor.vue": {"language": "vue", "code": 25, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/displayStyle-editor.vue": {"language": "vue", "code": 22, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/options-setting.vue": {"language": "vue", "code": 32, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-static-text/textContent-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/propertyMixin.js": {"language": "JavaScript", "code": 40, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/step-editor.vue": {"language": "vue", "code": 21, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/sort-setting-dialog.vue": {"language": "vue", "code": 131, "comment": 9, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/sort-setting.vue": {"language": "vue", "code": 50, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-button/plain-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/buttonStyle-editor.vue": {"language": "vue", "code": 19, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/appendButton-editor.vue": {"language": "vue", "code": 24, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/sql-setting.vue": {"language": "vue", "code": 270, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/modelField-editor/modelField-editor.vue": {"language": "vue", "code": 75, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-setting-dialog.vue": {"language": "vue", "code": 253, "comment": 9, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/typeCode-setting.vue": {"language": "vue", "code": 97, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/datasource-manager.vue": {"language": "vue", "code": 175, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-group.vue": {"language": "vue", "code": 155, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/automaticDropdown-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/model-setting.vue": {"language": "vue", "code": 287, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-group-setting.vue": {"language": "vue", "code": 83, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/labelIconPosition-editor.vue": {"language": "vue", "code": 33, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/api-setting.vue": {"language": "vue", "code": 297, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/optionItems-editor.vue": {"language": "vue", "code": 114, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-slider/showStops-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onMounted-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowChange-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/listener-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-slider/range-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-slider/vertical-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onChange-editor-pro.vue": {"language": "vue", "code": 166, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/datasource-editor.vue": {"language": "vue", "code": 74, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-time/time-defaultValue-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onAppendButtonClick-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-radio/radio-defaultValue-editor.vue": {"language": "vue", "code": 15, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/field-time/time-format-editor.vue": {"language": "vue", "code": 28, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onClick-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onBlur-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onInput-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onCreated-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onUploadSuccess-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowDelete-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/hidden-editor.vue": {"language": "vue", "code": 31, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/buttonIcon-editor.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/code-editor/code-editor.vue": {"language": "vue", "code": 88, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onRemoteQuery-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onValidate-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onUploadError-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onFocus-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/eventMixin.js": {"language": "JavaScript", "code": 11, "comment": 8, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/listener-editor-pro.vue": {"language": "vue", "code": 158, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onBeforeUpload-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onChange-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowInsert-editor.vue": {"language": "vue", "code": 31, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowAdd-editor.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/styles/global.scss": {"language": "SCSS", "code": 67, "comment": 1, "blank": 16}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/lang/en-US_render.js": {"language": "JavaScript", "code": 34, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-setting.vue": {"language": "vue", "code": 78, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/lang/zh-CN_extension.js": {"language": "JavaScript", "code": 82, "comment": 8, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/styles/index.scss": {"language": "SCSS", "code": 13, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onFileRemove.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/lang/zh-CN_render.js": {"language": "JavaScript", "code": 34, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/components/sfc-org-name/index.vue": {"language": "vue", "code": 29, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/lang/zh-CN.js": {"language": "JavaScript", "code": 330, "comment": 0, "blank": 20}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/beautifierLoader.js": {"language": "JavaScript", "code": 72, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/iconfont/iconfont.json": {"language": "JSON", "code": 37, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/iconfont/iconfont.svg": {"language": "XML", "code": 22, "comment": 3, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/iconfont/iconfont.css": {"language": "CSS", "code": 23, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/vue3SfcCompiler.js": {"language": "JavaScript", "code": 324, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/js/designer.js": {"language": "JavaScript", "code": 929, "comment": 60, "blank": 136}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/lang/en-US.js": {"language": "JavaScript", "code": 330, "comment": 0, "blank": 20}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/lowcode.js": {"language": "JavaScript", "code": 1845, "comment": 106, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/lang/en-US_extension.js": {"language": "JavaScript", "code": 75, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/iconfont/iconfont.js": {"language": "JavaScript", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/validators.js": {"language": "JavaScript", "code": 75, "comment": 30, "blank": 23}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/debug-console.js": {"language": "JavaScript", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/sfc-design-dialog/index.vue": {"language": "vue", "code": 78, "comment": 9, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/util.js": {"language": "JavaScript", "code": 789, "comment": 31, "blank": 63}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/vue3js-generator.js": {"language": "JavaScript", "code": 351, "comment": 8, "blank": 34}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/emitter.js": {"language": "JavaScript", "code": 84, "comment": 3, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/sfc-generator.js": {"language": "JavaScript", "code": 584, "comment": 16, "blank": 83}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/config.js": {"language": "JavaScript", "code": 4, "comment": 11, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/event-bus.js": {"language": "JavaScript", "code": 7, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/vue2js-generator.js": {"language": "JavaScript", "code": 827, "comment": 7, "blank": 41}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/js/refMixinDesign.js": {"language": "JavaScript", "code": 24, "comment": 1, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/create-app.js": {"language": "JavaScript", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/el-icons.js": {"language": "JavaScript", "code": 15, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/code-generator.js": {"language": "JavaScript", "code": 74, "comment": 4, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/switch-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/i18n.js": {"language": "JavaScript", "code": 50, "comment": 13, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/edit-curd.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/sub-form.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/histogram.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/directive.js": {"language": "JavaScript", "code": 98, "comment": 29, "blank": 28}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/tab.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/refMixin.js": {"language": "JavaScript", "code": 25, "comment": 8, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/funi-sfc-generator.js": {"language": "JavaScript", "code": 1174, "comment": 27, "blank": 96}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/time-range-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/group-title.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/smart-vue-i18n/utils.js": {"language": "JavaScript", "code": 33, "comment": 4, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/utils/smart-vue-i18n/index.js": {"language": "JavaScript", "code": 24, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/grid.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/org.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/index.vue": {"language": "vue", "code": 635, "comment": 11, "blank": 83}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/guid.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/region.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/slider-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/cascader-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/date-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/data-table.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/slot-component.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/document.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/extension-loader.js": {"language": "JavaScript", "code": 38, "comment": 64, "blank": 20}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/container-item/grid-col-item.vue": {"language": "vue", "code": 134, "comment": 2, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/extension-helper.js": {"language": "JavaScript", "code": 20, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/button.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/show-curd.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/container-item/sub-form-item.vue": {"language": "vue", "code": 381, "comment": 1, "blank": 58}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/container-item/container-item-wrapper.vue": {"language": "vue", "code": 20, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/static-text.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/picture-upload-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/pie.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/radar.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/container-item/table-item.vue": {"language": "vue", "code": 72, "comment": 9, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/section.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/text-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/container-item/containerItemMixin.js": {"language": "JavaScript", "code": 156, "comment": 26, "blank": 34}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/user.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/vue-sfc.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/card.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/container-item/table-cell-item.vue": {"language": "vue", "code": 77, "comment": 2, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/select-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/sfc-iframe.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/container-item/grid-item.vue": {"language": "vue", "code": 58, "comment": 1, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/address-edit.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/clone.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/container-item/tab-item.vue": {"language": "vue", "code": 107, "comment": 2, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/sfc-funi-log.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/number-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/move-down.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/file-table.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/file-upload-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/divider.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/plus.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/insert-column.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/line.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/samples/extension-schema.js": {"language": "JavaScript", "code": 45, "comment": 8, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/color-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/radio-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/scatterplot.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/menu.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/move-up.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/html-text.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-render/container-item/index.js": {"language": "JavaScript", "code": 9, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/drag-move.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/slot-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/samples/alert/alert-widget.vue": {"language": "vue", "code": 76, "comment": 8, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/view.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/insert-row.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/delete.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/undo.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/gantt.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/back.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/draggable-curd.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/rate-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/node-tree.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/funi-histogram-chart.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/set-up.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/zoom-in.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/date-range-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/funi-label.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/info.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/rich-editor-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/textarea-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/check.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/sfc-dialog.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/redo.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/download.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-ext-loader.js": {"language": "JavaScript", "code": 534, "comment": 116, "blank": 32}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/samples/card/card-widget.vue": {"language": "vue", "code": 154, "comment": 0, "blank": 16}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/custom-component.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/hide.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-file-table-widget/index.vue": {"language": "vue", "code": 73, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-group-title-widget/index.vue": {"language": "vue", "code": 70, "comment": 8, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-iframe-widget/index.vue": {"language": "vue", "code": 73, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-region-widget/index.vue": {"language": "vue", "code": 75, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/checkbox-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-user-widget/index.vue": {"language": "vue", "code": 71, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/samples/card/card-item.vue": {"language": "vue", "code": 124, "comment": 2, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-ext-sfc-generator.js": {"language": "JavaScript", "code": 242, "comment": 9, "blank": 18}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-show-curd-widget/index.vue": {"language": "vue", "code": 83, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-org-widget/index.vue": {"language": "vue", "code": 71, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-select-widget/index.vue": {"language": "vue", "code": 90, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-operation-log-widget/index.vue": {"language": "vue", "code": 73, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-user-name-widget/index.vue": {"language": "vue", "code": 72, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-curd-widget/index.vue": {"language": "vue", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-ext-schema.js": {"language": "JavaScript", "code": 518, "comment": 20, "blank": 19}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-histogram-chart-widget/index.vue": {"language": "vue", "code": 97, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-gantt-widget/index.vue": {"language": "vue", "code": 97, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-funi-log-widget/index.vue": {"language": "vue", "code": 74, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-sfc-dialog-widget/index.vue": {"language": "vue", "code": 71, "comment": 8, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-label-widget/index.vue": {"language": "vue", "code": 72, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/custom/search.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-org-name-widget/index.vue": {"language": "vue", "code": 72, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/table.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-edit-curd-widget/index.vue": {"language": "vue", "code": 80, "comment": 9, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/form-template.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/samples/extension-sfc-generator.js": {"language": "JavaScript", "code": 55, "comment": 9, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/el/arrow-down.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/drag.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/time-field.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/github.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/alert.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-draggable-curd-widget/index.vue": {"language": "vue", "code": 97, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-draggable-curd-widget/curd-column-item.vue": {"language": "vue", "code": 61, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-guid-widget/index.vue": {"language": "vue", "code": 71, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/sfc-draggable-curd-widget/sfc-draggable-curd.vue": {"language": "vue", "code": 277, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRUOCLowCode/index.vue": {"language": "vue", "code": 223, "comment": 10, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/code-editor/index.vue": {"language": "vue", "code": 130, "comment": 1, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/index.vue": {"language": "vue", "code": 244, "comment": 0, "blank": 39}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdPro/element-plus-render.ts": {"language": "TypeScript", "code": 585, "comment": 28, "blank": 27}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/funi-sfc-preview/FormPreview.vue": {"language": "vue", "code": 81, "comment": 9, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/funi-sfc-preview/index.vue": {"language": "vue", "code": 73, "comment": 8, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniActions/index.vue": {"language": "vue", "code": 6, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/container-widget/tab-widget.vue": {"language": "vue", "code": 127, "comment": 9, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/extension/funi/funi-sfc-dialog-widget/sfc-dialog-widget.vue": {"language": "vue", "code": 14, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/funi-sfc-preview/SfcRender.vue": {"language": "vue", "code": 44, "comment": 8, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/common/icons/svg/editList.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniWorkRecord/index.vue": {"language": "vue", "code": 139, "comment": 9, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/container-widget/index.js": {"language": "JavaScript", "code": 9, "comment": 8, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/container-widget/table-cell-widget.vue": {"language": "vue", "code": 335, "comment": 0, "blank": 52}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/container-widget/grid-widget.vue": {"language": "vue", "code": 81, "comment": 9, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/container-widget/table-widget.vue": {"language": "vue", "code": 104, "comment": 9, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniHighlightCode/index.vue": {"language": "vue", "code": 321, "comment": 9, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/container-widget/container-wrapper.vue": {"language": "vue", "code": 108, "comment": 9, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/index.js": {"language": "JavaScript", "code": 17, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniHighlightCode/index.css": {"language": "CSS", "code": 95, "comment": 7, "blank": 18}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/switch-widget.vue": {"language": "vue", "code": 95, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/cascader-widget.vue": {"language": "vue", "code": 108, "comment": 0, "blank": 16}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/container-widget/containerMixin.js": {"language": "JavaScript", "code": 79, "comment": 3, "blank": 18}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniHighlightCode/common.js": {"language": "JavaScript", "code": 26, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/input-widget.vue": {"language": "vue", "code": 114, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/index.js": {"language": "JavaScript", "code": 7, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchForm/index.vue": {"language": "vue", "code": 101, "comment": 0, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchRegion/index.vue": {"language": "vue", "code": 79, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/date-range-widget.vue": {"language": "vue", "code": 109, "comment": 0, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/radio-widget.vue": {"language": "vue", "code": 95, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/date-widget.vue": {"language": "vue", "code": 101, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/rate-widget.vue": {"language": "vue", "code": 96, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/file-upload-widget.vue": {"language": "vue", "code": 290, "comment": 1, "blank": 39}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/textarea-widget.vue": {"language": "vue", "code": 98, "comment": 0, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/static-content-wrapper.vue": {"language": "vue", "code": 193, "comment": 0, "blank": 30}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchForm/form.vue": {"language": "vue", "code": 212, "comment": 11, "blank": 32}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/slot-widget.vue": {"language": "vue", "code": 97, "comment": 3, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/time-widget.vue": {"language": "vue", "code": 100, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/number-widget.vue": {"language": "vue", "code": 100, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/fieldMixin.js": {"language": "JavaScript", "code": 480, "comment": 83, "blank": 95}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/html-text-widget.vue": {"language": "vue", "code": 74, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/container-widget/grid-col-widget.vue": {"language": "vue", "code": 313, "comment": 0, "blank": 40}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/divider-widget.vue": {"language": "vue", "code": 76, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/rich-editor-widget.vue": {"language": "vue", "code": 136, "comment": 7, "blank": 19}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniLog/index.vue": {"language": "vue", "code": 132, "comment": 9, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/static-text-widget.vue": {"language": "vue", "code": 73, "comment": 8, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/time-range-widget.vue": {"language": "vue", "code": 110, "comment": 0, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/checkbox-widget.vue": {"language": "vue", "code": 91, "comment": 0, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/picture-upload-widget.vue": {"language": "vue", "code": 291, "comment": 5, "blank": 39}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/curd-column-item.vue": {"language": "vue", "code": 79, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/select-widget.vue": {"language": "vue", "code": 114, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/color-widget.vue": {"language": "vue", "code": 91, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniTreeSelect/index.vue": {"language": "vue", "code": 14, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/slider-widget.vue": {"language": "vue", "code": 96, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/form-item-wrapper.vue": {"language": "vue", "code": 326, "comment": 9, "blank": 55}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/form.vue": {"language": "vue", "code": 265, "comment": 11, "blank": 40}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFormEngine/form-widget/field-widget/button-widget.vue": {"language": "vue", "code": 83, "comment": 8, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniGroupTitle/index.vue": {"language": "vue", "code": 61, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/cell-render.jsx": {"language": "JavaScript JSX", "code": 30, "comment": 3, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniBusAuditDrawer/index.vue": {"language": "vue", "code": 426, "comment": 7, "blank": 26}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniLineChart/index.vue": {"language": "vue", "code": 383, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/index.vue": {"language": "vue", "code": 150, "comment": 0, "blank": 17}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCodemirror/index.vue": {"language": "vue", "code": 62, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/cell/searchFormCascaderItem.vue": {"language": "vue", "code": 61, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/cell/searchFormInputItem.vue": {"language": "vue", "code": 8, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniFilePreview/index.vue": {"language": "vue", "code": 36, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/cell/searchFormSelectItem.vue": {"language": "vue", "code": 80, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/cell/searchFormInputNumberItem.vue": {"language": "vue", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/cell/searchFormDateItem.vue": {"language": "vue", "code": 21, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/cell/searchFormRegionItem.vue": {"language": "vue", "code": 55, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/element/var.scss": {"language": "SCSS", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/cell/searchFormAutocompleteItem.vue": {"language": "vue", "code": 163, "comment": 0, "blank": 25}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/client_switch_close.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniScatterplotChart/index.vue": {"language": "vue", "code": 403, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniGantt/index.vue": {"language": "vue", "code": 152, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniForm/index.vue": {"language": "vue", "code": 343, "comment": 11, "blank": 47}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/resize.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/element/colors.scss": {"language": "SCSS", "code": 115, "comment": 11, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/element/index.scss": {"language": "SCSS", "code": 37, "comment": 1, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/fullscreen_light.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniSearchFormV3/cell/searchFormInputNumberRangeItem.vue": {"language": "vue", "code": 58, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniGantt/demo2.vue": {"language": "vue", "code": 488, "comment": 0, "blank": 49}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/element/shadow.scss": {"language": "SCSS", "code": 19, "comment": 1, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniForm/hooks/updateLabelWidth.js": {"language": "JavaScript", "code": 51, "comment": 11, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/refresh.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniCurdColumn/index.vue": {"language": "vue", "code": 52, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniHistogramChart/index.vue": {"language": "vue", "code": 317, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/setting.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/lock_light.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionGridPanel.vue": {"language": "vue", "code": 103, "comment": 0, "blank": 15}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/function.js": {"language": "JavaScript", "code": 146, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/theme_light.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniRadarChart/index.vue": {"language": "vue", "code": 350, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVariableSetter/utils.js": {"language": "JavaScript", "code": 28, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVariableSetter/BindVariableDialog.scss": {"language": "SCSS", "code": 76, "comment": 0, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/stripe.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniListPage/index.vue": {"language": "vue", "code": 322, "comment": 9, "blank": 34}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionDetailPanel.vue": {"language": "vue", "code": 92, "comment": 9, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/symbols.js": {"language": "JavaScript", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/Logic/index.ts": {"language": "TypeScript", "code": 33, "comment": 8, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/Logic/types.ts": {"language": "TypeScript", "code": 53, "comment": 28, "blank": 21}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionContext/ActionContextItem.vue": {"language": "vue", "code": 47, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionContext/ActionContextStatusItem.vue": {"language": "vue", "code": 41, "comment": 2, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionContext/index.jsx": {"language": "JavaScript JSX", "code": 106, "comment": 0, "blank": 19}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/enums.js": {"language": "JavaScript", "code": 23, "comment": 3, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionContext/index.scss": {"language": "SCSS", "code": 773, "comment": 3, "blank": 232}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/index.vue": {"language": "vue", "code": 97, "comment": 0, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniListPageV2/index.vue": {"language": "vue", "code": 367, "comment": 6, "blank": 37}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/assets/arrow.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVariableSetter/index.vue": {"language": "vue", "code": 108, "comment": 0, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/collapse.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVariableSetter/BindVariableDialog.vue": {"language": "vue", "code": 136, "comment": 3, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingCallMethod.vue": {"language": "vue", "code": 91, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingEmitEvent.vue": {"language": "vue", "code": 77, "comment": 0, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/icon-home.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/client_switch.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingSetState.vue": {"language": "vue", "code": 37, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingShowModal.vue": {"language": "vue", "code": 45, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/assets/icons/fullscreen.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingNavigateTo.vue": {"language": "vue", "code": 80, "comment": 0, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingEmpty.vue": {"language": "vue", "code": 31, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVariableSetter/hooks/useFunctionVariables.js": {"language": "JavaScript", "code": 714, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVariableSetter/CodemirrorEditor/index.vue": {"language": "vue", "code": 74, "comment": 0, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingCloseModal.vue": {"language": "vue", "code": 42, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingValidateForm.vue": {"language": "vue", "code": 42, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniEventEditor/ActionSettingPanel/index.vue": {"language": "vue", "code": 58, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/main.js": {"language": "JavaScript", "code": 41, "comment": 9, "blank": 10}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/index.scss": {"language": "SCSS", "code": 3, "comment": 0, "blank": 0}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVariableSetter/CodemirrorEditor/index.scss": {"language": "SCSS", "code": 24, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/userWorker.js": {"language": "JavaScript", "code": 17, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/mixins/utils.scss": {"language": "SCSS", "code": 32, "comment": 6, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/stores/usePermissionStore.js": {"language": "JavaScript", "code": 66, "comment": 108, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/h5/color.css": {"language": "CSS", "code": 10, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVariableSetter/CodemirrorEditor/extentions/PlaceholderTag.js": {"language": "JavaScript", "code": 22, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/styles/mixins/functions.scss": {"language": "SCSS", "code": 33, "comment": 7, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/components/FuniVariableSetter/CodemirrorEditor/extentions/PlaceholderWidget.js": {"language": "JavaScript", "code": 28, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/useLayoutStore.js": {"language": "JavaScript", "code": 20, "comment": 11, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/stores/useAppStore/bpaas.js": {"language": "JavaScript", "code": 225, "comment": 10, "blank": 23}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/symbols.js": {"language": "JavaScript", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/appBootstrap.js": {"language": "JavaScript", "code": 10, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/stores/useAppStore/index.js": {"language": "JavaScript", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/plugins/userCenterRegister.jsx": {"language": "JavaScript JSX", "code": 31, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/smUtil.js": {"language": "JavaScript", "code": 46, "comment": 4, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/index.vue": {"language": "vue", "code": 103, "comment": 9, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/stores/useAppStore/paas.js": {"language": "JavaScript", "code": 111, "comment": 10, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/plugins/loadMap.js": {"language": "JavaScript", "code": 15, "comment": 1, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/plugins/index.js": {"language": "JavaScript", "code": 11, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/styles/multitab.scss": {"language": "SCSS", "code": 95, "comment": 0, "blank": 18}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/hooks/useWatermark.js": {"language": "JavaScript", "code": 68, "comment": 8, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/directive/index.js": {"language": "JavaScript", "code": 13, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/hooks/useForwardRef.js": {"language": "JavaScript", "code": 9, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/hooks/useAppsLoadFunc.js": {"language": "JavaScript", "code": 13, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/directive/auth.js": {"language": "JavaScript", "code": 13, "comment": 15, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/hooks/useExportTable.js": {"language": "JavaScript", "code": 58, "comment": 14, "blank": 12}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/styles/header.scss": {"language": "SCSS", "code": 82, "comment": 4, "blank": 18}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/styles/aside.scss": {"language": "SCSS", "code": 26, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/styles/root.scss": {"language": "SCSS", "code": 20, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/styles/var.scss": {"language": "SCSS", "code": 89, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/styles/nav_menu.scss": {"language": "SCSS", "code": 99, "comment": 0, "blank": 18}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/styles/logo.scss": {"language": "SCSS", "code": 23, "comment": 1, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/styles/index.scss": {"language": "SCSS", "code": 10, "comment": 10, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/hooks/useMutationObserver.js": {"language": "JavaScript", "code": 13, "comment": 13, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/directive/draggable.js": {"language": "JavaScript", "code": 9, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Main/useKeepAliveCache.js": {"language": "JavaScript", "code": 30, "comment": 0, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/hooks/useMultiTab.js": {"language": "JavaScript", "code": 13, "comment": 16, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Main/index.vue": {"language": "vue", "code": 37, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Breadcrumb.vue": {"language": "vue", "code": 72, "comment": 9, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/index.vue": {"language": "vue", "code": 25, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Aside/index.vue": {"language": "vue", "code": 101, "comment": 0, "blank": 14}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Aside/useRenderMenuItem.jsx": {"language": "JavaScript JSX", "code": 39, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/MultiTab/index.vue": {"language": "vue", "code": 169, "comment": 8, "blank": 21}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/NavMenu/useRenderMenuItem.jsx": {"language": "JavaScript JSX", "code": 39, "comment": 0, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarLeft/ClientSwitch.vue": {"language": "vue", "code": 127, "comment": 0, "blank": 20}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/NavMenu/index.vue": {"language": "vue", "code": 46, "comment": 0, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarLeft/index.vue": {"language": "vue", "code": 40, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarLeft/Logo.vue": {"language": "vue", "code": 22, "comment": 1, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/index.css": {"language": "CSS", "code": 25, "comment": 0, "blank": 5}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/index.vue": {"language": "vue", "code": 81, "comment": 11, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Collapse.vue": {"language": "vue", "code": 24, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/tenant.js": {"language": "JavaScript", "code": 18, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/env.js": {"language": "JavaScript", "code": 13, "comment": 6, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/index.js": {"language": "JavaScript", "code": 41, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/hooks/setTheme.js": {"language": "JavaScript", "code": 221, "comment": 51, "blank": 28}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/functions.js": {"language": "JavaScript", "code": 9, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/httpUtil.js": {"language": "JavaScript", "code": 28, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/file/index.js": {"language": "JavaScript", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/index.vue": {"language": "vue", "code": 20, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/Shortcuts.vue": {"language": "vue", "code": 35, "comment": 12, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/MenuLayout/index.vue": {"language": "vue", "code": 53, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/config/router.config/index.js": {"language": "JavaScript", "code": 11, "comment": 0, "blank": 4}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/config/router.config/paas.js": {"language": "JavaScript", "code": 11, "comment": 0, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/config/router.config/bpaas.js": {"language": "JavaScript", "code": 16, "comment": 1, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/UserCenter/index.vue": {"language": "vue", "code": 275, "comment": 10, "blank": 33}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/ColorGroup/index.vue": {"language": "vue", "code": 159, "comment": 9, "blank": 6}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/file/paas.js": {"language": "JavaScript", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/config/layout.config.js": {"language": "JavaScript", "code": 11, "comment": 12, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/style/comm.css": {"language": "CSS", "code": 53, "comment": 0, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/UserCenter/UserInfoModal.vue": {"language": "vue", "code": 92, "comment": 9, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/HeadColor/index.vue": {"language": "vue", "code": 57, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/FormStyle/index.vue": {"language": "vue", "code": 53, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/UserCenter/UserInfoModalEdit.vue": {"language": "vue", "code": 232, "comment": 8, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/UserCenter/store/logStore.js": {"language": "JavaScript", "code": 56, "comment": 34, "blank": 8}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/utils/file/bpaas.js": {"language": "JavaScript", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/UserCenter/FuniREC/index.vue": {"language": "vue", "code": 193, "comment": 0, "blank": 22}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/Title/index.vue": {"language": "vue", "code": 11, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/config/app.config/bpaas.js": {"language": "JavaScript", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/config/app.config/paas.js": {"language": "JavaScript", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/MenuColor/index.vue": {"language": "vue", "code": 55, "comment": 9, "blank": 3}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/config/app.config/index.js": {"language": "JavaScript", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/UserCenter/apis/log.js": {"language": "JavaScript", "code": 30, "comment": 40, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/UserCenter/apis/log/index.js": {"language": "JavaScript", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/UserCenter/apis/log/paas.js": {"language": "JavaScript", "code": 30, "comment": 40, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/Header/ToolBarRight/UserCenter/apis/log/bpaas.js": {"language": "JavaScript", "code": 30, "comment": 40, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/App.vue": {"language": "vue", "code": 62, "comment": 9, "blank": 7}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/svg/tableDefault.svg.vue": {"language": "vue", "code": 303, "comment": 0, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/svg/menuColor.svg.vue": {"language": "vue", "code": 68, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/svg/menuTop.svg.vue": {"language": "vue", "code": 22, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/svg/formDefault.svg.vue": {"language": "vue", "code": 167, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/svg/formBorder.svg.vue": {"language": "vue", "code": 295, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/svg/menuDefault.svg.vue": {"language": "vue", "code": 29, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/TableStyle/index.vue": {"language": "vue", "code": 53, "comment": 9, "blank": 2}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apis/base/index.js": {"language": "JavaScript", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/svg/tableBorder.svg.vue": {"language": "vue", "code": 320, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/layout/components/theme/components/svg/headColor.svg.vue": {"language": "vue", "code": 62, "comment": 9, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apis/app/paas.js": {"language": "JavaScript", "code": 36, "comment": 30, "blank": 9}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apis/app/index.js": {"language": "JavaScript", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apis/base/bpaas.js": {"language": "JavaScript", "code": 126, "comment": 15, "blank": 13}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apis/base/paas.js": {"language": "JavaScript", "code": 79, "comment": 18, "blank": 11}, "file:///Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src/apis/app/bpaas.js": {"language": "JavaScript", "code": 185, "comment": 57, "blank": 25}}