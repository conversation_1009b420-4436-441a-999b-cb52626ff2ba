# Details

Date : 2024-08-09 16:59:54

Directory /Users/<USER>/project/funi/funi-cloud-erm/funi-cloud-erm-ui/src

Total : 1105 files,  139334 codes, 6791 comments, 9487 blanks, all 155612 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [src/App.vue](/src/App.vue) | vue | 62 | 9 | 7 | 78 |
| [src/apis/app/bpaas.js](/src/apis/app/bpaas.js) | JavaScript | 185 | 57 | 25 | 267 |
| [src/apis/app/index.js](/src/apis/app/index.js) | JavaScript | 5 | 0 | 1 | 6 |
| [src/apis/app/paas.js](/src/apis/app/paas.js) | JavaScript | 36 | 30 | 9 | 75 |
| [src/apis/base/bpaas.js](/src/apis/base/bpaas.js) | JavaScript | 126 | 15 | 13 | 154 |
| [src/apis/base/index.js](/src/apis/base/index.js) | JavaScript | 5 | 0 | 1 | 6 |
| [src/apis/base/paas.js](/src/apis/base/paas.js) | JavaScript | 79 | 18 | 11 | 108 |
| [src/apps/dynamicApps.js](/src/apps/dynamicApps.js) | JavaScript | 1 | 3 | 1 | 5 |
| [src/apps/erm/bootstrap.jsx](/src/apps/erm/bootstrap.jsx) | JavaScript JSX | 23 | 9 | 6 | 38 |
| [src/apps/erm/budget/commponent/finance/baseInfo.vue](/src/apps/erm/budget/commponent/finance/baseInfo.vue) | vue | 779 | 0 | 21 | 800 |
| [src/apps/erm/budget/commponent/financeDialog/index.vue](/src/apps/erm/budget/commponent/financeDialog/index.vue) | vue | 136 | 9 | 10 | 155 |
| [src/apps/erm/budget/commponent/project/baseInfo.vue](/src/apps/erm/budget/commponent/project/baseInfo.vue) | vue | 141 | 8 | 10 | 159 |
| [src/apps/erm/budget/commponent/project_budget/baseInfo.vue](/src/apps/erm/budget/commponent/project_budget/baseInfo.vue) | vue | 1,238 | 0 | 88 | 1,326 |
| [src/apps/erm/budget/commponent/project_budget/modal/chooseBudget.vue](/src/apps/erm/budget/commponent/project_budget/modal/chooseBudget.vue) | vue | 197 | 0 | 9 | 206 |
| [src/apps/erm/budget/commponent/supervision/baseInfo.vue](/src/apps/erm/budget/commponent/supervision/baseInfo.vue) | vue | 199 | 0 | 13 | 212 |
| [src/apps/erm/budget/commponent/supervision/chosePreson.vue](/src/apps/erm/budget/commponent/supervision/chosePreson.vue) | vue | 121 | 0 | 5 | 126 |
| [src/apps/erm/budget/commponent/supervision/comment.vue](/src/apps/erm/budget/commponent/supervision/comment.vue) | vue | 104 | 1 | 15 | 120 |
| [src/apps/erm/budget/commponent/supervision/commentDialog.vue](/src/apps/erm/budget/commponent/supervision/commentDialog.vue) | vue | 202 | 0 | 20 | 222 |
| [src/apps/erm/budget/commponent/supervision/commentList.vue](/src/apps/erm/budget/commponent/supervision/commentList.vue) | vue | 494 | 3 | 56 | 553 |
| [src/apps/erm/budget/commponent/supervision/select.vue](/src/apps/erm/budget/commponent/supervision/select.vue) | vue | 134 | 0 | 9 | 143 |
| [src/apps/erm/budget/commponent/supervision/urgentTargetList.vue](/src/apps/erm/budget/commponent/supervision/urgentTargetList.vue) | vue | 102 | 0 | 9 | 111 |
| [src/apps/erm/budget/finance/add.vue](/src/apps/erm/budget/finance/add.vue) | vue | 42 | 0 | 2 | 44 |
| [src/apps/erm/budget/finance/list.vue](/src/apps/erm/budget/finance/list.vue) | vue | 100 | 9 | 7 | 116 |
| [src/apps/erm/budget/hooks/api.js](/src/apps/erm/budget/hooks/api.js) | JavaScript | 83 | 29 | 21 | 133 |
| [src/apps/erm/budget/hooks/baseInfo.jsx](/src/apps/erm/budget/hooks/baseInfo.jsx) | JavaScript JSX | 197 | 16 | 6 | 219 |
| [src/apps/erm/budget/hooks/baseInfoBudget.jsx](/src/apps/erm/budget/hooks/baseInfoBudget.jsx) | JavaScript JSX | 907 | 13 | 14 | 934 |
| [src/apps/erm/budget/hooks/finance.jsx](/src/apps/erm/budget/hooks/finance.jsx) | JavaScript JSX | 87 | 9 | 5 | 101 |
| [src/apps/erm/budget/hooks/financeApi.js](/src/apps/erm/budget/hooks/financeApi.js) | JavaScript | 35 | 4 | 2 | 41 |
| [src/apps/erm/budget/hooks/financeInfo.jsx](/src/apps/erm/budget/hooks/financeInfo.jsx) | JavaScript JSX | 1,009 | 0 | 6 | 1,015 |
| [src/apps/erm/budget/hooks/getInfo.js](/src/apps/erm/budget/hooks/getInfo.js) | JavaScript | 10 | 12 | 2 | 24 |
| [src/apps/erm/budget/hooks/project.jsx](/src/apps/erm/budget/hooks/project.jsx) | JavaScript JSX | 95 | 14 | 3 | 112 |
| [src/apps/erm/budget/hooks/projectBudget.jsx](/src/apps/erm/budget/hooks/projectBudget.jsx) | JavaScript JSX | 128 | 15 | 3 | 146 |
| [src/apps/erm/budget/hooks/supervision.jsx](/src/apps/erm/budget/hooks/supervision.jsx) | JavaScript JSX | 279 | 26 | 11 | 316 |
| [src/apps/erm/budget/hooks/supervisionApi.jsx](/src/apps/erm/budget/hooks/supervisionApi.jsx) | JavaScript JSX | 39 | 6 | 3 | 48 |
| [src/apps/erm/budget/hooks/supervisonInfo.jsx](/src/apps/erm/budget/hooks/supervisonInfo.jsx) | JavaScript JSX | 258 | 40 | 8 | 306 |
| [src/apps/erm/budget/hooks/urgentManager.jsx](/src/apps/erm/budget/hooks/urgentManager.jsx) | JavaScript JSX | 71 | 9 | 4 | 84 |
| [src/apps/erm/budget/hooks/urgentManagerApi.jsx](/src/apps/erm/budget/hooks/urgentManagerApi.jsx) | JavaScript JSX | 10 | 2 | 2 | 14 |
| [src/apps/erm/budget/project/add.vue](/src/apps/erm/budget/project/add.vue) | vue | 52 | 0 | 3 | 55 |
| [src/apps/erm/budget/project/info.vue](/src/apps/erm/budget/project/info.vue) | vue | 50 | 8 | 3 | 61 |
| [src/apps/erm/budget/project/list.vue](/src/apps/erm/budget/project/list.vue) | vue | 146 | 0 | 10 | 156 |
| [src/apps/erm/budget/project_budget/add.vue](/src/apps/erm/budget/project_budget/add.vue) | vue | 46 | 0 | 4 | 50 |
| [src/apps/erm/budget/project_budget/info.vue](/src/apps/erm/budget/project_budget/info.vue) | vue | 43 | 0 | 4 | 47 |
| [src/apps/erm/budget/project_budget/list.vue](/src/apps/erm/budget/project_budget/list.vue) | vue | 96 | 0 | 8 | 104 |
| [src/apps/erm/budget/supervision/add.vue](/src/apps/erm/budget/supervision/add.vue) | vue | 56 | 0 | 4 | 60 |
| [src/apps/erm/budget/supervision/info.vue](/src/apps/erm/budget/supervision/info.vue) | vue | 61 | 0 | 2 | 63 |
| [src/apps/erm/budget/supervision/list.vue](/src/apps/erm/budget/supervision/list.vue) | vue | 267 | 0 | 19 | 286 |
| [src/apps/erm/budget/urgentManager/list.vue](/src/apps/erm/budget/urgentManager/list.vue) | vue | 98 | 0 | 6 | 104 |
| [src/apps/erm/component/adv_search/orgTree/index.vue](/src/apps/erm/component/adv_search/orgTree/index.vue) | vue | 68 | 10 | 6 | 84 |
| [src/apps/erm/component/auditDialog/index.vue](/src/apps/erm/component/auditDialog/index.vue) | vue | 79 | 9 | 4 | 92 |
| [src/apps/erm/component/changCompareTable/index.vue](/src/apps/erm/component/changCompareTable/index.vue) | vue | 52 | 9 | 2 | 63 |
| [src/apps/erm/component/echarts/config/index.js](/src/apps/erm/component/echarts/config/index.js) | JavaScript | 39 | 0 | 3 | 42 |
| [src/apps/erm/component/echarts/index.vue](/src/apps/erm/component/echarts/index.vue) | vue | 94 | 0 | 11 | 105 |
| [src/apps/erm/component/ermSelect/index.vue](/src/apps/erm/component/ermSelect/index.vue) | vue | 120 | 9 | 8 | 137 |
| [src/apps/erm/component/ermSelect/labelIndex.vue](/src/apps/erm/component/ermSelect/labelIndex.vue) | vue | 76 | 9 | 5 | 90 |
| [src/apps/erm/component/ermTree/index.vue](/src/apps/erm/component/ermTree/index.vue) | vue | 127 | 0 | 13 | 140 |
| [src/apps/erm/component/groupTitle/index.vue](/src/apps/erm/component/groupTitle/index.vue) | vue | 91 | 10 | 4 | 105 |
| [src/apps/erm/component/hyperlinkTable/index.vue](/src/apps/erm/component/hyperlinkTable/index.vue) | vue | 19 | 0 | 3 | 22 |
| [src/apps/erm/component/hyperlinkTable/infoBtn.vue](/src/apps/erm/component/hyperlinkTable/infoBtn.vue) | vue | 45 | 9 | 5 | 59 |
| [src/apps/erm/component/infiniteSelect/index.vue](/src/apps/erm/component/infiniteSelect/index.vue) | vue | 360 | 0 | 17 | 377 |
| [src/apps/erm/component/inputList/dynamicSelect/index.js](/src/apps/erm/component/inputList/dynamicSelect/index.js) | JavaScript | 29 | 1 | 3 | 33 |
| [src/apps/erm/component/inputList/dynamicSelect/index.vue](/src/apps/erm/component/inputList/dynamicSelect/index.vue) | vue | 70 | 0 | 7 | 77 |
| [src/apps/erm/component/inputList/inputInstall.jsx](/src/apps/erm/component/inputList/inputInstall.jsx) | JavaScript JSX | 4 | 0 | 3 | 7 |
| [src/apps/erm/component/inputList/radio.vue](/src/apps/erm/component/inputList/radio.vue) | vue | 43 | 0 | 6 | 49 |
| [src/apps/erm/component/inputMoneyNumber/index.vue](/src/apps/erm/component/inputMoneyNumber/index.vue) | vue | 164 | 0 | 9 | 173 |
| [src/apps/erm/component/personChoose/index.vue](/src/apps/erm/component/personChoose/index.vue) | vue | 181 | 9 | 12 | 202 |
| [src/apps/erm/component/printAuditLog/index.vue](/src/apps/erm/component/printAuditLog/index.vue) | vue | 62 | 9 | 4 | 75 |
| [src/apps/erm/component/printForm/index.vue](/src/apps/erm/component/printForm/index.vue) | vue | 247 | 2 | 31 | 280 |
| [src/apps/erm/component/printTable/index.vue](/src/apps/erm/component/printTable/index.vue) | vue | 160 | 0 | 10 | 170 |
| [src/apps/erm/component/region/index.vue](/src/apps/erm/component/region/index.vue) | vue | 260 | 8 | 14 | 282 |
| [src/apps/erm/component/simulatePPT/collection/index.vue](/src/apps/erm/component/simulatePPT/collection/index.vue) | vue | 105 | 9 | 5 | 119 |
| [src/apps/erm/component/simulatePPT/complete/index.vue](/src/apps/erm/component/simulatePPT/complete/index.vue) | vue | 281 | 9 | 11 | 301 |
| [src/apps/erm/component/simulatePPT/cost/index.vue](/src/apps/erm/component/simulatePPT/cost/index.vue) | vue | 91 | 9 | 4 | 104 |
| [src/apps/erm/component/simulatePPT/income/index.vue](/src/apps/erm/component/simulatePPT/income/index.vue) | vue | 95 | 9 | 4 | 108 |
| [src/apps/erm/component/simulatePPT/index.vue](/src/apps/erm/component/simulatePPT/index.vue) | vue | 143 | 9 | 12 | 164 |
| [src/apps/erm/component/simulatePPT/indicator/index.vue](/src/apps/erm/component/simulatePPT/indicator/index.vue) | vue | 79 | 9 | 5 | 93 |
| [src/apps/erm/component/simulatePPT/profit/index.vue](/src/apps/erm/component/simulatePPT/profit/index.vue) | vue | 119 | 9 | 3 | 131 |
| [src/apps/erm/component/simulatePPT/refund/index.vue](/src/apps/erm/component/simulatePPT/refund/index.vue) | vue | 95 | 9 | 4 | 108 |
| [src/apps/erm/component/simulatePPT/sign/index.vue](/src/apps/erm/component/simulatePPT/sign/index.vue) | vue | 95 | 9 | 4 | 108 |
| [src/apps/erm/component/simulatePPT/table/index.vue](/src/apps/erm/component/simulatePPT/table/index.vue) | vue | 95 | 9 | 3 | 107 |
| [src/apps/erm/component/simulatePPT/textarea/index.vue](/src/apps/erm/component/simulatePPT/textarea/index.vue) | vue | 27 | 9 | 1 | 37 |
| [src/apps/erm/component/simulatePPT/title/index.vue](/src/apps/erm/component/simulatePPT/title/index.vue) | vue | 105 | 9 | 5 | 119 |
| [src/apps/erm/component/submit_success/index.vue](/src/apps/erm/component/submit_success/index.vue) | vue | 46 | 9 | 4 | 59 |
| [src/apps/erm/component/upload/upload.css](/src/apps/erm/component/upload/upload.css) | CSS | 70 | 0 | 14 | 84 |
| [src/apps/erm/component/upload/upload.vue](/src/apps/erm/component/upload/upload.vue) | vue | 108 | 9 | 6 | 123 |
| [src/apps/erm/component/upload/uploadUse.js](/src/apps/erm/component/upload/uploadUse.js) | JavaScript | 12 | 14 | 3 | 29 |
| [src/apps/erm/component/verifica/index.vue](/src/apps/erm/component/verifica/index.vue) | vue | 114 | 8 | 5 | 127 |
| [src/apps/erm/config/business.js](/src/apps/erm/config/business.js) | JavaScript | 46 | 19 | 10 | 75 |
| [src/apps/erm/config/config.jsx](/src/apps/erm/config/config.jsx) | JavaScript JSX | 212 | 61 | 13 | 286 |
| [src/apps/erm/config/style.js](/src/apps/erm/config/style.js) | JavaScript | 40 | 9 | 2 | 51 |
| [src/apps/erm/config/table_disabled.css](/src/apps/erm/config/table_disabled.css) | CSS | 20 | 0 | 5 | 25 |
| [src/apps/erm/config/validate.js](/src/apps/erm/config/validate.js) | JavaScript | 29 | 5 | 3 | 37 |
| [src/apps/erm/contract/collection/add.vue](/src/apps/erm/contract/collection/add.vue) | vue | 102 | 8 | 6 | 116 |
| [src/apps/erm/contract/collection/info.vue](/src/apps/erm/contract/collection/info.vue) | vue | 228 | 8 | 6 | 242 |
| [src/apps/erm/contract/collection/list.vue](/src/apps/erm/contract/collection/list.vue) | vue | 222 | 8 | 9 | 239 |
| [src/apps/erm/contract/collection/print.vue](/src/apps/erm/contract/collection/print.vue) | vue | 170 | 9 | 7 | 186 |
| [src/apps/erm/contract/component/collection/addServeAmount.vue](/src/apps/erm/contract/component/collection/addServeAmount.vue) | vue | 192 | 0 | 5 | 197 |
| [src/apps/erm/contract/component/collection/baseInfo.vue](/src/apps/erm/contract/component/collection/baseInfo.vue) | vue | 1,082 | 8 | 54 | 1,144 |
| [src/apps/erm/contract/component/collection/changeContRelatedParties.vue](/src/apps/erm/contract/component/collection/changeContRelatedParties.vue) | vue | 144 | 0 | 6 | 150 |
| [src/apps/erm/contract/component/collection/changeEmployee.vue](/src/apps/erm/contract/component/collection/changeEmployee.vue) | vue | 87 | 0 | 6 | 93 |
| [src/apps/erm/contract/component/collection/incomeList.vue](/src/apps/erm/contract/component/collection/incomeList.vue) | vue | 147 | 0 | 6 | 153 |
| [src/apps/erm/contract/component/collection/invoiceList.vue](/src/apps/erm/contract/component/collection/invoiceList.vue) | vue | 143 | 0 | 5 | 148 |
| [src/apps/erm/contract/component/collection/receiptList.vue](/src/apps/erm/contract/component/collection/receiptList.vue) | vue | 155 | 0 | 5 | 160 |
| [src/apps/erm/contract/component/contractRelated/index.vue](/src/apps/erm/contract/component/contractRelated/index.vue) | vue | 289 | 8 | 11 | 308 |
| [src/apps/erm/contract/component/income/baseInfo.vue](/src/apps/erm/contract/component/income/baseInfo.vue) | vue | 551 | 8 | 32 | 591 |
| [src/apps/erm/contract/component/income/modal/chooseCollection.vue](/src/apps/erm/contract/component/income/modal/chooseCollection.vue) | vue | 185 | 0 | 7 | 192 |
| [src/apps/erm/contract/component/invoice/baseInfo.vue](/src/apps/erm/contract/component/invoice/baseInfo.vue) | vue | 871 | 8 | 54 | 933 |
| [src/apps/erm/contract/component/invoice/modal/chooseCollection.vue](/src/apps/erm/contract/component/invoice/modal/chooseCollection.vue) | vue | 159 | 0 | 7 | 166 |
| [src/apps/erm/contract/component/other/baseInfo.vue](/src/apps/erm/contract/component/other/baseInfo.vue) | vue | 314 | 8 | 17 | 339 |
| [src/apps/erm/contract/component/payment/baseInfo.vue](/src/apps/erm/contract/component/payment/baseInfo.vue) | vue | 836 | 8 | 36 | 880 |
| [src/apps/erm/contract/component/receipt/baseInfo.vue](/src/apps/erm/contract/component/receipt/baseInfo.vue) | vue | 505 | 8 | 32 | 545 |
| [src/apps/erm/contract/component/receipt/modal/chooseCollection.vue](/src/apps/erm/contract/component/receipt/modal/chooseCollection.vue) | vue | 159 | 0 | 7 | 166 |
| [src/apps/erm/contract/hooks/collection/api.js](/src/apps/erm/contract/hooks/collection/api.js) | JavaScript | 87 | 11 | 9 | 107 |
| [src/apps/erm/contract/hooks/collection/baseInfo.jsx](/src/apps/erm/contract/hooks/collection/baseInfo.jsx) | JavaScript JSX | 1,865 | 69 | 22 | 1,956 |
| [src/apps/erm/contract/hooks/collection/collection.jsx](/src/apps/erm/contract/hooks/collection/collection.jsx) | JavaScript JSX | 247 | 14 | 7 | 268 |
| [src/apps/erm/contract/hooks/income/api.js](/src/apps/erm/contract/hooks/income/api.js) | JavaScript | 31 | 9 | 3 | 43 |
| [src/apps/erm/contract/hooks/income/baseInfo.jsx](/src/apps/erm/contract/hooks/income/baseInfo.jsx) | JavaScript JSX | 594 | 25 | 18 | 637 |
| [src/apps/erm/contract/hooks/income/income.jsx](/src/apps/erm/contract/hooks/income/income.jsx) | JavaScript JSX | 176 | 8 | 8 | 192 |
| [src/apps/erm/contract/hooks/invoice/api.js](/src/apps/erm/contract/hooks/invoice/api.js) | JavaScript | 27 | 15 | 7 | 49 |
| [src/apps/erm/contract/hooks/invoice/baseInfo.jsx](/src/apps/erm/contract/hooks/invoice/baseInfo.jsx) | JavaScript JSX | 803 | 70 | 24 | 897 |
| [src/apps/erm/contract/hooks/invoice/invoice.jsx](/src/apps/erm/contract/hooks/invoice/invoice.jsx) | JavaScript JSX | 198 | 8 | 11 | 217 |
| [src/apps/erm/contract/hooks/other/api.js](/src/apps/erm/contract/hooks/other/api.js) | JavaScript | 19 | 9 | 2 | 30 |
| [src/apps/erm/contract/hooks/other/baseInfo.jsx](/src/apps/erm/contract/hooks/other/baseInfo.jsx) | JavaScript JSX | 360 | 2 | 5 | 367 |
| [src/apps/erm/contract/hooks/other/other.jsx](/src/apps/erm/contract/hooks/other/other.jsx) | JavaScript JSX | 182 | 8 | 5 | 195 |
| [src/apps/erm/contract/hooks/payment/api.js](/src/apps/erm/contract/hooks/payment/api.js) | JavaScript | 36 | 10 | 7 | 53 |
| [src/apps/erm/contract/hooks/payment/baseInfo.jsx](/src/apps/erm/contract/hooks/payment/baseInfo.jsx) | JavaScript JSX | 1,154 | 35 | 8 | 1,197 |
| [src/apps/erm/contract/hooks/payment/payment.jsx](/src/apps/erm/contract/hooks/payment/payment.jsx) | JavaScript JSX | 215 | 8 | 6 | 229 |
| [src/apps/erm/contract/hooks/receipt/api.js](/src/apps/erm/contract/hooks/receipt/api.js) | JavaScript | 19 | 9 | 2 | 30 |
| [src/apps/erm/contract/hooks/receipt/baseInfo.jsx](/src/apps/erm/contract/hooks/receipt/baseInfo.jsx) | JavaScript JSX | 573 | 35 | 14 | 622 |
| [src/apps/erm/contract/hooks/receipt/receipt.jsx](/src/apps/erm/contract/hooks/receipt/receipt.jsx) | JavaScript JSX | 197 | 8 | 8 | 213 |
| [src/apps/erm/contract/income/add.vue](/src/apps/erm/contract/income/add.vue) | vue | 95 | 8 | 6 | 109 |
| [src/apps/erm/contract/income/info.vue](/src/apps/erm/contract/income/info.vue) | vue | 157 | 8 | 6 | 171 |
| [src/apps/erm/contract/income/list.vue](/src/apps/erm/contract/income/list.vue) | vue | 156 | 8 | 10 | 174 |
| [src/apps/erm/contract/invoice/add.vue](/src/apps/erm/contract/invoice/add.vue) | vue | 107 | 8 | 7 | 122 |
| [src/apps/erm/contract/invoice/info.vue](/src/apps/erm/contract/invoice/info.vue) | vue | 166 | 8 | 8 | 182 |
| [src/apps/erm/contract/invoice/list.vue](/src/apps/erm/contract/invoice/list.vue) | vue | 165 | 8 | 12 | 185 |
| [src/apps/erm/contract/other/add.vue](/src/apps/erm/contract/other/add.vue) | vue | 103 | 8 | 4 | 115 |
| [src/apps/erm/contract/other/info.vue](/src/apps/erm/contract/other/info.vue) | vue | 161 | 13 | 7 | 181 |
| [src/apps/erm/contract/other/list.vue](/src/apps/erm/contract/other/list.vue) | vue | 161 | 8 | 12 | 181 |
| [src/apps/erm/contract/other/print.vue](/src/apps/erm/contract/other/print.vue) | vue | 164 | 2 | 9 | 175 |
| [src/apps/erm/contract/payment/add.vue](/src/apps/erm/contract/payment/add.vue) | vue | 109 | 8 | 7 | 124 |
| [src/apps/erm/contract/payment/info.vue](/src/apps/erm/contract/payment/info.vue) | vue | 168 | 8 | 5 | 181 |
| [src/apps/erm/contract/payment/list.vue](/src/apps/erm/contract/payment/list.vue) | vue | 206 | 8 | 12 | 226 |
| [src/apps/erm/contract/payment/print.vue](/src/apps/erm/contract/payment/print.vue) | vue | 208 | 0 | 10 | 218 |
| [src/apps/erm/contract/receipt/add.vue](/src/apps/erm/contract/receipt/add.vue) | vue | 78 | 8 | 4 | 90 |
| [src/apps/erm/contract/receipt/info.vue](/src/apps/erm/contract/receipt/info.vue) | vue | 157 | 8 | 6 | 171 |
| [src/apps/erm/contract/receipt/list.vue](/src/apps/erm/contract/receipt/list.vue) | vue | 157 | 8 | 11 | 176 |
| [src/apps/erm/cost/component/cost_manager/baseInfo.vue](/src/apps/erm/cost/component/cost_manager/baseInfo.vue) | vue | 153 | 0 | 11 | 164 |
| [src/apps/erm/cost/component/cost_sharing/baseInfo.vue](/src/apps/erm/cost/component/cost_sharing/baseInfo.vue) | vue | 426 | 0 | 27 | 453 |
| [src/apps/erm/cost/component/cost_sharing/projectShipList.vue](/src/apps/erm/cost/component/cost_sharing/projectShipList.vue) | vue | 65 | 0 | 5 | 70 |
| [src/apps/erm/cost/component/cost_sharing/sharingDetail.vue](/src/apps/erm/cost/component/cost_sharing/sharingDetail.vue) | vue | 47 | 0 | 6 | 53 |
| [src/apps/erm/cost/component/epibolyCost/baseInfo.vue](/src/apps/erm/cost/component/epibolyCost/baseInfo.vue) | vue | 384 | 0 | 21 | 405 |
| [src/apps/erm/cost/component/epibolyCost/datePicker.vue](/src/apps/erm/cost/component/epibolyCost/datePicker.vue) | vue | 43 | 0 | 5 | 48 |
| [src/apps/erm/cost/component/epibolyCost/personSelect.vue](/src/apps/erm/cost/component/epibolyCost/personSelect.vue) | vue | 217 | 0 | 13 | 230 |
| [src/apps/erm/cost/component/non_contractual/baseInfo.vue](/src/apps/erm/cost/component/non_contractual/baseInfo.vue) | vue | 226 | 0 | 27 | 253 |
| [src/apps/erm/cost/component/non_contractual/formTable.vue](/src/apps/erm/cost/component/non_contractual/formTable.vue) | vue | 25 | 0 | 4 | 29 |
| [src/apps/erm/cost/component/payment_contract/baseInfo.vue](/src/apps/erm/cost/component/payment_contract/baseInfo.vue) | vue | 228 | 0 | 30 | 258 |
| [src/apps/erm/cost/component/payment_contract/formTable.vue](/src/apps/erm/cost/component/payment_contract/formTable.vue) | vue | 24 | 0 | 4 | 28 |
| [src/apps/erm/cost/component/performance_provision/baseInfo.vue](/src/apps/erm/cost/component/performance_provision/baseInfo.vue) | vue | 343 | 1 | 25 | 369 |
| [src/apps/erm/cost/component/performance_provision/ceartePerformance.vue](/src/apps/erm/cost/component/performance_provision/ceartePerformance.vue) | vue | 125 | 0 | 12 | 137 |
| [src/apps/erm/cost/component/regularCost/baseInfo.vue](/src/apps/erm/cost/component/regularCost/baseInfo.vue) | vue | 79 | 1 | 6 | 86 |
| [src/apps/erm/cost/component/regularCost/handlerDialog.vue](/src/apps/erm/cost/component/regularCost/handlerDialog.vue) | vue | 303 | 0 | 10 | 313 |
| [src/apps/erm/cost/component/wages_manager/baseInfo.vue](/src/apps/erm/cost/component/wages_manager/baseInfo.vue) | vue | 60 | 0 | 9 | 69 |
| [src/apps/erm/cost/component/wages_manager/sharModal.vue](/src/apps/erm/cost/component/wages_manager/sharModal.vue) | vue | 157 | 0 | 8 | 165 |
| [src/apps/erm/cost/cost_manager/info.vue](/src/apps/erm/cost/cost_manager/info.vue) | vue | 40 | 0 | 5 | 45 |
| [src/apps/erm/cost/cost_manager/list.vue](/src/apps/erm/cost/cost_manager/list.vue) | vue | 56 | 0 | 7 | 63 |
| [src/apps/erm/cost/cost_sharing/add.vue](/src/apps/erm/cost/cost_sharing/add.vue) | vue | 109 | 0 | 7 | 116 |
| [src/apps/erm/cost/cost_sharing/info.vue](/src/apps/erm/cost/cost_sharing/info.vue) | vue | 144 | 1 | 7 | 152 |
| [src/apps/erm/cost/cost_sharing/list.vue](/src/apps/erm/cost/cost_sharing/list.vue) | vue | 181 | 1 | 11 | 193 |
| [src/apps/erm/cost/epibolyCost/add.vue](/src/apps/erm/cost/epibolyCost/add.vue) | vue | 57 | 0 | 4 | 61 |
| [src/apps/erm/cost/epibolyCost/info.vue](/src/apps/erm/cost/epibolyCost/info.vue) | vue | 109 | 0 | 8 | 117 |
| [src/apps/erm/cost/epibolyCost/list.vue](/src/apps/erm/cost/epibolyCost/list.vue) | vue | 166 | 1 | 6 | 173 |
| [src/apps/erm/cost/epibolyCost/print.vue](/src/apps/erm/cost/epibolyCost/print.vue) | vue | 96 | 9 | 11 | 116 |
| [src/apps/erm/cost/hooks/cost_manager/costManagerApi.jsx](/src/apps/erm/cost/hooks/cost_manager/costManagerApi.jsx) | JavaScript JSX | 15 | 7 | 2 | 24 |
| [src/apps/erm/cost/hooks/cost_manager/useCostManager.jsx](/src/apps/erm/cost/hooks/cost_manager/useCostManager.jsx) | JavaScript JSX | 93 | 19 | 4 | 116 |
| [src/apps/erm/cost/hooks/cost_manager/useCostManagerInfo.jsx](/src/apps/erm/cost/hooks/cost_manager/useCostManagerInfo.jsx) | JavaScript JSX | 136 | 1 | 4 | 141 |
| [src/apps/erm/cost/hooks/cost_sharing/costharingApi.jsx](/src/apps/erm/cost/hooks/cost_sharing/costharingApi.jsx) | JavaScript JSX | 35 | 8 | 1 | 44 |
| [src/apps/erm/cost/hooks/cost_sharing/useCostSharing.jsx](/src/apps/erm/cost/hooks/cost_sharing/useCostSharing.jsx) | JavaScript JSX | 165 | 9 | 3 | 177 |
| [src/apps/erm/cost/hooks/cost_sharing/useCostSharingInfo.jsx](/src/apps/erm/cost/hooks/cost_sharing/useCostSharingInfo.jsx) | JavaScript JSX | 518 | 33 | 14 | 565 |
| [src/apps/erm/cost/hooks/epibolyCost/epibolyCostApi.jsx](/src/apps/erm/cost/hooks/epibolyCost/epibolyCostApi.jsx) | JavaScript JSX | 24 | 0 | 2 | 26 |
| [src/apps/erm/cost/hooks/epibolyCost/useEpibolyCost.jsx](/src/apps/erm/cost/hooks/epibolyCost/useEpibolyCost.jsx) | JavaScript JSX | 135 | 0 | 4 | 139 |
| [src/apps/erm/cost/hooks/epibolyCost/useEpiboyCostInfo.jsx](/src/apps/erm/cost/hooks/epibolyCost/useEpiboyCostInfo.jsx) | JavaScript JSX | 250 | 25 | 4 | 279 |
| [src/apps/erm/cost/hooks/non_contractual/nonContractual.jsx](/src/apps/erm/cost/hooks/non_contractual/nonContractual.jsx) | JavaScript JSX | 27 | 9 | 2 | 38 |
| [src/apps/erm/cost/hooks/non_contractual/useNonContractual.jsx](/src/apps/erm/cost/hooks/non_contractual/useNonContractual.jsx) | JavaScript JSX | 148 | 6 | 4 | 158 |
| [src/apps/erm/cost/hooks/non_contractual/useNonContractualInfo.jsx](/src/apps/erm/cost/hooks/non_contractual/useNonContractualInfo.jsx) | JavaScript JSX | 446 | 12 | 12 | 470 |
| [src/apps/erm/cost/hooks/payment_contract/paymentContractApi.jsx](/src/apps/erm/cost/hooks/payment_contract/paymentContractApi.jsx) | JavaScript JSX | 24 | 5 | 1 | 30 |
| [src/apps/erm/cost/hooks/payment_contract/usePaymentContract.jsx](/src/apps/erm/cost/hooks/payment_contract/usePaymentContract.jsx) | JavaScript JSX | 181 | 9 | 3 | 193 |
| [src/apps/erm/cost/hooks/payment_contract/usePaymentContractInfo.jsx](/src/apps/erm/cost/hooks/payment_contract/usePaymentContractInfo.jsx) | JavaScript JSX | 328 | 24 | 6 | 358 |
| [src/apps/erm/cost/hooks/performance_provision/perforProvisionApi.jsx](/src/apps/erm/cost/hooks/performance_provision/perforProvisionApi.jsx) | JavaScript JSX | 32 | 4 | 3 | 39 |
| [src/apps/erm/cost/hooks/performance_provision/usePerforProvision.jsx](/src/apps/erm/cost/hooks/performance_provision/usePerforProvision.jsx) | JavaScript JSX | 150 | 7 | 4 | 161 |
| [src/apps/erm/cost/hooks/performance_provision/usePerforProvisionInfo.jsx](/src/apps/erm/cost/hooks/performance_provision/usePerforProvisionInfo.jsx) | JavaScript JSX | 2,751 | 23 | 44 | 2,818 |
| [src/apps/erm/cost/hooks/regularCost/regularCostApi.jsx](/src/apps/erm/cost/hooks/regularCost/regularCostApi.jsx) | JavaScript JSX | 27 | 3 | 3 | 33 |
| [src/apps/erm/cost/hooks/regularCost/useRegularCost.jsx](/src/apps/erm/cost/hooks/regularCost/useRegularCost.jsx) | JavaScript JSX | 117 | 0 | 3 | 120 |
| [src/apps/erm/cost/hooks/regularCost/useRegularCostInfo.jsx](/src/apps/erm/cost/hooks/regularCost/useRegularCostInfo.jsx) | JavaScript JSX | 145 | 0 | 3 | 148 |
| [src/apps/erm/cost/hooks/wages_manager/useWagesManager.jsx](/src/apps/erm/cost/hooks/wages_manager/useWagesManager.jsx) | JavaScript JSX | 101 | 75 | 6 | 182 |
| [src/apps/erm/cost/hooks/wages_manager/useWagesManagerInfo.jsx](/src/apps/erm/cost/hooks/wages_manager/useWagesManagerInfo.jsx) | JavaScript JSX | 445 | 5 | 6 | 456 |
| [src/apps/erm/cost/hooks/wages_manager/wagesManagerApi.jsx](/src/apps/erm/cost/hooks/wages_manager/wagesManagerApi.jsx) | JavaScript JSX | 28 | 6 | 1 | 35 |
| [src/apps/erm/cost/non_contractual/add.vue](/src/apps/erm/cost/non_contractual/add.vue) | vue | 77 | 0 | 4 | 81 |
| [src/apps/erm/cost/non_contractual/info.vue](/src/apps/erm/cost/non_contractual/info.vue) | vue | 137 | 1 | 6 | 144 |
| [src/apps/erm/cost/non_contractual/list.vue](/src/apps/erm/cost/non_contractual/list.vue) | vue | 128 | 1 | 13 | 142 |
| [src/apps/erm/cost/payment_contract/add.vue](/src/apps/erm/cost/payment_contract/add.vue) | vue | 75 | 0 | 4 | 79 |
| [src/apps/erm/cost/payment_contract/info.vue](/src/apps/erm/cost/payment_contract/info.vue) | vue | 130 | 1 | 6 | 137 |
| [src/apps/erm/cost/payment_contract/list.vue](/src/apps/erm/cost/payment_contract/list.vue) | vue | 128 | 1 | 11 | 140 |
| [src/apps/erm/cost/performance_provision/add.vue](/src/apps/erm/cost/performance_provision/add.vue) | vue | 72 | 0 | 5 | 77 |
| [src/apps/erm/cost/performance_provision/info.vue](/src/apps/erm/cost/performance_provision/info.vue) | vue | 88 | 0 | 7 | 95 |
| [src/apps/erm/cost/performance_provision/list.vue](/src/apps/erm/cost/performance_provision/list.vue) | vue | 128 | 0 | 6 | 134 |
| [src/apps/erm/cost/performance_provision/print.vue](/src/apps/erm/cost/performance_provision/print.vue) | vue | 129 | 0 | 8 | 137 |
| [src/apps/erm/cost/regularCost/info.vue](/src/apps/erm/cost/regularCost/info.vue) | vue | 143 | 0 | 12 | 155 |
| [src/apps/erm/cost/regularCost/list.vue](/src/apps/erm/cost/regularCost/list.vue) | vue | 104 | 0 | 9 | 113 |
| [src/apps/erm/cost/regularCost/print.vue](/src/apps/erm/cost/regularCost/print.vue) | vue | 131 | 9 | 11 | 151 |
| [src/apps/erm/cost/wages_manager/info.vue](/src/apps/erm/cost/wages_manager/info.vue) | vue | 35 | 0 | 8 | 43 |
| [src/apps/erm/cost/wages_manager/list.vue](/src/apps/erm/cost/wages_manager/list.vue) | vue | 101 | 1 | 9 | 111 |
| [src/apps/erm/hooks/Print.js](/src/apps/erm/hooks/Print.js) | JavaScript | 130 | 12 | 12 | 154 |
| [src/apps/erm/hooks/changCompare.jsx](/src/apps/erm/hooks/changCompare.jsx) | JavaScript JSX | 7 | 9 | 1 | 17 |
| [src/apps/erm/hooks/dateHooks.js](/src/apps/erm/hooks/dateHooks.js) | JavaScript | 20 | 8 | 2 | 30 |
| [src/apps/erm/hooks/exportFile.js](/src/apps/erm/hooks/exportFile.js) | JavaScript | 12 | 8 | 1 | 21 |
| [src/apps/erm/hooks/getUserInfo.js](/src/apps/erm/hooks/getUserInfo.js) | JavaScript | 4 | 0 | 0 | 4 |
| [src/apps/erm/hooks/index.js](/src/apps/erm/hooks/index.js) | JavaScript | 8 | 17 | 2 | 27 |
| [src/apps/erm/hooks/intl.js](/src/apps/erm/hooks/intl.js) | JavaScript | 19 | 15 | 2 | 36 |
| [src/apps/erm/hooks/merge.jsx](/src/apps/erm/hooks/merge.jsx) | JavaScript JSX | 21 | 4 | 2 | 27 |
| [src/apps/erm/hooks/moreBtn.jsx](/src/apps/erm/hooks/moreBtn.jsx) | JavaScript JSX | 19 | 9 | 3 | 31 |
| [src/apps/erm/personnel/component/project_manpower/baseInfo.vue](/src/apps/erm/personnel/component/project_manpower/baseInfo.vue) | vue | 110 | 0 | 13 | 123 |
| [src/apps/erm/personnel/component/project_manpower/ymSelect.vue](/src/apps/erm/personnel/component/project_manpower/ymSelect.vue) | vue | 44 | 0 | 6 | 50 |
| [src/apps/erm/personnel/component/staff_infomation/baseInfo.vue](/src/apps/erm/personnel/component/staff_infomation/baseInfo.vue) | vue | 316 | 0 | 12 | 328 |
| [src/apps/erm/personnel/component/staff_infomation/formTable.vue](/src/apps/erm/personnel/component/staff_infomation/formTable.vue) | vue | 52 | 0 | 7 | 59 |
| [src/apps/erm/personnel/hooks/dynamicApi.jsx](/src/apps/erm/personnel/hooks/dynamicApi.jsx) | JavaScript JSX | 36 | 3 | 2 | 41 |
| [src/apps/erm/personnel/hooks/projectManpowerApi.jsx](/src/apps/erm/personnel/hooks/projectManpowerApi.jsx) | JavaScript JSX | 21 | 1 | 3 | 25 |
| [src/apps/erm/personnel/hooks/staffInfoApi.jsx](/src/apps/erm/personnel/hooks/staffInfoApi.jsx) | JavaScript JSX | 59 | 11 | 1 | 71 |
| [src/apps/erm/personnel/hooks/useManpowerInfo.jsx](/src/apps/erm/personnel/hooks/useManpowerInfo.jsx) | JavaScript JSX | 216 | 1 | 5 | 222 |
| [src/apps/erm/personnel/hooks/useManpowerList.jsx](/src/apps/erm/personnel/hooks/useManpowerList.jsx) | JavaScript JSX | 91 | 7 | 4 | 102 |
| [src/apps/erm/personnel/hooks/useStaffInfo.jsx](/src/apps/erm/personnel/hooks/useStaffInfo.jsx) | JavaScript JSX | 625 | 22 | 12 | 659 |
| [src/apps/erm/personnel/hooks/useStaffList.jsx](/src/apps/erm/personnel/hooks/useStaffList.jsx) | JavaScript JSX | 94 | 8 | 3 | 105 |
| [src/apps/erm/personnel/project_manpower/add.vue](/src/apps/erm/personnel/project_manpower/add.vue) | vue | 40 | 0 | 3 | 43 |
| [src/apps/erm/personnel/project_manpower/info.vue](/src/apps/erm/personnel/project_manpower/info.vue) | vue | 42 | 0 | 5 | 47 |
| [src/apps/erm/personnel/project_manpower/list.vue](/src/apps/erm/personnel/project_manpower/list.vue) | vue | 68 | 0 | 10 | 78 |
| [src/apps/erm/personnel/staff_infomation/add.vue](/src/apps/erm/personnel/staff_infomation/add.vue) | vue | 47 | 0 | 4 | 51 |
| [src/apps/erm/personnel/staff_infomation/info.vue](/src/apps/erm/personnel/staff_infomation/info.vue) | vue | 60 | 0 | 7 | 67 |
| [src/apps/erm/personnel/staff_infomation/list.vue](/src/apps/erm/personnel/staff_infomation/list.vue) | vue | 72 | 0 | 12 | 84 |
| [src/apps/erm/projectManage/component/baseInfo.vue](/src/apps/erm/projectManage/component/baseInfo.vue) | vue | 503 | 1 | 17 | 521 |
| [src/apps/erm/projectManage/component/bulletinBorder/borderBaseInfo.vue](/src/apps/erm/projectManage/component/bulletinBorder/borderBaseInfo.vue) | vue | 40 | 0 | 5 | 45 |
| [src/apps/erm/projectManage/component/bulletinBorder/collection.vue](/src/apps/erm/projectManage/component/bulletinBorder/collection.vue) | vue | 183 | 0 | 16 | 199 |
| [src/apps/erm/projectManage/component/bulletinBorder/component/lineChart.vue](/src/apps/erm/projectManage/component/bulletinBorder/component/lineChart.vue) | vue | 201 | 0 | 17 | 218 |
| [src/apps/erm/projectManage/component/bulletinBorder/cost.vue](/src/apps/erm/projectManage/component/bulletinBorder/cost.vue) | vue | 150 | 8 | 21 | 179 |
| [src/apps/erm/projectManage/component/bulletinBorder/income.vue](/src/apps/erm/projectManage/component/bulletinBorder/income.vue) | vue | 166 | 0 | 19 | 185 |
| [src/apps/erm/projectManage/component/bulletinBorder/invoice.vue](/src/apps/erm/projectManage/component/bulletinBorder/invoice.vue) | vue | 156 | 0 | 17 | 173 |
| [src/apps/erm/projectManage/component/bulletinBorder/payment.vue](/src/apps/erm/projectManage/component/bulletinBorder/payment.vue) | vue | 168 | 0 | 15 | 183 |
| [src/apps/erm/projectManage/component/bulletinBorder/purchase.vue](/src/apps/erm/projectManage/component/bulletinBorder/purchase.vue) | vue | 8 | 0 | 2 | 10 |
| [src/apps/erm/projectManage/component/bulletinBorder/receipt.vue](/src/apps/erm/projectManage/component/bulletinBorder/receipt.vue) | vue | 178 | 0 | 14 | 192 |
| [src/apps/erm/projectManage/component/bulletinBorder/statistic.vue](/src/apps/erm/projectManage/component/bulletinBorder/statistic.vue) | vue | 223 | 0 | 27 | 250 |
| [src/apps/erm/projectManage/component/formTable.vue](/src/apps/erm/projectManage/component/formTable.vue) | vue | 40 | 8 | 5 | 53 |
| [src/apps/erm/projectManage/component/relateTable.vue](/src/apps/erm/projectManage/component/relateTable.vue) | vue | 50 | 0 | 8 | 58 |
| [src/apps/erm/projectManage/component/selectBuget.vue](/src/apps/erm/projectManage/component/selectBuget.vue) | vue | 57 | 0 | 7 | 64 |
| [src/apps/erm/projectManage/hooks/bulletinBorder/borderBaseInfo.jsx](/src/apps/erm/projectManage/hooks/bulletinBorder/borderBaseInfo.jsx) | JavaScript JSX | 283 | 8 | 6 | 297 |
| [src/apps/erm/projectManage/hooks/bulletinBorder/collection.jsx](/src/apps/erm/projectManage/hooks/bulletinBorder/collection.jsx) | JavaScript JSX | 121 | 0 | 6 | 127 |
| [src/apps/erm/projectManage/hooks/bulletinBorder/cost.jsx](/src/apps/erm/projectManage/hooks/bulletinBorder/cost.jsx) | JavaScript JSX | 83 | 0 | 4 | 87 |
| [src/apps/erm/projectManage/hooks/bulletinBorder/income.jsx](/src/apps/erm/projectManage/hooks/bulletinBorder/income.jsx) | JavaScript JSX | 95 | 0 | 3 | 98 |
| [src/apps/erm/projectManage/hooks/bulletinBorder/invoice.jsx](/src/apps/erm/projectManage/hooks/bulletinBorder/invoice.jsx) | JavaScript JSX | 87 | 0 | 3 | 90 |
| [src/apps/erm/projectManage/hooks/bulletinBorder/payment.jsx](/src/apps/erm/projectManage/hooks/bulletinBorder/payment.jsx) | JavaScript JSX | 99 | 0 | 2 | 101 |
| [src/apps/erm/projectManage/hooks/bulletinBorder/receipt.jsx](/src/apps/erm/projectManage/hooks/bulletinBorder/receipt.jsx) | JavaScript JSX | 107 | 0 | 4 | 111 |
| [src/apps/erm/projectManage/hooks/bulletinBorder/statistic.jsx](/src/apps/erm/projectManage/hooks/bulletinBorder/statistic.jsx) | JavaScript JSX | 117 | 3 | 4 | 124 |
| [src/apps/erm/projectManage/hooks/projectManagerApi.jsx](/src/apps/erm/projectManage/hooks/projectManagerApi.jsx) | JavaScript JSX | 84 | 16 | 3 | 103 |
| [src/apps/erm/projectManage/hooks/useProjectInfo.jsx](/src/apps/erm/projectManage/hooks/useProjectInfo.jsx) | JavaScript JSX | 965 | 15 | 14 | 994 |
| [src/apps/erm/projectManage/hooks/useProjectList.jsx](/src/apps/erm/projectManage/hooks/useProjectList.jsx) | JavaScript JSX | 286 | 13 | 8 | 307 |
| [src/apps/erm/projectManage/hooks/useRelatList.jsx](/src/apps/erm/projectManage/hooks/useRelatList.jsx) | JavaScript JSX | 46 | 0 | 5 | 51 |
| [src/apps/erm/projectManage/project/add.vue](/src/apps/erm/projectManage/project/add.vue) | vue | 88 | 0 | 7 | 95 |
| [src/apps/erm/projectManage/project/bulletinBoard.vue](/src/apps/erm/projectManage/project/bulletinBoard.vue) | vue | 165 | 0 | 3 | 168 |
| [src/apps/erm/projectManage/project/info.vue](/src/apps/erm/projectManage/project/info.vue) | vue | 319 | 0 | 15 | 334 |
| [src/apps/erm/projectManage/project/list.vue](/src/apps/erm/projectManage/project/list.vue) | vue | 286 | 0 | 13 | 299 |
| [src/apps/erm/projectManage/project/print.vue](/src/apps/erm/projectManage/project/print.vue) | vue | 163 | 1 | 9 | 173 |
| [src/apps/erm/purchase/component/checkSection.vue](/src/apps/erm/purchase/component/checkSection.vue) | vue | 146 | 0 | 11 | 157 |
| [src/apps/erm/purchase/component/flieTable.vue](/src/apps/erm/purchase/component/flieTable.vue) | vue | 59 | 0 | 5 | 64 |
| [src/apps/erm/purchase/evaluation_result/add.vue](/src/apps/erm/purchase/evaluation_result/add.vue) | vue | 75 | 0 | 6 | 81 |
| [src/apps/erm/purchase/evaluation_result/component/baseInfo.vue](/src/apps/erm/purchase/evaluation_result/component/baseInfo.vue) | vue | 310 | 0 | 24 | 334 |
| [src/apps/erm/purchase/evaluation_result/component/checkSection.vue](/src/apps/erm/purchase/evaluation_result/component/checkSection.vue) | vue | 154 | 0 | 11 | 165 |
| [src/apps/erm/purchase/evaluation_result/component/selectSuppler.vue](/src/apps/erm/purchase/evaluation_result/component/selectSuppler.vue) | vue | 121 | 0 | 11 | 132 |
| [src/apps/erm/purchase/evaluation_result/hooks/useEvaluationApi.jsx](/src/apps/erm/purchase/evaluation_result/hooks/useEvaluationApi.jsx) | JavaScript JSX | 23 | 0 | 2 | 25 |
| [src/apps/erm/purchase/evaluation_result/hooks/useEvaluationInfo.jsx](/src/apps/erm/purchase/evaluation_result/hooks/useEvaluationInfo.jsx) | JavaScript JSX | 450 | 3 | 10 | 463 |
| [src/apps/erm/purchase/evaluation_result/hooks/useEvaluationList.jsx](/src/apps/erm/purchase/evaluation_result/hooks/useEvaluationList.jsx) | JavaScript JSX | 176 | 0 | 4 | 180 |
| [src/apps/erm/purchase/evaluation_result/info.vue](/src/apps/erm/purchase/evaluation_result/info.vue) | vue | 82 | 0 | 6 | 88 |
| [src/apps/erm/purchase/evaluation_result/list.vue](/src/apps/erm/purchase/evaluation_result/list.vue) | vue | 137 | 0 | 7 | 144 |
| [src/apps/erm/purchase/evaluation_result/print.vue](/src/apps/erm/purchase/evaluation_result/print.vue) | vue | 154 | 3 | 12 | 169 |
| [src/apps/erm/purchase/project_approval/add.vue](/src/apps/erm/purchase/project_approval/add.vue) | vue | 84 | 0 | 7 | 91 |
| [src/apps/erm/purchase/project_approval/component/baseInfo.vue](/src/apps/erm/purchase/project_approval/component/baseInfo.vue) | vue | 350 | 1 | 27 | 378 |
| [src/apps/erm/purchase/project_approval/component/editDirectorDialog.vue](/src/apps/erm/purchase/project_approval/component/editDirectorDialog.vue) | vue | 55 | 0 | 8 | 63 |
| [src/apps/erm/purchase/project_approval/hooks/useProjectApproval.jsx](/src/apps/erm/purchase/project_approval/hooks/useProjectApproval.jsx) | JavaScript JSX | 168 | 0 | 4 | 172 |
| [src/apps/erm/purchase/project_approval/hooks/useProjectApprovalApi.jsx](/src/apps/erm/purchase/project_approval/hooks/useProjectApprovalApi.jsx) | JavaScript JSX | 25 | 0 | 2 | 27 |
| [src/apps/erm/purchase/project_approval/hooks/useProjectApprovalInfo.jsx](/src/apps/erm/purchase/project_approval/hooks/useProjectApprovalInfo.jsx) | JavaScript JSX | 522 | 10 | 12 | 544 |
| [src/apps/erm/purchase/project_approval/info.vue](/src/apps/erm/purchase/project_approval/info.vue) | vue | 112 | 0 | 8 | 120 |
| [src/apps/erm/purchase/project_approval/list.vue](/src/apps/erm/purchase/project_approval/list.vue) | vue | 180 | 1 | 17 | 198 |
| [src/apps/erm/purchase/project_approval/print.vue](/src/apps/erm/purchase/project_approval/print.vue) | vue | 129 | 3 | 11 | 143 |
| [src/apps/erm/purchase/recruitment_plan/add.vue](/src/apps/erm/purchase/recruitment_plan/add.vue) | vue | 85 | 0 | 7 | 92 |
| [src/apps/erm/purchase/recruitment_plan/component/baseInfo.vue](/src/apps/erm/purchase/recruitment_plan/component/baseInfo.vue) | vue | 267 | 0 | 20 | 287 |
| [src/apps/erm/purchase/recruitment_plan/component/checkSection.vue](/src/apps/erm/purchase/recruitment_plan/component/checkSection.vue) | vue | 163 | 0 | 10 | 173 |
| [src/apps/erm/purchase/recruitment_plan/component/documentSealDialog.vue](/src/apps/erm/purchase/recruitment_plan/component/documentSealDialog.vue) | vue | 59 | 0 | 3 | 62 |
| [src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentApi.jsx](/src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentApi.jsx) | JavaScript JSX | 27 | 0 | 2 | 29 |
| [src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentInfo.jsx](/src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentInfo.jsx) | JavaScript JSX | 228 | 4 | 7 | 239 |
| [src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentList.jsx](/src/apps/erm/purchase/recruitment_plan/hooks/useRecruitmentList.jsx) | JavaScript JSX | 152 | 0 | 3 | 155 |
| [src/apps/erm/purchase/recruitment_plan/info.vue](/src/apps/erm/purchase/recruitment_plan/info.vue) | vue | 125 | 0 | 7 | 132 |
| [src/apps/erm/purchase/recruitment_plan/list.vue](/src/apps/erm/purchase/recruitment_plan/list.vue) | vue | 165 | 1 | 14 | 180 |
| [src/apps/erm/purchase/recruitment_plan/print.vue](/src/apps/erm/purchase/recruitment_plan/print.vue) | vue | 159 | 3 | 11 | 173 |
| [src/apps/erm/skip.config.json](/src/apps/erm/skip.config.json) | JSON | 438 | 0 | 0 | 438 |
| [src/apps/erm/supplier/component/baseInfo.vue](/src/apps/erm/supplier/component/baseInfo.vue) | vue | 223 | 0 | 17 | 240 |
| [src/apps/erm/supplier/component/formTable.vue](/src/apps/erm/supplier/component/formTable.vue) | vue | 55 | 8 | 7 | 70 |
| [src/apps/erm/supplier/component/invoiceInfo.vue](/src/apps/erm/supplier/component/invoiceInfo.vue) | vue | 200 | 0 | 18 | 218 |
| [src/apps/erm/supplier/hooks/supplierApi.jsx](/src/apps/erm/supplier/hooks/supplierApi.jsx) | JavaScript JSX | 42 | 9 | 1 | 52 |
| [src/apps/erm/supplier/hooks/useSupplierInfo.jsx](/src/apps/erm/supplier/hooks/useSupplierInfo.jsx) | JavaScript JSX | 565 | 16 | 14 | 595 |
| [src/apps/erm/supplier/hooks/useSupplierList.jsx](/src/apps/erm/supplier/hooks/useSupplierList.jsx) | JavaScript JSX | 88 | 9 | 6 | 103 |
| [src/apps/erm/supplier/supplier/add.vue](/src/apps/erm/supplier/supplier/add.vue) | vue | 44 | 0 | 6 | 50 |
| [src/apps/erm/supplier/supplier/info.vue](/src/apps/erm/supplier/supplier/info.vue) | vue | 45 | 0 | 6 | 51 |
| [src/apps/erm/supplier/supplier/list.vue](/src/apps/erm/supplier/supplier/list.vue) | vue | 89 | 0 | 8 | 97 |
| [src/apps/erm/system/company_info/add.vue](/src/apps/erm/system/company_info/add.vue) | vue | 46 | 8 | 5 | 59 |
| [src/apps/erm/system/company_info/info.vue](/src/apps/erm/system/company_info/info.vue) | vue | 43 | 0 | 3 | 46 |
| [src/apps/erm/system/company_info/list.vue](/src/apps/erm/system/company_info/list.vue) | vue | 78 | 8 | 10 | 96 |
| [src/apps/erm/system/component/company_info/baseInfo.vue](/src/apps/erm/system/component/company_info/baseInfo.vue) | vue | 140 | 0 | 13 | 153 |
| [src/apps/erm/system/component/department_info/baseInfo.vue](/src/apps/erm/system/component/department_info/baseInfo.vue) | vue | 139 | 0 | 12 | 151 |
| [src/apps/erm/system/department_info/add.vue](/src/apps/erm/system/department_info/add.vue) | vue | 46 | 8 | 5 | 59 |
| [src/apps/erm/system/department_info/info.vue](/src/apps/erm/system/department_info/info.vue) | vue | 43 | 0 | 3 | 46 |
| [src/apps/erm/system/department_info/list.vue](/src/apps/erm/system/department_info/list.vue) | vue | 74 | 8 | 8 | 90 |
| [src/apps/erm/system/hooks/company/api.js](/src/apps/erm/system/hooks/company/api.js) | JavaScript | 18 | 4 | 5 | 27 |
| [src/apps/erm/system/hooks/company/useCompanyInfo.jsx](/src/apps/erm/system/hooks/company/useCompanyInfo.jsx) | JavaScript JSX | 116 | 44 | 3 | 163 |
| [src/apps/erm/system/hooks/company/useCompanyList.jsx](/src/apps/erm/system/hooks/company/useCompanyList.jsx) | JavaScript JSX | 58 | 38 | 5 | 101 |
| [src/apps/erm/system/hooks/department/api.js](/src/apps/erm/system/hooks/department/api.js) | JavaScript | 18 | 4 | 5 | 27 |
| [src/apps/erm/system/hooks/department/useDepartmentInfo.jsx](/src/apps/erm/system/hooks/department/useDepartmentInfo.jsx) | JavaScript JSX | 140 | 2 | 3 | 145 |
| [src/apps/erm/system/hooks/department/useDepartmentList.jsx](/src/apps/erm/system/hooks/department/useDepartmentList.jsx) | JavaScript JSX | 53 | 23 | 4 | 80 |
| [src/assets/icons/client_switch.svg](/src/assets/icons/client_switch.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/client_switch_close.svg](/src/assets/icons/client_switch_close.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/collapse.svg](/src/assets/icons/collapse.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/fullscreen.svg](/src/assets/icons/fullscreen.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/fullscreen_light.svg](/src/assets/icons/fullscreen_light.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/icon-home.svg](/src/assets/icons/icon-home.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/lock_light.svg](/src/assets/icons/lock_light.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/refresh.svg](/src/assets/icons/refresh.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/resize.svg](/src/assets/icons/resize.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/setting.svg](/src/assets/icons/setting.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/stripe.svg](/src/assets/icons/stripe.svg) | XML | 1 | 0 | 0 | 1 |
| [src/assets/icons/theme_light.svg](/src/assets/icons/theme_light.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniActions/index.vue](/src/components/FuniActions/index.vue) | vue | 6 | 0 | 2 | 8 |
| [src/components/FuniAuditButtomBtn/index.vue](/src/components/FuniAuditButtomBtn/index.vue) | vue | 51 | 9 | 4 | 64 |
| [src/components/FuniAuthButton/index.vue](/src/components/FuniAuthButton/index.vue) | vue | 3 | 9 | 1 | 13 |
| [src/components/FuniBpmn/api/api.js](/src/components/FuniBpmn/api/api.js) | JavaScript | 109 | 9 | 12 | 130 |
| [src/components/FuniBpmn/api/js-base64.js](/src/components/FuniBpmn/api/js-base64.js) | JavaScript | 206 | 90 | 0 | 296 |
| [src/components/FuniBpmn/bpmn-uilt/funi-bpmn-dataFunc/funi-bpmn-dataFunc.js](/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-dataFunc/funi-bpmn-dataFunc.js) | JavaScript | 62 | 17 | 13 | 92 |
| [src/components/FuniBpmn/bpmn-uilt/funi-bpmn-dataFunc/index.js](/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-dataFunc/index.js) | JavaScript | 2 | 9 | 0 | 11 |
| [src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-default-listener.js](/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-default-listener.js) | JavaScript | 31 | 13 | 4 | 48 |
| [src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-operationData.js](/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-operationData.js) | JavaScript | 144 | 38 | 38 | 220 |
| [src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-setNativeProperty.js](/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-setNativeProperty.js) | JavaScript | 52 | 14 | 12 | 78 |
| [src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/index.js](/src/components/FuniBpmn/bpmn-uilt/funi-bpmn-operationData/index.js) | JavaScript | 3 | 9 | 0 | 12 |
| [src/components/FuniBpmn/component/bpmn/businessFunc.js](/src/components/FuniBpmn/component/bpmn/businessFunc.js) | JavaScript | 119 | 49 | 16 | 184 |
| [src/components/FuniBpmn/component/bpmn/index.css](/src/components/FuniBpmn/component/bpmn/index.css) | CSS | 83 | 0 | 11 | 94 |
| [src/components/FuniBpmn/component/bpmn/index.vue](/src/components/FuniBpmn/component/bpmn/index.vue) | vue | 689 | 10 | 41 | 740 |
| [src/components/FuniBpmn/component/bpmn/previewXML.vue](/src/components/FuniBpmn/component/bpmn/previewXML.vue) | vue | 34 | 11 | 5 | 50 |
| [src/components/FuniBpmn/component/funiModule/btnGroup/iconSet/icon.json](/src/components/FuniBpmn/component/funiModule/btnGroup/iconSet/icon.json) | JSON | 122 | 0 | 1 | 123 |
| [src/components/FuniBpmn/component/funiModule/btnGroup/iconSet/index.vue](/src/components/FuniBpmn/component/funiModule/btnGroup/iconSet/index.vue) | vue | 136 | 9 | 6 | 151 |
| [src/components/FuniBpmn/component/funiModule/btnGroup/index.css](/src/components/FuniBpmn/component/funiModule/btnGroup/index.css) | CSS | 78 | 0 | 15 | 93 |
| [src/components/FuniBpmn/component/funiModule/btnGroup/index.vue](/src/components/FuniBpmn/component/funiModule/btnGroup/index.vue) | vue | 321 | 9 | 17 | 347 |
| [src/components/FuniBpmn/component/funiModule/common.jsx](/src/components/FuniBpmn/component/funiModule/common.jsx) | JavaScript JSX | 244 | 11 | 17 | 272 |
| [src/components/FuniBpmn/component/funiModule/customizeItem/index.css](/src/components/FuniBpmn/component/funiModule/customizeItem/index.css) | CSS | 111 | 1 | 26 | 138 |
| [src/components/FuniBpmn/component/funiModule/customizeItem/org.vue](/src/components/FuniBpmn/component/funiModule/customizeItem/org.vue) | vue | 61 | 0 | 2 | 63 |
| [src/components/FuniBpmn/component/funiModule/customizeItem/personAndRole.vue](/src/components/FuniBpmn/component/funiModule/customizeItem/personAndRole.vue) | vue | 133 | 9 | 7 | 149 |
| [src/components/FuniBpmn/component/funiModule/customizeItem/role.vue](/src/components/FuniBpmn/component/funiModule/customizeItem/role.vue) | vue | 179 | 0 | 7 | 186 |
| [src/components/FuniBpmn/component/funiModule/customizeItem/user.vue](/src/components/FuniBpmn/component/funiModule/customizeItem/user.vue) | vue | 176 | 0 | 6 | 182 |
| [src/components/FuniBpmn/component/funiModule/extendInfo/index.vue](/src/components/FuniBpmn/component/funiModule/extendInfo/index.vue) | vue | 145 | 9 | 6 | 160 |
| [src/components/FuniBpmn/component/funiModule/extendInfo/listTable.vue](/src/components/FuniBpmn/component/funiModule/extendInfo/listTable.vue) | vue | 186 | 0 | 10 | 196 |
| [src/components/FuniBpmn/component/funiModule/function/index.css](/src/components/FuniBpmn/component/funiModule/function/index.css) | CSS | 80 | 0 | 13 | 93 |
| [src/components/FuniBpmn/component/funiModule/function/index.vue](/src/components/FuniBpmn/component/funiModule/function/index.vue) | vue | 81 | 10 | 7 | 98 |
| [src/components/FuniBpmn/component/funiModule/index.vue](/src/components/FuniBpmn/component/funiModule/index.vue) | vue | 361 | 9 | 30 | 400 |
| [src/components/FuniBpmn/component/funiModule/prevPerson/index.vue](/src/components/FuniBpmn/component/funiModule/prevPerson/index.vue) | vue | 48 | 9 | 3 | 60 |
| [src/components/FuniBpmn/component/funiModule/selectBpmn/index.vue](/src/components/FuniBpmn/component/funiModule/selectBpmn/index.vue) | vue | 57 | 9 | 2 | 68 |
| [src/components/FuniBpmn/index.vue](/src/components/FuniBpmn/index.vue) | vue | 138 | 8 | 10 | 156 |
| [src/components/FuniBusAuditDrawer/index.vue](/src/components/FuniBusAuditDrawer/index.vue) | vue | 426 | 7 | 26 | 459 |
| [src/components/FuniCimMapDialog/index.vue](/src/components/FuniCimMapDialog/index.vue) | vue | 113 | 9 | 8 | 130 |
| [src/components/FuniCodemirror/index.vue](/src/components/FuniCodemirror/index.vue) | vue | 62 | 0 | 8 | 70 |
| [src/components/FuniCurd/Pagination.vue](/src/components/FuniCurd/Pagination.vue) | vue | 62 | 9 | 11 | 82 |
| [src/components/FuniCurd/index.vue](/src/components/FuniCurd/index.vue) | vue | 483 | 19 | 63 | 565 |
| [src/components/FuniCurd/useDraggable.jsx](/src/components/FuniCurd/useDraggable.jsx) | JavaScript JSX | 27 | 0 | 3 | 30 |
| [src/components/FuniCurd/useResetSummaryWidth.js](/src/components/FuniCurd/useResetSummaryWidth.js) | JavaScript | 25 | 9 | 2 | 36 |
| [src/components/FuniCurdColumn/index.vue](/src/components/FuniCurdColumn/index.vue) | vue | 52 | 0 | 7 | 59 |
| [src/components/FuniCurdPro/element-plus-render.ts](/src/components/FuniCurdPro/element-plus-render.ts) | TypeScript | 585 | 28 | 27 | 640 |
| [src/components/FuniCurdPro/index.vue](/src/components/FuniCurdPro/index.vue) | vue | 99 | 0 | 14 | 113 |
| [src/components/FuniCurdV2/components/CurdColumnSetting.vue](/src/components/FuniCurdV2/components/CurdColumnSetting.vue) | vue | 102 | 4 | 15 | 121 |
| [src/components/FuniCurdV2/components/CurdFilterPanel.vue](/src/components/FuniCurdV2/components/CurdFilterPanel.vue) | vue | 130 | 0 | 16 | 146 |
| [src/components/FuniCurdV2/components/CurdHeader.vue](/src/components/FuniCurdV2/components/CurdHeader.vue) | vue | 97 | 5 | 13 | 115 |
| [src/components/FuniCurdV2/components/CurdPagination.vue](/src/components/FuniCurdV2/components/CurdPagination.vue) | vue | 61 | 9 | 11 | 81 |
| [src/components/FuniCurdV2/components/CurdSearch.vue](/src/components/FuniCurdV2/components/CurdSearch.vue) | vue | 49 | 9 | 8 | 66 |
| [src/components/FuniCurdV2/hooks/useColSetting.js](/src/components/FuniCurdV2/hooks/useColSetting.js) | JavaScript | 36 | 0 | 7 | 43 |
| [src/components/FuniCurdV2/hooks/useDraggable.jsx](/src/components/FuniCurdV2/hooks/useDraggable.jsx) | JavaScript JSX | 27 | 0 | 3 | 30 |
| [src/components/FuniCurdV2/hooks/useFullscreen.js](/src/components/FuniCurdV2/hooks/useFullscreen.js) | JavaScript | 7 | 0 | 2 | 9 |
| [src/components/FuniCurdV2/hooks/useRender.jsx](/src/components/FuniCurdV2/hooks/useRender.jsx) | JavaScript JSX | 26 | 0 | 5 | 31 |
| [src/components/FuniCurdV2/hooks/useResetSummaryWidth.js](/src/components/FuniCurdV2/hooks/useResetSummaryWidth.js) | JavaScript | 25 | 9 | 2 | 36 |
| [src/components/FuniCurdV2/hooks/useSelection.js](/src/components/FuniCurdV2/hooks/useSelection.js) | JavaScript | 73 | 49 | 19 | 141 |
| [src/components/FuniCurdV2/index.vue](/src/components/FuniCurdV2/index.vue) | vue | 537 | 16 | 70 | 623 |
| [src/components/FuniDetail/FuniDetailContent.vue](/src/components/FuniDetail/FuniDetailContent.vue) | vue | 173 | 10 | 16 | 199 |
| [src/components/FuniDetail/FuniDetailHead.vue](/src/components/FuniDetail/FuniDetailHead.vue) | vue | 106 | 6 | 5 | 117 |
| [src/components/FuniDetail/FuniSteps.vue](/src/components/FuniDetail/FuniSteps.vue) | vue | 125 | 1 | 3 | 129 |
| [src/components/FuniDetail/index.vue](/src/components/FuniDetail/index.vue) | vue | 330 | 1 | 28 | 359 |
| [src/components/FuniDialog/index.vue](/src/components/FuniDialog/index.vue) | vue | 114 | 0 | 17 | 131 |
| [src/components/FuniEditor/TinyMceEditor.vue](/src/components/FuniEditor/TinyMceEditor.vue) | vue | 226 | 0 | 13 | 239 |
| [src/components/FuniEditor/VditorEditor.vue](/src/components/FuniEditor/VditorEditor.vue) | vue | 64 | 9 | 7 | 80 |
| [src/components/FuniEditor/index.vue](/src/components/FuniEditor/index.vue) | vue | 46 | 0 | 4 | 50 |
| [src/components/FuniEditor/tinymce/langs/zh-Hans.js](/src/components/FuniEditor/tinymce/langs/zh-Hans.js) | JavaScript | 406 | 6 | 0 | 412 |
| [src/components/FuniEditor/tinymce/skins/content/dark/content.css](/src/components/FuniEditor/tinymce/skins/content/dark/content.css) | CSS | 59 | 13 | 1 | 73 |
| [src/components/FuniEditor/tinymce/skins/content/dark/content.min.css](/src/components/FuniEditor/tinymce/skins/content/dark/content.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/content/default/content.css](/src/components/FuniEditor/tinymce/skins/content/default/content.css) | CSS | 54 | 13 | 1 | 68 |
| [src/components/FuniEditor/tinymce/skins/content/default/content.min.css](/src/components/FuniEditor/tinymce/skins/content/default/content.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/content/document/content.css](/src/components/FuniEditor/tinymce/skins/content/document/content.css) | CSS | 59 | 13 | 1 | 73 |
| [src/components/FuniEditor/tinymce/skins/content/document/content.min.css](/src/components/FuniEditor/tinymce/skins/content/document/content.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/content/writer/content.css](/src/components/FuniEditor/tinymce/skins/content/writer/content.css) | CSS | 55 | 13 | 1 | 69 |
| [src/components/FuniEditor/tinymce/skins/content/writer/content.min.css](/src/components/FuniEditor/tinymce/skins/content/writer/content.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.css) | CSS | 697 | 17 | 1 | 715 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.inline.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.inline.css) | CSS | 710 | 16 | 1 | 727 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.inline.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.inline.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.mobile.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.mobile.css) | CSS | 21 | 8 | 1 | 30 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.mobile.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/content.mobile.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.css) | CSS | 3,025 | 22 | 1 | 3,048 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.mobile.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.mobile.css) | CSS | 643 | 30 | 1 | 674 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.mobile.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.mobile.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.shadowdom.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.shadowdom.css) | CSS | 31 | 6 | 1 | 38 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/content.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/content.css) | CSS | 716 | 16 | 1 | 733 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/content.inline.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/content.inline.css) | CSS | 710 | 16 | 1 | 727 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/content.inline.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/content.inline.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/content.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/content.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/content.mobile.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/content.mobile.css) | CSS | 21 | 8 | 1 | 30 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/content.mobile.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/content.mobile.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/skin.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.css) | CSS | 3,025 | 22 | 1 | 3,048 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/skin.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/skin.mobile.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.mobile.css) | CSS | 643 | 30 | 1 | 674 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/skin.mobile.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.mobile.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/skin.shadowdom.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.shadowdom.css) | CSS | 31 | 6 | 1 | 38 |
| [src/components/FuniEditor/tinymce/skins/ui/oxide/skin.shadowdom.min.css](/src/components/FuniEditor/tinymce/skins/ui/oxide/skin.shadowdom.min.css) | CSS | 1 | 6 | 1 | 8 |
| [src/components/FuniEventEditor/ActionContext/ActionContextItem.vue](/src/components/FuniEventEditor/ActionContext/ActionContextItem.vue) | vue | 47 | 0 | 5 | 52 |
| [src/components/FuniEventEditor/ActionContext/ActionContextStatusItem.vue](/src/components/FuniEventEditor/ActionContext/ActionContextStatusItem.vue) | vue | 41 | 2 | 6 | 49 |
| [src/components/FuniEventEditor/ActionContext/index.jsx](/src/components/FuniEventEditor/ActionContext/index.jsx) | JavaScript JSX | 106 | 0 | 19 | 125 |
| [src/components/FuniEventEditor/ActionContext/index.scss](/src/components/FuniEventEditor/ActionContext/index.scss) | SCSS | 773 | 3 | 232 | 1,008 |
| [src/components/FuniEventEditor/ActionDetailPanel.vue](/src/components/FuniEventEditor/ActionDetailPanel.vue) | vue | 92 | 9 | 12 | 113 |
| [src/components/FuniEventEditor/ActionGridPanel.vue](/src/components/FuniEventEditor/ActionGridPanel.vue) | vue | 103 | 0 | 15 | 118 |
| [src/components/FuniEventEditor/ActionSettingPanel/ActionSettingCallMethod.vue](/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingCallMethod.vue) | vue | 91 | 0 | 13 | 104 |
| [src/components/FuniEventEditor/ActionSettingPanel/ActionSettingCloseModal.vue](/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingCloseModal.vue) | vue | 42 | 0 | 9 | 51 |
| [src/components/FuniEventEditor/ActionSettingPanel/ActionSettingEmitEvent.vue](/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingEmitEvent.vue) | vue | 77 | 0 | 12 | 89 |
| [src/components/FuniEventEditor/ActionSettingPanel/ActionSettingEmpty.vue](/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingEmpty.vue) | vue | 31 | 0 | 6 | 37 |
| [src/components/FuniEventEditor/ActionSettingPanel/ActionSettingNavigateTo.vue](/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingNavigateTo.vue) | vue | 80 | 0 | 12 | 92 |
| [src/components/FuniEventEditor/ActionSettingPanel/ActionSettingSetState.vue](/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingSetState.vue) | vue | 37 | 0 | 8 | 45 |
| [src/components/FuniEventEditor/ActionSettingPanel/ActionSettingShowModal.vue](/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingShowModal.vue) | vue | 45 | 0 | 9 | 54 |
| [src/components/FuniEventEditor/ActionSettingPanel/ActionSettingValidateForm.vue](/src/components/FuniEventEditor/ActionSettingPanel/ActionSettingValidateForm.vue) | vue | 42 | 0 | 9 | 51 |
| [src/components/FuniEventEditor/ActionSettingPanel/index.vue](/src/components/FuniEventEditor/ActionSettingPanel/index.vue) | vue | 58 | 0 | 9 | 67 |
| [src/components/FuniEventEditor/Logic/index.ts](/src/components/FuniEventEditor/Logic/index.ts) | TypeScript | 33 | 8 | 12 | 53 |
| [src/components/FuniEventEditor/Logic/types.ts](/src/components/FuniEventEditor/Logic/types.ts) | TypeScript | 53 | 28 | 21 | 102 |
| [src/components/FuniEventEditor/assets/arrow.svg](/src/components/FuniEventEditor/assets/arrow.svg) | XML | 6 | 0 | 1 | 7 |
| [src/components/FuniEventEditor/enums.js](/src/components/FuniEventEditor/enums.js) | JavaScript | 23 | 3 | 3 | 29 |
| [src/components/FuniEventEditor/function.js](/src/components/FuniEventEditor/function.js) | JavaScript | 146 | 0 | 2 | 148 |
| [src/components/FuniEventEditor/index.vue](/src/components/FuniEventEditor/index.vue) | vue | 97 | 0 | 12 | 109 |
| [src/components/FuniEventEditor/symbols.js](/src/components/FuniEventEditor/symbols.js) | JavaScript | 1 | 0 | 1 | 2 |
| [src/components/FuniFilePreview/index.vue](/src/components/FuniFilePreview/index.vue) | vue | 36 | 9 | 5 | 50 |
| [src/components/FuniFileTable/index.vue](/src/components/FuniFileTable/index.vue) | vue | 463 | 1 | 26 | 490 |
| [src/components/FuniFileTable/upload.vue](/src/components/FuniFileTable/upload.vue) | vue | 131 | 17 | 15 | 163 |
| [src/components/FuniForm/hooks/updateLabelWidth.js](/src/components/FuniForm/hooks/updateLabelWidth.js) | JavaScript | 51 | 11 | 10 | 72 |
| [src/components/FuniForm/index.vue](/src/components/FuniForm/index.vue) | vue | 343 | 11 | 47 | 401 |
| [src/components/FuniFormEngine/DialogDesigner.vue](/src/components/FuniFormEngine/DialogDesigner.vue) | vue | 450 | 9 | 61 | 520 |
| [src/components/FuniFormEngine/code-editor/index.vue](/src/components/FuniFormEngine/code-editor/index.vue) | vue | 130 | 1 | 17 | 148 |
| [src/components/FuniFormEngine/common/iconfont/iconfont.css](/src/components/FuniFormEngine/common/iconfont/iconfont.css) | CSS | 23 | 0 | 7 | 30 |
| [src/components/FuniFormEngine/common/iconfont/iconfont.js](/src/components/FuniFormEngine/common/iconfont/iconfont.js) | JavaScript | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/iconfont/iconfont.json](/src/components/FuniFormEngine/common/iconfont/iconfont.json) | JSON | 37 | 0 | 1 | 38 |
| [src/components/FuniFormEngine/common/iconfont/iconfont.svg](/src/components/FuniFormEngine/common/iconfont/iconfont.svg) | XML | 22 | 3 | 11 | 36 |
| [src/components/FuniFormEngine/common/icons/svg/address-edit.svg](/src/components/FuniFormEngine/common/icons/svg/address-edit.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/alert.svg](/src/components/FuniFormEngine/common/icons/svg/alert.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/button.svg](/src/components/FuniFormEngine/common/icons/svg/button.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/card.svg](/src/components/FuniFormEngine/common/icons/svg/card.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/cascader-field.svg](/src/components/FuniFormEngine/common/icons/svg/cascader-field.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/checkbox-field.svg](/src/components/FuniFormEngine/common/icons/svg/checkbox-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/color-field.svg](/src/components/FuniFormEngine/common/icons/svg/color-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/custom-component.svg](/src/components/FuniFormEngine/common/icons/svg/custom-component.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/custom/search.svg](/src/components/FuniFormEngine/common/icons/svg/custom/search.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/data-table.svg](/src/components/FuniFormEngine/common/icons/svg/data-table.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/date-field.svg](/src/components/FuniFormEngine/common/icons/svg/date-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/date-range-field.svg](/src/components/FuniFormEngine/common/icons/svg/date-range-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/divider.svg](/src/components/FuniFormEngine/common/icons/svg/divider.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/document.svg](/src/components/FuniFormEngine/common/icons/svg/document.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/drag.svg](/src/components/FuniFormEngine/common/icons/svg/drag.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/draggable-curd.svg](/src/components/FuniFormEngine/common/icons/svg/draggable-curd.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/edit-curd.svg](/src/components/FuniFormEngine/common/icons/svg/edit-curd.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/editList.svg](/src/components/FuniFormEngine/common/icons/svg/editList.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/arrow-down.svg](/src/components/FuniFormEngine/common/icons/svg/el/arrow-down.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/back.svg](/src/components/FuniFormEngine/common/icons/svg/el/back.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/el/check.svg](/src/components/FuniFormEngine/common/icons/svg/el/check.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/clone.svg](/src/components/FuniFormEngine/common/icons/svg/el/clone.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/delete.svg](/src/components/FuniFormEngine/common/icons/svg/el/delete.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/el/download.svg](/src/components/FuniFormEngine/common/icons/svg/el/download.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/drag-move.svg](/src/components/FuniFormEngine/common/icons/svg/el/drag-move.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/form-template.svg](/src/components/FuniFormEngine/common/icons/svg/el/form-template.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/hide.svg](/src/components/FuniFormEngine/common/icons/svg/el/hide.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/info.svg](/src/components/FuniFormEngine/common/icons/svg/el/info.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/insert-column.svg](/src/components/FuniFormEngine/common/icons/svg/el/insert-column.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/insert-row.svg](/src/components/FuniFormEngine/common/icons/svg/el/insert-row.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/menu.svg](/src/components/FuniFormEngine/common/icons/svg/el/menu.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/move-down.svg](/src/components/FuniFormEngine/common/icons/svg/el/move-down.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/move-up.svg](/src/components/FuniFormEngine/common/icons/svg/el/move-up.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/plus.svg](/src/components/FuniFormEngine/common/icons/svg/el/plus.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/set-up.svg](/src/components/FuniFormEngine/common/icons/svg/el/set-up.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/el/view.svg](/src/components/FuniFormEngine/common/icons/svg/el/view.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/el/zoom-in.svg](/src/components/FuniFormEngine/common/icons/svg/el/zoom-in.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/file-table.svg](/src/components/FuniFormEngine/common/icons/svg/file-table.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/file-upload-field.svg](/src/components/FuniFormEngine/common/icons/svg/file-upload-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/funi-histogram-chart.svg](/src/components/FuniFormEngine/common/icons/svg/funi-histogram-chart.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/funi-label.svg](/src/components/FuniFormEngine/common/icons/svg/funi-label.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/gantt.svg](/src/components/FuniFormEngine/common/icons/svg/gantt.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/github.svg](/src/components/FuniFormEngine/common/icons/svg/github.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/grid.svg](/src/components/FuniFormEngine/common/icons/svg/grid.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/group-title.svg](/src/components/FuniFormEngine/common/icons/svg/group-title.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/guid.svg](/src/components/FuniFormEngine/common/icons/svg/guid.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/histogram.svg](/src/components/FuniFormEngine/common/icons/svg/histogram.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/html-text.svg](/src/components/FuniFormEngine/common/icons/svg/html-text.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/line.svg](/src/components/FuniFormEngine/common/icons/svg/line.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/node-tree.svg](/src/components/FuniFormEngine/common/icons/svg/node-tree.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/number-field.svg](/src/components/FuniFormEngine/common/icons/svg/number-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/org.svg](/src/components/FuniFormEngine/common/icons/svg/org.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/picture-upload-field.svg](/src/components/FuniFormEngine/common/icons/svg/picture-upload-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/pie.svg](/src/components/FuniFormEngine/common/icons/svg/pie.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/radar.svg](/src/components/FuniFormEngine/common/icons/svg/radar.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/radio-field.svg](/src/components/FuniFormEngine/common/icons/svg/radio-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/rate-field.svg](/src/components/FuniFormEngine/common/icons/svg/rate-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/redo.svg](/src/components/FuniFormEngine/common/icons/svg/redo.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/region.svg](/src/components/FuniFormEngine/common/icons/svg/region.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/rich-editor-field.svg](/src/components/FuniFormEngine/common/icons/svg/rich-editor-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/scatterplot.svg](/src/components/FuniFormEngine/common/icons/svg/scatterplot.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/section.svg](/src/components/FuniFormEngine/common/icons/svg/section.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/select-field.svg](/src/components/FuniFormEngine/common/icons/svg/select-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/sfc-dialog.svg](/src/components/FuniFormEngine/common/icons/svg/sfc-dialog.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/sfc-funi-log.svg](/src/components/FuniFormEngine/common/icons/svg/sfc-funi-log.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/sfc-iframe.svg](/src/components/FuniFormEngine/common/icons/svg/sfc-iframe.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/show-curd.svg](/src/components/FuniFormEngine/common/icons/svg/show-curd.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/slider-field.svg](/src/components/FuniFormEngine/common/icons/svg/slider-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/slot-component.svg](/src/components/FuniFormEngine/common/icons/svg/slot-component.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/slot-field.svg](/src/components/FuniFormEngine/common/icons/svg/slot-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/static-text.svg](/src/components/FuniFormEngine/common/icons/svg/static-text.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/sub-form.svg](/src/components/FuniFormEngine/common/icons/svg/sub-form.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/switch-field.svg](/src/components/FuniFormEngine/common/icons/svg/switch-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/tab.svg](/src/components/FuniFormEngine/common/icons/svg/tab.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/table.svg](/src/components/FuniFormEngine/common/icons/svg/table.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/text-field.svg](/src/components/FuniFormEngine/common/icons/svg/text-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/textarea-field.svg](/src/components/FuniFormEngine/common/icons/svg/textarea-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/time-field.svg](/src/components/FuniFormEngine/common/icons/svg/time-field.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/icons/svg/time-range-field.svg](/src/components/FuniFormEngine/common/icons/svg/time-range-field.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/undo.svg](/src/components/FuniFormEngine/common/icons/svg/undo.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/user.svg](/src/components/FuniFormEngine/common/icons/svg/user.svg) | XML | 1 | 0 | 0 | 1 |
| [src/components/FuniFormEngine/common/icons/svg/vue-sfc.svg](/src/components/FuniFormEngine/common/icons/svg/vue-sfc.svg) | XML | 1 | 0 | 1 | 2 |
| [src/components/FuniFormEngine/common/js/designer.js](/src/components/FuniFormEngine/common/js/designer.js) | JavaScript | 929 | 60 | 136 | 1,125 |
| [src/components/FuniFormEngine/common/js/refMixinDesign.js](/src/components/FuniFormEngine/common/js/refMixinDesign.js) | JavaScript | 24 | 1 | 4 | 29 |
| [src/components/FuniFormEngine/common/lang/en-US.js](/src/components/FuniFormEngine/common/lang/en-US.js) | JavaScript | 330 | 0 | 20 | 350 |
| [src/components/FuniFormEngine/common/lang/en-US_extension.js](/src/components/FuniFormEngine/common/lang/en-US_extension.js) | JavaScript | 75 | 9 | 3 | 87 |
| [src/components/FuniFormEngine/common/lang/en-US_render.js](/src/components/FuniFormEngine/common/lang/en-US_render.js) | JavaScript | 34 | 0 | 5 | 39 |
| [src/components/FuniFormEngine/common/lang/zh-CN.js](/src/components/FuniFormEngine/common/lang/zh-CN.js) | JavaScript | 330 | 0 | 20 | 350 |
| [src/components/FuniFormEngine/common/lang/zh-CN_extension.js](/src/components/FuniFormEngine/common/lang/zh-CN_extension.js) | JavaScript | 82 | 8 | 2 | 92 |
| [src/components/FuniFormEngine/common/lang/zh-CN_render.js](/src/components/FuniFormEngine/common/lang/zh-CN_render.js) | JavaScript | 34 | 8 | 4 | 46 |
| [src/components/FuniFormEngine/common/styles/global.scss](/src/components/FuniFormEngine/common/styles/global.scss) | SCSS | 67 | 1 | 16 | 84 |
| [src/components/FuniFormEngine/common/styles/index.scss](/src/components/FuniFormEngine/common/styles/index.scss) | SCSS | 13 | 0 | 3 | 16 |
| [src/components/FuniFormEngine/common/utils/beautifierLoader.js](/src/components/FuniFormEngine/common/utils/beautifierLoader.js) | JavaScript | 72 | 9 | 5 | 86 |
| [src/components/FuniFormEngine/common/utils/code-generator.js](/src/components/FuniFormEngine/common/utils/code-generator.js) | JavaScript | 74 | 4 | 9 | 87 |
| [src/components/FuniFormEngine/common/utils/config.js](/src/components/FuniFormEngine/common/utils/config.js) | JavaScript | 4 | 11 | 4 | 19 |
| [src/components/FuniFormEngine/common/utils/create-app.js](/src/components/FuniFormEngine/common/utils/create-app.js) | JavaScript | 3 | 0 | 1 | 4 |
| [src/components/FuniFormEngine/common/utils/debug-console.js](/src/components/FuniFormEngine/common/utils/debug-console.js) | JavaScript | 7 | 0 | 1 | 8 |
| [src/components/FuniFormEngine/common/utils/directive.js](/src/components/FuniFormEngine/common/utils/directive.js) | JavaScript | 98 | 29 | 28 | 155 |
| [src/components/FuniFormEngine/common/utils/el-icons.js](/src/components/FuniFormEngine/common/utils/el-icons.js) | JavaScript | 15 | 0 | 2 | 17 |
| [src/components/FuniFormEngine/common/utils/emitter.js](/src/components/FuniFormEngine/common/utils/emitter.js) | JavaScript | 84 | 3 | 14 | 101 |
| [src/components/FuniFormEngine/common/utils/event-bus.js](/src/components/FuniFormEngine/common/utils/event-bus.js) | JavaScript | 7 | 0 | 3 | 10 |
| [src/components/FuniFormEngine/common/utils/funi-sfc-generator.js](/src/components/FuniFormEngine/common/utils/funi-sfc-generator.js) | JavaScript | 1,174 | 27 | 96 | 1,297 |
| [src/components/FuniFormEngine/common/utils/i18n.js](/src/components/FuniFormEngine/common/utils/i18n.js) | JavaScript | 50 | 13 | 11 | 74 |
| [src/components/FuniFormEngine/common/utils/lowcode.js](/src/components/FuniFormEngine/common/utils/lowcode.js) | JavaScript | 1,845 | 106 | 17 | 1,968 |
| [src/components/FuniFormEngine/common/utils/sfc-generator.js](/src/components/FuniFormEngine/common/utils/sfc-generator.js) | JavaScript | 584 | 16 | 83 | 683 |
| [src/components/FuniFormEngine/common/utils/smart-vue-i18n/index.js](/src/components/FuniFormEngine/common/utils/smart-vue-i18n/index.js) | JavaScript | 24 | 0 | 7 | 31 |
| [src/components/FuniFormEngine/common/utils/smart-vue-i18n/utils.js](/src/components/FuniFormEngine/common/utils/smart-vue-i18n/utils.js) | JavaScript | 33 | 4 | 11 | 48 |
| [src/components/FuniFormEngine/common/utils/util.js](/src/components/FuniFormEngine/common/utils/util.js) | JavaScript | 789 | 31 | 63 | 883 |
| [src/components/FuniFormEngine/common/utils/validators.js](/src/components/FuniFormEngine/common/utils/validators.js) | JavaScript | 75 | 30 | 23 | 128 |
| [src/components/FuniFormEngine/common/utils/vue2js-generator.js](/src/components/FuniFormEngine/common/utils/vue2js-generator.js) | JavaScript | 827 | 7 | 41 | 875 |
| [src/components/FuniFormEngine/common/utils/vue3SfcCompiler.js](/src/components/FuniFormEngine/common/utils/vue3SfcCompiler.js) | JavaScript | 324 | 8 | 4 | 336 |
| [src/components/FuniFormEngine/common/utils/vue3js-generator.js](/src/components/FuniFormEngine/common/utils/vue3js-generator.js) | JavaScript | 351 | 8 | 34 | 393 |
| [src/components/FuniFormEngine/components/sfc-blank/index.vue](/src/components/FuniFormEngine/components/sfc-blank/index.vue) | vue | 222 | 9 | 0 | 231 |
| [src/components/FuniFormEngine/components/sfc-button/index.vue](/src/components/FuniFormEngine/components/sfc-button/index.vue) | vue | 42 | 9 | 0 | 51 |
| [src/components/FuniFormEngine/components/sfc-chart/index.vue](/src/components/FuniFormEngine/components/sfc-chart/index.vue) | vue | 72 | 9 | 5 | 86 |
| [src/components/FuniFormEngine/components/sfc-checkbox-group/index.vue](/src/components/FuniFormEngine/components/sfc-checkbox-group/index.vue) | vue | 138 | 9 | 7 | 154 |
| [src/components/FuniFormEngine/components/sfc-curd/index.vue](/src/components/FuniFormEngine/components/sfc-curd/index.vue) | vue | 496 | 0 | 4 | 500 |
| [src/components/FuniFormEngine/components/sfc-dialog-render/index.vue](/src/components/FuniFormEngine/components/sfc-dialog-render/index.vue) | vue | 46 | 9 | 3 | 58 |
| [src/components/FuniFormEngine/components/sfc-dialog/index.vue](/src/components/FuniFormEngine/components/sfc-dialog/index.vue) | vue | 91 | 9 | 3 | 103 |
| [src/components/FuniFormEngine/components/sfc-file-table/index.vue](/src/components/FuniFormEngine/components/sfc-file-table/index.vue) | vue | 43 | 9 | 3 | 55 |
| [src/components/FuniFormEngine/components/sfc-form-item/index.vue](/src/components/FuniFormEngine/components/sfc-form-item/index.vue) | vue | 68 | 9 | 4 | 81 |
| [src/components/FuniFormEngine/components/sfc-form/index.vue](/src/components/FuniFormEngine/components/sfc-form/index.vue) | vue | 133 | 9 | 9 | 151 |
| [src/components/FuniFormEngine/components/sfc-funi-log-dialog/service.js](/src/components/FuniFormEngine/components/sfc-funi-log-dialog/service.js) | JavaScript | 11 | 0 | 2 | 13 |
| [src/components/FuniFormEngine/components/sfc-funi-log-dialog/sfc-funi-log-inner-dialog.vue](/src/components/FuniFormEngine/components/sfc-funi-log-dialog/sfc-funi-log-inner-dialog.vue) | vue | 31 | 9 | 3 | 43 |
| [src/components/FuniFormEngine/components/sfc-funi-log/index.vue](/src/components/FuniFormEngine/components/sfc-funi-log/index.vue) | vue | 19 | 9 | 3 | 31 |
| [src/components/FuniFormEngine/components/sfc-gantt/index.vue](/src/components/FuniFormEngine/components/sfc-gantt/index.vue) | vue | 72 | 9 | 11 | 92 |
| [src/components/FuniFormEngine/components/sfc-guid/index.vue](/src/components/FuniFormEngine/components/sfc-guid/index.vue) | vue | 55 | 9 | 3 | 67 |
| [src/components/FuniFormEngine/components/sfc-iframe/index.vue](/src/components/FuniFormEngine/components/sfc-iframe/index.vue) | vue | 63 | 9 | 1 | 73 |
| [src/components/FuniFormEngine/components/sfc-operation-log/index.vue](/src/components/FuniFormEngine/components/sfc-operation-log/index.vue) | vue | 25 | 9 | 1 | 35 |
| [src/components/FuniFormEngine/components/sfc-org-name/index.vue](/src/components/FuniFormEngine/components/sfc-org-name/index.vue) | vue | 29 | 0 | 3 | 32 |
| [src/components/FuniFormEngine/components/sfc-org/index.vue](/src/components/FuniFormEngine/components/sfc-org/index.vue) | vue | 138 | 9 | 10 | 157 |
| [src/components/FuniFormEngine/components/sfc-radio-group/index.vue](/src/components/FuniFormEngine/components/sfc-radio-group/index.vue) | vue | 120 | 0 | 9 | 129 |
| [src/components/FuniFormEngine/components/sfc-region/index.vue](/src/components/FuniFormEngine/components/sfc-region/index.vue) | vue | 48 | 9 | 2 | 59 |
| [src/components/FuniFormEngine/components/sfc-select/index.vue](/src/components/FuniFormEngine/components/sfc-select/index.vue) | vue | 300 | 9 | 8 | 317 |
| [src/components/FuniFormEngine/components/sfc-show-curd/index.vue](/src/components/FuniFormEngine/components/sfc-show-curd/index.vue) | vue | 544 | 0 | 2 | 546 |
| [src/components/FuniFormEngine/components/sfc-step/index.vue](/src/components/FuniFormEngine/components/sfc-step/index.vue) | vue | 504 | 9 | 2 | 515 |
| [src/components/FuniFormEngine/components/sfc-upload-dialog/service.js](/src/components/FuniFormEngine/components/sfc-upload-dialog/service.js) | JavaScript | 35 | 9 | 1 | 45 |
| [src/components/FuniFormEngine/components/sfc-upload-dialog/sfc-upload-inner-dialog.vue](/src/components/FuniFormEngine/components/sfc-upload-dialog/sfc-upload-inner-dialog.vue) | vue | 136 | 9 | 3 | 148 |
| [src/components/FuniFormEngine/components/sfc-upload/index.vue](/src/components/FuniFormEngine/components/sfc-upload/index.vue) | vue | 231 | 0 | 13 | 244 |
| [src/components/FuniFormEngine/components/sfc-user/index.vue](/src/components/FuniFormEngine/components/sfc-user/index.vue) | vue | 138 | 0 | 11 | 149 |
| [src/components/FuniFormEngine/extension/extension-helper.js](/src/components/FuniFormEngine/extension/extension-helper.js) | JavaScript | 20 | 8 | 4 | 32 |
| [src/components/FuniFormEngine/extension/extension-loader.js](/src/components/FuniFormEngine/extension/extension-loader.js) | JavaScript | 38 | 64 | 20 | 122 |
| [src/components/FuniFormEngine/extension/funi/funi-curd-widget/index.vue](/src/components/FuniFormEngine/extension/funi/funi-curd-widget/index.vue) | vue | 0 | 0 | 1 | 1 |
| [src/components/FuniFormEngine/extension/funi/funi-edit-curd-widget/index.vue](/src/components/FuniFormEngine/extension/funi/funi-edit-curd-widget/index.vue) | vue | 80 | 9 | 4 | 93 |
| [src/components/FuniFormEngine/extension/funi/funi-ext-loader.js](/src/components/FuniFormEngine/extension/funi/funi-ext-loader.js) | JavaScript | 534 | 116 | 32 | 682 |
| [src/components/FuniFormEngine/extension/funi/funi-ext-schema.js](/src/components/FuniFormEngine/extension/funi/funi-ext-schema.js) | JavaScript | 518 | 20 | 19 | 557 |
| [src/components/FuniFormEngine/extension/funi/funi-ext-sfc-generator.js](/src/components/FuniFormEngine/extension/funi/funi-ext-sfc-generator.js) | JavaScript | 242 | 9 | 18 | 269 |
| [src/components/FuniFormEngine/extension/funi/funi-group-title-widget/index.vue](/src/components/FuniFormEngine/extension/funi/funi-group-title-widget/index.vue) | vue | 70 | 8 | 14 | 92 |
| [src/components/FuniFormEngine/extension/funi/funi-histogram-chart-widget/index.vue](/src/components/FuniFormEngine/extension/funi/funi-histogram-chart-widget/index.vue) | vue | 97 | 9 | 3 | 109 |
| [src/components/FuniFormEngine/extension/funi/funi-label-widget/index.vue](/src/components/FuniFormEngine/extension/funi/funi-label-widget/index.vue) | vue | 72 | 9 | 5 | 86 |
| [src/components/FuniFormEngine/extension/funi/funi-region-widget/index.vue](/src/components/FuniFormEngine/extension/funi/funi-region-widget/index.vue) | vue | 75 | 9 | 5 | 89 |
| [src/components/FuniFormEngine/extension/funi/funi-select-widget/index.vue](/src/components/FuniFormEngine/extension/funi/funi-select-widget/index.vue) | vue | 90 | 9 | 6 | 105 |
| [src/components/FuniFormEngine/extension/funi/funi-sfc-dialog-widget/index.vue](/src/components/FuniFormEngine/extension/funi/funi-sfc-dialog-widget/index.vue) | vue | 71 | 8 | 7 | 86 |
| [src/components/FuniFormEngine/extension/funi/funi-sfc-dialog-widget/sfc-dialog-widget.vue](/src/components/FuniFormEngine/extension/funi/funi-sfc-dialog-widget/sfc-dialog-widget.vue) | vue | 14 | 0 | 0 | 14 |
| [src/components/FuniFormEngine/extension/funi/funi-show-curd-widget/index.vue](/src/components/FuniFormEngine/extension/funi/funi-show-curd-widget/index.vue) | vue | 83 | 9 | 4 | 96 |
| [src/components/FuniFormEngine/extension/funi/sfc-draggable-curd-widget/curd-column-item.vue](/src/components/FuniFormEngine/extension/funi/sfc-draggable-curd-widget/curd-column-item.vue) | vue | 61 | 0 | 2 | 63 |
| [src/components/FuniFormEngine/extension/funi/sfc-draggable-curd-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-draggable-curd-widget/index.vue) | vue | 97 | 9 | 6 | 112 |
| [src/components/FuniFormEngine/extension/funi/sfc-draggable-curd-widget/sfc-draggable-curd.vue](/src/components/FuniFormEngine/extension/funi/sfc-draggable-curd-widget/sfc-draggable-curd.vue) | vue | 277 | 9 | 5 | 291 |
| [src/components/FuniFormEngine/extension/funi/sfc-file-table-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-file-table-widget/index.vue) | vue | 73 | 9 | 4 | 86 |
| [src/components/FuniFormEngine/extension/funi/sfc-funi-log-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-funi-log-widget/index.vue) | vue | 74 | 9 | 4 | 87 |
| [src/components/FuniFormEngine/extension/funi/sfc-gantt-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-gantt-widget/index.vue) | vue | 97 | 9 | 3 | 109 |
| [src/components/FuniFormEngine/extension/funi/sfc-guid-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-guid-widget/index.vue) | vue | 71 | 9 | 5 | 85 |
| [src/components/FuniFormEngine/extension/funi/sfc-iframe-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-iframe-widget/index.vue) | vue | 73 | 9 | 5 | 87 |
| [src/components/FuniFormEngine/extension/funi/sfc-operation-log-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-operation-log-widget/index.vue) | vue | 73 | 9 | 4 | 86 |
| [src/components/FuniFormEngine/extension/funi/sfc-org-name-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-org-name-widget/index.vue) | vue | 72 | 9 | 5 | 86 |
| [src/components/FuniFormEngine/extension/funi/sfc-org-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-org-widget/index.vue) | vue | 71 | 9 | 5 | 85 |
| [src/components/FuniFormEngine/extension/funi/sfc-user-name-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-user-name-widget/index.vue) | vue | 72 | 9 | 5 | 86 |
| [src/components/FuniFormEngine/extension/funi/sfc-user-widget/index.vue](/src/components/FuniFormEngine/extension/funi/sfc-user-widget/index.vue) | vue | 71 | 9 | 5 | 85 |
| [src/components/FuniFormEngine/extension/samples/alert/alert-widget.vue](/src/components/FuniFormEngine/extension/samples/alert/alert-widget.vue) | vue | 76 | 8 | 8 | 92 |
| [src/components/FuniFormEngine/extension/samples/card/card-item.vue](/src/components/FuniFormEngine/extension/samples/card/card-item.vue) | vue | 124 | 2 | 10 | 136 |
| [src/components/FuniFormEngine/extension/samples/card/card-widget.vue](/src/components/FuniFormEngine/extension/samples/card/card-widget.vue) | vue | 154 | 0 | 16 | 170 |
| [src/components/FuniFormEngine/extension/samples/extension-schema.js](/src/components/FuniFormEngine/extension/samples/extension-schema.js) | JavaScript | 45 | 8 | 3 | 56 |
| [src/components/FuniFormEngine/extension/samples/extension-sfc-generator.js](/src/components/FuniFormEngine/extension/samples/extension-sfc-generator.js) | JavaScript | 55 | 9 | 9 | 73 |
| [src/components/FuniFormEngine/form-render/container-item/container-item-wrapper.vue](/src/components/FuniFormEngine/form-render/container-item/container-item-wrapper.vue) | vue | 20 | 9 | 6 | 35 |
| [src/components/FuniFormEngine/form-render/container-item/containerItemMixin.js](/src/components/FuniFormEngine/form-render/container-item/containerItemMixin.js) | JavaScript | 156 | 26 | 34 | 216 |
| [src/components/FuniFormEngine/form-render/container-item/grid-col-item.vue](/src/components/FuniFormEngine/form-render/container-item/grid-col-item.vue) | vue | 134 | 2 | 12 | 148 |
| [src/components/FuniFormEngine/form-render/container-item/grid-item.vue](/src/components/FuniFormEngine/form-render/container-item/grid-item.vue) | vue | 58 | 1 | 8 | 67 |
| [src/components/FuniFormEngine/form-render/container-item/index.js](/src/components/FuniFormEngine/form-render/container-item/index.js) | JavaScript | 9 | 0 | 3 | 12 |
| [src/components/FuniFormEngine/form-render/container-item/sub-form-item.vue](/src/components/FuniFormEngine/form-render/container-item/sub-form-item.vue) | vue | 381 | 1 | 58 | 440 |
| [src/components/FuniFormEngine/form-render/container-item/tab-item.vue](/src/components/FuniFormEngine/form-render/container-item/tab-item.vue) | vue | 107 | 2 | 8 | 117 |
| [src/components/FuniFormEngine/form-render/container-item/table-cell-item.vue](/src/components/FuniFormEngine/form-render/container-item/table-cell-item.vue) | vue | 77 | 2 | 7 | 86 |
| [src/components/FuniFormEngine/form-render/container-item/table-item.vue](/src/components/FuniFormEngine/form-render/container-item/table-item.vue) | vue | 72 | 9 | 8 | 89 |
| [src/components/FuniFormEngine/form-render/index.vue](/src/components/FuniFormEngine/form-render/index.vue) | vue | 635 | 11 | 83 | 729 |
| [src/components/FuniFormEngine/form-render/refMixin.js](/src/components/FuniFormEngine/form-render/refMixin.js) | JavaScript | 25 | 8 | 6 | 39 |
| [src/components/FuniFormEngine/form-widget/container-widget/container-wrapper.vue](/src/components/FuniFormEngine/form-widget/container-widget/container-wrapper.vue) | vue | 108 | 9 | 13 | 130 |
| [src/components/FuniFormEngine/form-widget/container-widget/containerMixin.js](/src/components/FuniFormEngine/form-widget/container-widget/containerMixin.js) | JavaScript | 79 | 3 | 18 | 100 |
| [src/components/FuniFormEngine/form-widget/container-widget/grid-col-widget.vue](/src/components/FuniFormEngine/form-widget/container-widget/grid-col-widget.vue) | vue | 313 | 0 | 40 | 353 |
| [src/components/FuniFormEngine/form-widget/container-widget/grid-widget.vue](/src/components/FuniFormEngine/form-widget/container-widget/grid-widget.vue) | vue | 81 | 9 | 13 | 103 |
| [src/components/FuniFormEngine/form-widget/container-widget/index.js](/src/components/FuniFormEngine/form-widget/container-widget/index.js) | JavaScript | 9 | 8 | 2 | 19 |
| [src/components/FuniFormEngine/form-widget/container-widget/tab-widget.vue](/src/components/FuniFormEngine/form-widget/container-widget/tab-widget.vue) | vue | 127 | 9 | 15 | 151 |
| [src/components/FuniFormEngine/form-widget/container-widget/table-cell-widget.vue](/src/components/FuniFormEngine/form-widget/container-widget/table-cell-widget.vue) | vue | 335 | 0 | 52 | 387 |
| [src/components/FuniFormEngine/form-widget/container-widget/table-widget.vue](/src/components/FuniFormEngine/form-widget/container-widget/table-widget.vue) | vue | 104 | 9 | 15 | 128 |
| [src/components/FuniFormEngine/form-widget/field-widget/button-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/button-widget.vue) | vue | 83 | 8 | 15 | 106 |
| [src/components/FuniFormEngine/form-widget/field-widget/cascader-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/cascader-widget.vue) | vue | 108 | 0 | 16 | 124 |
| [src/components/FuniFormEngine/form-widget/field-widget/checkbox-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/checkbox-widget.vue) | vue | 91 | 0 | 14 | 105 |
| [src/components/FuniFormEngine/form-widget/field-widget/color-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/color-widget.vue) | vue | 91 | 0 | 15 | 106 |
| [src/components/FuniFormEngine/form-widget/field-widget/curd-column-item.vue](/src/components/FuniFormEngine/form-widget/field-widget/curd-column-item.vue) | vue | 79 | 9 | 2 | 90 |
| [src/components/FuniFormEngine/form-widget/field-widget/date-range-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/date-range-widget.vue) | vue | 109 | 0 | 17 | 126 |
| [src/components/FuniFormEngine/form-widget/field-widget/date-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/date-widget.vue) | vue | 101 | 0 | 15 | 116 |
| [src/components/FuniFormEngine/form-widget/field-widget/divider-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/divider-widget.vue) | vue | 76 | 0 | 15 | 91 |
| [src/components/FuniFormEngine/form-widget/field-widget/fieldMixin.js](/src/components/FuniFormEngine/form-widget/field-widget/fieldMixin.js) | JavaScript | 480 | 83 | 95 | 658 |
| [src/components/FuniFormEngine/form-widget/field-widget/file-upload-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/file-upload-widget.vue) | vue | 290 | 1 | 39 | 330 |
| [src/components/FuniFormEngine/form-widget/field-widget/form-item-wrapper.vue](/src/components/FuniFormEngine/form-widget/field-widget/form-item-wrapper.vue) | vue | 326 | 9 | 55 | 390 |
| [src/components/FuniFormEngine/form-widget/field-widget/html-text-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/html-text-widget.vue) | vue | 74 | 0 | 15 | 89 |
| [src/components/FuniFormEngine/form-widget/field-widget/index.js](/src/components/FuniFormEngine/form-widget/field-widget/index.js) | JavaScript | 7 | 0 | 3 | 10 |
| [src/components/FuniFormEngine/form-widget/field-widget/input-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/input-widget.vue) | vue | 114 | 0 | 15 | 129 |
| [src/components/FuniFormEngine/form-widget/field-widget/number-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/number-widget.vue) | vue | 100 | 0 | 15 | 115 |
| [src/components/FuniFormEngine/form-widget/field-widget/picture-upload-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/picture-upload-widget.vue) | vue | 291 | 5 | 39 | 335 |
| [src/components/FuniFormEngine/form-widget/field-widget/radio-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/radio-widget.vue) | vue | 95 | 0 | 15 | 110 |
| [src/components/FuniFormEngine/form-widget/field-widget/rate-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/rate-widget.vue) | vue | 96 | 0 | 15 | 111 |
| [src/components/FuniFormEngine/form-widget/field-widget/rich-editor-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/rich-editor-widget.vue) | vue | 136 | 7 | 19 | 162 |
| [src/components/FuniFormEngine/form-widget/field-widget/select-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/select-widget.vue) | vue | 114 | 0 | 15 | 129 |
| [src/components/FuniFormEngine/form-widget/field-widget/slider-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/slider-widget.vue) | vue | 96 | 0 | 15 | 111 |
| [src/components/FuniFormEngine/form-widget/field-widget/slot-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/slot-widget.vue) | vue | 97 | 3 | 17 | 117 |
| [src/components/FuniFormEngine/form-widget/field-widget/static-content-wrapper.vue](/src/components/FuniFormEngine/form-widget/field-widget/static-content-wrapper.vue) | vue | 193 | 0 | 30 | 223 |
| [src/components/FuniFormEngine/form-widget/field-widget/static-text-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/static-text-widget.vue) | vue | 73 | 8 | 15 | 96 |
| [src/components/FuniFormEngine/form-widget/field-widget/switch-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/switch-widget.vue) | vue | 95 | 0 | 15 | 110 |
| [src/components/FuniFormEngine/form-widget/field-widget/textarea-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/textarea-widget.vue) | vue | 98 | 0 | 14 | 112 |
| [src/components/FuniFormEngine/form-widget/field-widget/time-range-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/time-range-widget.vue) | vue | 110 | 0 | 17 | 127 |
| [src/components/FuniFormEngine/form-widget/field-widget/time-widget.vue](/src/components/FuniFormEngine/form-widget/field-widget/time-widget.vue) | vue | 100 | 0 | 15 | 115 |
| [src/components/FuniFormEngine/form-widget/index.vue](/src/components/FuniFormEngine/form-widget/index.vue) | vue | 244 | 0 | 39 | 283 |
| [src/components/FuniFormEngine/funi-sfc-code/index.vue](/src/components/FuniFormEngine/funi-sfc-code/index.vue) | vue | 82 | 8 | 2 | 92 |
| [src/components/FuniFormEngine/funi-sfc-preview/FormPreview.vue](/src/components/FuniFormEngine/funi-sfc-preview/FormPreview.vue) | vue | 81 | 9 | 0 | 90 |
| [src/components/FuniFormEngine/funi-sfc-preview/SfcRender.vue](/src/components/FuniFormEngine/funi-sfc-preview/SfcRender.vue) | vue | 44 | 8 | 4 | 56 |
| [src/components/FuniFormEngine/funi-sfc-preview/index.vue](/src/components/FuniFormEngine/funi-sfc-preview/index.vue) | vue | 73 | 8 | 3 | 84 |
| [src/components/FuniFormEngine/index.vue](/src/components/FuniFormEngine/index.vue) | vue | 509 | 47 | 69 | 625 |
| [src/components/FuniFormEngine/install.js](/src/components/FuniFormEngine/install.js) | JavaScript | 81 | 9 | 1 | 91 |
| [src/components/FuniFormEngine/setting-panel/dialog-designer-dialog.vue](/src/components/FuniFormEngine/setting-panel/dialog-designer-dialog.vue) | vue | 55 | 9 | 5 | 69 |
| [src/components/FuniFormEngine/setting-panel/dialog-items-setting.vue](/src/components/FuniFormEngine/setting-panel/dialog-items-setting.vue) | vue | 83 | 9 | 12 | 104 |
| [src/components/FuniFormEngine/setting-panel/dialog-setting.vue](/src/components/FuniFormEngine/setting-panel/dialog-setting.vue) | vue | 214 | 6 | 2 | 222 |
| [src/components/FuniFormEngine/setting-panel/event-editor-dialog.vue](/src/components/FuniFormEngine/setting-panel/event-editor-dialog.vue) | vue | 114 | 9 | 13 | 136 |
| [src/components/FuniFormEngine/setting-panel/form-setting.vue](/src/components/FuniFormEngine/setting-panel/form-setting.vue) | vue | 582 | 43 | 37 | 662 |
| [src/components/FuniFormEngine/setting-panel/form-setting1.vue](/src/components/FuniFormEngine/setting-panel/form-setting1.vue) | vue | 484 | 46 | 37 | 567 |
| [src/components/FuniFormEngine/setting-panel/index.vue](/src/components/FuniFormEngine/setting-panel/index.vue) | vue | 466 | 0 | 54 | 520 |
| [src/components/FuniFormEngine/setting-panel/index1.vue](/src/components/FuniFormEngine/setting-panel/index1.vue) | vue | 463 | 0 | 54 | 517 |
| [src/components/FuniFormEngine/setting-panel/option-items-setting.vue](/src/components/FuniFormEngine/setting-panel/option-items-setting.vue) | vue | 287 | 0 | 30 | 317 |
| [src/components/FuniFormEngine/setting-panel/property-editor-factory.jsx](/src/components/FuniFormEngine/setting-panel/property-editor-factory.jsx) | JavaScript JSX | 736 | 3 | 26 | 765 |
| [src/components/FuniFormEngine/setting-panel/property-editor/allowCreate-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/allowCreate-editor.vue) | vue | 20 | 16 | 5 | 41 |
| [src/components/FuniFormEngine/setting-panel/property-editor/appendButton-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/appendButton-editor.vue) | vue | 24 | 0 | 5 | 29 |
| [src/components/FuniFormEngine/setting-panel/property-editor/appendButtonDisabled-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/appendButtonDisabled-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/autoFullWidth-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/autoFullWidth-editor.vue) | vue | 19 | 0 | 5 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/automaticDropdown-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/automaticDropdown-editor.vue) | vue | 19 | 0 | 5 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/border-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/border-editor.vue) | vue | 19 | 0 | 5 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/buttonIcon-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/buttonIcon-editor.vue) | vue | 19 | 0 | 5 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/buttonStyle-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/buttonStyle-editor.vue) | vue | 19 | 8 | 4 | 31 |
| [src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-code-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-code-dialog.vue) | vue | 71 | 9 | 7 | 87 |
| [src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-editor-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-editor-dialog.vue) | vue | 379 | 9 | 3 | 391 |
| [src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-editor.vue) | vue | 80 | 9 | 0 | 89 |
| [src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-event-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/buttons-editor/buttons-event-dialog.vue) | vue | 169 | 9 | 15 | 193 |
| [src/components/FuniFormEngine/setting-panel/property-editor/clearable-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/clearable-editor.vue) | vue | 19 | 8 | 4 | 31 |
| [src/components/FuniFormEngine/setting-panel/property-editor/code-editor/code-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/code-editor/code-editor.vue) | vue | 88 | 9 | 7 | 104 |
| [src/components/FuniFormEngine/setting-panel/property-editor/column-editor/code-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/code-dialog.vue) | vue | 69 | 9 | 7 | 85 |
| [src/components/FuniFormEngine/setting-panel/property-editor/column-editor/column-code-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/column-code-dialog.vue) | vue | 97 | 12 | 10 | 119 |
| [src/components/FuniFormEngine/setting-panel/property-editor/column-editor/column-editor-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/column-editor-dialog.vue) | vue | 806 | 0 | 2 | 808 |
| [src/components/FuniFormEngine/setting-panel/property-editor/column-editor/column-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/column-editor.vue) | vue | 64 | 9 | 1 | 74 |
| [src/components/FuniFormEngine/setting-panel/property-editor/column-editor/components/sfc-select-setting-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/column-editor/components/sfc-select-setting-dialog.vue) | vue | 122 | 9 | 5 | 136 |
| [src/components/FuniFormEngine/setting-panel/property-editor/columnWidth-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/columnWidth-editor.vue) | vue | 37 | 0 | 5 | 42 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-offset-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-offset-editor.vue) | vue | 22 | 0 | 4 | 26 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-pull-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-pull-editor.vue) | vue | 20 | 0 | 5 | 25 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-push-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-push-editor.vue) | vue | 20 | 0 | 5 | 25 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-responsive-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-responsive-editor.vue) | vue | 19 | 0 | 5 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-span-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-grid-col/grid-col-span-editor.vue) | vue | 51 | 0 | 6 | 57 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-grid/colHeight-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-grid/colHeight-editor.vue) | vue | 26 | 8 | 5 | 39 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-grid/gutter-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-grid/gutter-editor.vue) | vue | 82 | 0 | 10 | 92 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-sub-form/showBlankRow-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-sub-form/showBlankRow-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-sub-form/showRowNumber-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-sub-form/showRowNumber-editor.vue) | vue | 19 | 0 | 5 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-sub-form/sub-form-labelAlign-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-sub-form/sub-form-labelAlign-editor.vue) | vue | 33 | 0 | 4 | 37 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-tab/tab-customClass-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-tab/tab-customClass-editor.vue) | vue | 122 | 4 | 15 | 141 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-table-cell/cellHeight-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-table-cell/cellHeight-editor.vue) | vue | 20 | 0 | 4 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/container-table-cell/cellWidth-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/container-table-cell/cellWidth-editor.vue) | vue | 20 | 8 | 4 | 32 |
| [src/components/FuniFormEngine/setting-panel/property-editor/customClass-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/customClass-editor.vue) | vue | 42 | 0 | 4 | 46 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/api-setting.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/api-setting.vue) | vue | 297 | 9 | 4 | 310 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/datasource-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/datasource-editor.vue) | vue | 74 | 9 | 4 | 87 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/datasource-manager.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/datasource-manager.vue) | vue | 175 | 9 | 2 | 186 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/model-setting.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/model-setting.vue) | vue | 287 | 9 | 3 | 299 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/options-setting.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/options-setting.vue) | vue | 32 | 9 | 5 | 46 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-group-setting-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-group-setting-dialog.vue) | vue | 154 | 0 | 8 | 162 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-group-setting.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-group-setting.vue) | vue | 83 | 9 | 2 | 94 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-group.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-group.vue) | vue | 155 | 0 | 10 | 165 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-setting-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-setting-dialog.vue) | vue | 253 | 9 | 8 | 270 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-setting.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/param-setting.vue) | vue | 78 | 9 | 2 | 89 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/sort-setting-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/sort-setting-dialog.vue) | vue | 131 | 9 | 10 | 150 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/sort-setting.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/sort-setting.vue) | vue | 50 | 9 | 6 | 65 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/sql-setting.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/sql-setting.vue) | vue | 270 | 9 | 4 | 283 |
| [src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/typeCode-setting.vue](/src/components/FuniFormEngine/setting-panel/property-editor/datasource-editor/typeCode-setting.vue) | vue | 97 | 9 | 4 | 110 |
| [src/components/FuniFormEngine/setting-panel/property-editor/defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/defaultValue-editor.vue) | vue | 47 | 9 | 5 | 61 |
| [src/components/FuniFormEngine/setting-panel/property-editor/disabled-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/disabled-editor.vue) | vue | 31 | 9 | 3 | 43 |
| [src/components/FuniFormEngine/setting-panel/property-editor/displayStyle-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/displayStyle-editor.vue) | vue | 22 | 0 | 4 | 26 |
| [src/components/FuniFormEngine/setting-panel/property-editor/editable-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/editable-editor.vue) | vue | 19 | 8 | 5 | 32 |
| [src/components/FuniFormEngine/setting-panel/property-editor/endPlaceholder-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/endPlaceholder-editor.vue) | vue | 20 | 0 | 4 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/eventMixin.js](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/eventMixin.js) | JavaScript | 11 | 8 | 3 | 22 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/listener-editor-pro.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/listener-editor-pro.vue) | vue | 158 | 8 | 4 | 170 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/listener-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/listener-editor.vue) | vue | 31 | 8 | 4 | 43 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onAppendButtonClick-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onAppendButtonClick-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onBeforeUpload-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onBeforeUpload-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onBlur-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onBlur-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onChange-editor-pro.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onChange-editor-pro.vue) | vue | 166 | 9 | 7 | 182 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onChange-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onChange-editor.vue) | vue | 31 | 8 | 4 | 43 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onClick-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onClick-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onCreated-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onCreated-editor.vue) | vue | 31 | 8 | 5 | 44 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onFileRemove.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onFileRemove.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onFocus-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onFocus-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onInput-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onInput-editor.vue) | vue | 31 | 8 | 4 | 43 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onMounted-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onMounted-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onRemoteQuery-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onRemoteQuery-editor.vue) | vue | 31 | 8 | 4 | 43 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowAdd-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowAdd-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowChange-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowChange-editor.vue) | vue | 31 | 8 | 4 | 43 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowDelete-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowDelete-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowInsert-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onSubFormRowInsert-editor.vue) | vue | 31 | 8 | 4 | 43 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onUploadError-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onUploadError-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onUploadSuccess-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onUploadSuccess-editor.vue) | vue | 31 | 0 | 4 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onValidate-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/event-handler/onValidate-editor.vue) | vue | 31 | 8 | 4 | 43 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-button/button-type-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-button/button-type-editor.vue) | vue | 35 | 8 | 4 | 47 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-button/circle-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-button/circle-editor.vue) | vue | 20 | 8 | 4 | 32 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-button/icon-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-button/icon-editor.vue) | vue | 20 | 8 | 4 | 32 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-button/plain-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-button/plain-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-button/round-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-button/round-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/cascader-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/cascader-defaultValue-editor.vue) | vue | 15 | 0 | 4 | 19 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/cascader-multiple-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/cascader-multiple-editor.vue) | vue | 20 | 8 | 4 | 32 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/checkStrictly-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/checkStrictly-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/showAllLevels-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-cascader/showAllLevels-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-checkbox/checkbox-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-checkbox/checkbox-defaultValue-editor.vue) | vue | 15 | 0 | 4 | 19 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-color/color-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-color/color-defaultValue-editor.vue) | vue | 22 | 0 | 4 | 26 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-defaultValue-editor.vue) | vue | 31 | 9 | 5 | 45 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-format-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-format-editor.vue) | vue | 32 | 0 | 5 | 37 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-type-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-type-editor.vue) | vue | 44 | 0 | 4 | 48 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-valueFormat-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-date-range/date-range-valueFormat-editor.vue) | vue | 32 | 0 | 4 | 36 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-defaultValue-editor.vue) | vue | 79 | 8 | 5 | 92 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-format-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-format-editor.vue) | vue | 38 | 9 | 5 | 52 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-type-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-type-editor.vue) | vue | 56 | 9 | 4 | 69 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-valueFormat-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-date/date-valueFormat-editor.vue) | vue | 38 | 9 | 4 | 51 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-divider/contentPosition-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-divider/contentPosition-editor.vue) | vue | 26 | 0 | 4 | 30 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-file-upload/file-upload-fileTypes-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-file-upload/file-upload-fileTypes-editor.vue) | vue | 51 | 8 | 4 | 63 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-html-text/htmlContent-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-html-text/htmlContent-editor.vue) | vue | 29 | 0 | 4 | 33 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-number/controlsPosition-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-number/controlsPosition-editor.vue) | vue | 22 | 0 | 6 | 28 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-number/number-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-number/number-defaultValue-editor.vue) | vue | 31 | 9 | 5 | 45 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-picture-upload/picture-upload-fileTypes-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-picture-upload/picture-upload-fileTypes-editor.vue) | vue | 50 | 8 | 4 | 62 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-radio/radio-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-radio/radio-defaultValue-editor.vue) | vue | 15 | 0 | 4 | 19 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-rate/allowHalf-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/allowHalf-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-rate/highThreshold-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/highThreshold-editor.vue) | vue | 20 | 0 | 5 | 25 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-rate/lowThreshold-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/lowThreshold-editor.vue) | vue | 20 | 0 | 5 | 25 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-rate/rate-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/rate-defaultValue-editor.vue) | vue | 25 | 0 | 4 | 29 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-rate/rate-max-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/rate-max-editor.vue) | vue | 23 | 0 | 5 | 28 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-rate/showScore-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/showScore-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-rate/showText-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-rate/showText-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-rich-editor/rich-editor-contentHeight-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-rich-editor/rich-editor-contentHeight-editor.vue) | vue | 22 | 0 | 4 | 26 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-select/select-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-select/select-defaultValue-editor.vue) | vue | 15 | 0 | 4 | 19 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-slider/range-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-slider/range-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-slider/showStops-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-slider/showStops-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-slider/vertical-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-slider/vertical-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-static-text/textContent-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-static-text/textContent-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-switch/activeColor-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/activeColor-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-switch/activeText-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/activeText-editor.vue) | vue | 19 | 8 | 4 | 31 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-switch/inactiveColor-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/inactiveColor-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-switch/inactiveText-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/inactiveText-editor.vue) | vue | 19 | 0 | 5 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-switch/switch-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/switch-defaultValue-editor.vue) | vue | 23 | 0 | 4 | 27 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-switch/switchWidth-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-switch/switchWidth-editor.vue) | vue | 20 | 8 | 4 | 32 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-time-range/time-range-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-time-range/time-range-defaultValue-editor.vue) | vue | 31 | 8 | 5 | 44 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-time-range/time-range-format-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-time-range/time-range-format-editor.vue) | vue | 28 | 0 | 4 | 32 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-time/time-defaultValue-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-time/time-defaultValue-editor.vue) | vue | 31 | 8 | 5 | 44 |
| [src/components/FuniFormEngine/setting-panel/property-editor/field-time/time-format-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/field-time/time-format-editor.vue) | vue | 28 | 8 | 4 | 40 |
| [src/components/FuniFormEngine/setting-panel/property-editor/fieldVariablesMixins.js](/src/components/FuniFormEngine/setting-panel/property-editor/fieldVariablesMixins.js) | JavaScript | 106 | 9 | 1 | 116 |
| [src/components/FuniFormEngine/setting-panel/property-editor/fileMaxSize-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/fileMaxSize-editor.vue) | vue | 22 | 0 | 4 | 26 |
| [src/components/FuniFormEngine/setting-panel/property-editor/filterable-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/filterable-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/form-dialog/form-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/form-dialog/form-dialog.vue) | vue | 54 | 9 | 5 | 68 |
| [src/components/FuniFormEngine/setting-panel/property-editor/gantt-column-editor/gantt-column-editor-dialog.vue](/src/components/FuniFormEngine/setting-panel/property-editor/gantt-column-editor/gantt-column-editor-dialog.vue) | vue | 244 | 0 | 1 | 245 |
| [src/components/FuniFormEngine/setting-panel/property-editor/gantt-column-editor/gantt-column-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/gantt-column-editor/gantt-column-editor.vue) | vue | 40 | 9 | 2 | 51 |
| [src/components/FuniFormEngine/setting-panel/property-editor/hidden-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/hidden-editor.vue) | vue | 31 | 9 | 3 | 43 |
| [src/components/FuniFormEngine/setting-panel/property-editor/icon-editor/icon-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/icon-editor/icon-editor.vue) | vue | 134 | 9 | 4 | 147 |
| [src/components/FuniFormEngine/setting-panel/property-editor/index.js](/src/components/FuniFormEngine/setting-panel/property-editor/index.js) | JavaScript | 7 | 9 | 3 | 19 |
| [src/components/FuniFormEngine/setting-panel/property-editor/label-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/label-editor.vue) | vue | 27 | 0 | 5 | 32 |
| [src/components/FuniFormEngine/setting-panel/property-editor/labelAlign-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/labelAlign-editor.vue) | vue | 40 | 0 | 5 | 45 |
| [src/components/FuniFormEngine/setting-panel/property-editor/labelHidden-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/labelHidden-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/labelIconClass-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/labelIconClass-editor.vue) | vue | 25 | 0 | 4 | 29 |
| [src/components/FuniFormEngine/setting-panel/property-editor/labelIconPosition-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/labelIconPosition-editor.vue) | vue | 33 | 0 | 6 | 39 |
| [src/components/FuniFormEngine/setting-panel/property-editor/labelTooltip-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/labelTooltip-editor.vue) | vue | 20 | 0 | 4 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/labelWidth-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/labelWidth-editor.vue) | vue | 24 | 0 | 4 | 28 |
| [src/components/FuniFormEngine/setting-panel/property-editor/limit-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/limit-editor.vue) | vue | 22 | 0 | 5 | 27 |
| [src/components/FuniFormEngine/setting-panel/property-editor/max-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/max-editor.vue) | vue | 35 | 0 | 6 | 41 |
| [src/components/FuniFormEngine/setting-panel/property-editor/maxLength-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/maxLength-editor.vue) | vue | 39 | 0 | 5 | 44 |
| [src/components/FuniFormEngine/setting-panel/property-editor/min-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/min-editor.vue) | vue | 36 | 0 | 6 | 42 |
| [src/components/FuniFormEngine/setting-panel/property-editor/minLength-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/minLength-editor.vue) | vue | 39 | 0 | 6 | 45 |
| [src/components/FuniFormEngine/setting-panel/property-editor/mode-editor/mode-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/mode-editor/mode-editor.vue) | vue | 25 | 9 | 1 | 35 |
| [src/components/FuniFormEngine/setting-panel/property-editor/modelField-editor/modelField-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/modelField-editor/modelField-editor.vue) | vue | 75 | 9 | 3 | 87 |
| [src/components/FuniFormEngine/setting-panel/property-editor/multiple-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/multiple-editor.vue) | vue | 21 | 8 | 4 | 33 |
| [src/components/FuniFormEngine/setting-panel/property-editor/multipleLimit-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/multipleLimit-editor.vue) | vue | 22 | 0 | 4 | 26 |
| [src/components/FuniFormEngine/setting-panel/property-editor/multipleSelect-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/multipleSelect-editor.vue) | vue | 19 | 0 | 5 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/name-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/name-editor.vue) | vue | 200 | 0 | 13 | 213 |
| [src/components/FuniFormEngine/setting-panel/property-editor/optionItems-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/optionItems-editor.vue) | vue | 114 | 8 | 5 | 127 |
| [src/components/FuniFormEngine/setting-panel/property-editor/placeholder-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/placeholder-editor.vue) | vue | 21 | 0 | 4 | 25 |
| [src/components/FuniFormEngine/setting-panel/property-editor/precision-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/precision-editor.vue) | vue | 22 | 0 | 4 | 26 |
| [src/components/FuniFormEngine/setting-panel/property-editor/prefixIcon-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/prefixIcon-editor.vue) | vue | 20 | 0 | 4 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/propertyMixin.js](/src/components/FuniFormEngine/setting-panel/property-editor/propertyMixin.js) | JavaScript | 40 | 9 | 6 | 55 |
| [src/components/FuniFormEngine/setting-panel/property-editor/readonly-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/readonly-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/remote-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/remote-editor.vue) | vue | 21 | 0 | 5 | 26 |
| [src/components/FuniFormEngine/setting-panel/property-editor/required-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/required-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/requiredHint-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/requiredHint-editor.vue) | vue | 21 | 0 | 4 | 25 |
| [src/components/FuniFormEngine/setting-panel/property-editor/rows-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/rows-editor.vue) | vue | 20 | 0 | 5 | 25 |
| [src/components/FuniFormEngine/setting-panel/property-editor/seting-curd-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/seting-curd-editor.vue) | vue | 21 | 9 | 7 | 37 |
| [src/components/FuniFormEngine/setting-panel/property-editor/showFileList-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/showFileList-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/showPassword-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/showPassword-editor.vue) | vue | 20 | 0 | 5 | 25 |
| [src/components/FuniFormEngine/setting-panel/property-editor/showWordLimit-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/showWordLimit-editor.vue) | vue | 19 | 0 | 4 | 23 |
| [src/components/FuniFormEngine/setting-panel/property-editor/size-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/size-editor.vue) | vue | 36 | 0 | 4 | 40 |
| [src/components/FuniFormEngine/setting-panel/property-editor/startPlaceholder-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/startPlaceholder-editor.vue) | vue | 20 | 0 | 4 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/step-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/step-editor.vue) | vue | 21 | 0 | 5 | 26 |
| [src/components/FuniFormEngine/setting-panel/property-editor/submit-code-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/submit-code-editor.vue) | vue | 29 | 9 | 3 | 41 |
| [src/components/FuniFormEngine/setting-panel/property-editor/suffixIcon-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/suffixIcon-editor.vue) | vue | 20 | 0 | 4 | 24 |
| [src/components/FuniFormEngine/setting-panel/property-editor/type-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/type-editor.vue) | vue | 27 | 4 | 5 | 36 |
| [src/components/FuniFormEngine/setting-panel/property-editor/uploadTip-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/uploadTip-editor.vue) | vue | 20 | 0 | 5 | 25 |
| [src/components/FuniFormEngine/setting-panel/property-editor/uploadURL-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/uploadURL-editor.vue) | vue | 25 | 8 | 4 | 37 |
| [src/components/FuniFormEngine/setting-panel/property-editor/urlPrefix-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/urlPrefix-editor.vue) | vue | 22 | 8 | 4 | 34 |
| [src/components/FuniFormEngine/setting-panel/property-editor/user-org-scope-editor/user-org-scope-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/user-org-scope-editor/user-org-scope-editor.vue) | vue | 76 | 9 | 2 | 87 |
| [src/components/FuniFormEngine/setting-panel/property-editor/validation-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/validation-editor.vue) | vue | 54 | 8 | 5 | 67 |
| [src/components/FuniFormEngine/setting-panel/property-editor/validationHint-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/validationHint-editor.vue) | vue | 20 | 8 | 4 | 32 |
| [src/components/FuniFormEngine/setting-panel/property-editor/withCredentials-editor.vue](/src/components/FuniFormEngine/setting-panel/property-editor/withCredentials-editor.vue) | vue | 19 | 8 | 4 | 31 |
| [src/components/FuniFormEngine/setting-panel/propertyRegister.js](/src/components/FuniFormEngine/setting-panel/propertyRegister.js) | JavaScript | 156 | 71 | 17 | 244 |
| [src/components/FuniFormEngine/setting-panel/switch-fn-setting.vue](/src/components/FuniFormEngine/setting-panel/switch-fn-setting.vue) | vue | 49 | 9 | 6 | 64 |
| [src/components/FuniFormEngine/setting-panel/validator-items-setting.vue](/src/components/FuniFormEngine/setting-panel/validator-items-setting.vue) | vue | 80 | 9 | 3 | 92 |
| [src/components/FuniFormEngine/setting-panel/validator-items-setting/validator-items-dialog.vue](/src/components/FuniFormEngine/setting-panel/validator-items-setting/validator-items-dialog.vue) | vue | 156 | 9 | 3 | 168 |
| [src/components/FuniFormEngine/setting-panel/validator-items-setting/validator-items-setting.vue](/src/components/FuniFormEngine/setting-panel/validator-items-setting/validator-items-setting.vue) | vue | 95 | 9 | 3 | 107 |
| [src/components/FuniFormEngine/setting-panel/variable-setter.vue](/src/components/FuniFormEngine/setting-panel/variable-setter.vue) | vue | 109 | 0 | 13 | 122 |
| [src/components/FuniFormEngine/sfc-design-dialog/index.vue](/src/components/FuniFormEngine/sfc-design-dialog/index.vue) | vue | 78 | 9 | 10 | 97 |
| [src/components/FuniFormEngine/sfc-preview/demo.js](/src/components/FuniFormEngine/sfc-preview/demo.js) | JavaScript | 12 | 0 | 0 | 12 |
| [src/components/FuniFormEngine/sfc-preview/index.vue](/src/components/FuniFormEngine/sfc-preview/index.vue) | vue | 33 | 8 | 0 | 41 |
| [src/components/FuniFormEngine/svg-icon/index.vue](/src/components/FuniFormEngine/svg-icon/index.vue) | vue | 47 | 0 | 3 | 50 |
| [src/components/FuniFormEngine/toolbar-panel/index.vue](/src/components/FuniFormEngine/toolbar-panel/index.vue) | vue | 1,079 | 19 | 128 | 1,226 |
| [src/components/FuniFormEngine/widget-panel/index.vue](/src/components/FuniFormEngine/widget-panel/index.vue) | vue | 476 | 0 | 61 | 537 |
| [src/components/FuniFormEngine/widget-panel/templatesConfig.js](/src/components/FuniFormEngine/widget-panel/templatesConfig.js) | JavaScript | 52 | 9 | 9 | 70 |
| [src/components/FuniFormEngine/widget-panel/widgetsConfig.js](/src/components/FuniFormEngine/widget-panel/widgetsConfig.js) | JavaScript | 885 | 103 | 36 | 1,024 |
| [src/components/FuniFormRender/index.vue](/src/components/FuniFormRender/index.vue) | vue | 41 | 8 | 5 | 54 |
| [src/components/FuniFormV2/index.vue](/src/components/FuniFormV2/index.vue) | vue | 237 | 11 | 36 | 284 |
| [src/components/FuniGantt/demo2.vue](/src/components/FuniGantt/demo2.vue) | vue | 488 | 0 | 49 | 537 |
| [src/components/FuniGantt/index.vue](/src/components/FuniGantt/index.vue) | vue | 152 | 9 | 2 | 163 |
| [src/components/FuniGroupTitle/index.vue](/src/components/FuniGroupTitle/index.vue) | vue | 61 | 9 | 2 | 72 |
| [src/components/FuniHighlightCode/common.js](/src/components/FuniHighlightCode/common.js) | JavaScript | 26 | 9 | 1 | 36 |
| [src/components/FuniHighlightCode/index.css](/src/components/FuniHighlightCode/index.css) | CSS | 95 | 7 | 18 | 120 |
| [src/components/FuniHighlightCode/index.vue](/src/components/FuniHighlightCode/index.vue) | vue | 321 | 9 | 15 | 345 |
| [src/components/FuniHistogramChart/index.vue](/src/components/FuniHistogramChart/index.vue) | vue | 317 | 0 | 9 | 326 |
| [src/components/FuniIcon/index.vue](/src/components/FuniIcon/index.vue) | vue | 9 | 0 | 1 | 10 |
| [src/components/FuniIconSelect/assets/icons.json](/src/components/FuniIconSelect/assets/icons.json) | JSON | 1,397 | 0 | 1 | 1,398 |
| [src/components/FuniIconSelect/index.css](/src/components/FuniIconSelect/index.css) | CSS | 136 | 3 | 21 | 160 |
| [src/components/FuniIconSelect/index.vue](/src/components/FuniIconSelect/index.vue) | vue | 307 | 11 | 15 | 333 |
| [src/components/FuniIconSelect/search.css](/src/components/FuniIconSelect/search.css) | CSS | 166 | 1 | 34 | 201 |
| [src/components/FuniIconSelect/search.vue](/src/components/FuniIconSelect/search.vue) | vue | 86 | 10 | 3 | 99 |
| [src/components/FuniImage/index.vue](/src/components/FuniImage/index.vue) | vue | 33 | 0 | 1 | 34 |
| [src/components/FuniImageView/index.vue](/src/components/FuniImageView/index.vue) | vue | 37 | 9 | 2 | 48 |
| [src/components/FuniInputNumber/index.vue](/src/components/FuniInputNumber/index.vue) | vue | 182 | 0 | 18 | 200 |
| [src/components/FuniInputNumberRange/index.vue](/src/components/FuniInputNumberRange/index.vue) | vue | 56 | 0 | 9 | 65 |
| [src/components/FuniLabel/index.vue](/src/components/FuniLabel/index.vue) | vue | 81 | 9 | 5 | 95 |
| [src/components/FuniLineChart/index.vue](/src/components/FuniLineChart/index.vue) | vue | 383 | 9 | 6 | 398 |
| [src/components/FuniListPage/index.vue](/src/components/FuniListPage/index.vue) | vue | 322 | 9 | 34 | 365 |
| [src/components/FuniListPageV2/index.vue](/src/components/FuniListPageV2/index.vue) | vue | 367 | 6 | 37 | 410 |
| [src/components/FuniLog/index.vue](/src/components/FuniLog/index.vue) | vue | 132 | 9 | 9 | 150 |
| [src/components/FuniOperationLog/index.vue](/src/components/FuniOperationLog/index.vue) | vue | 69 | 1 | 11 | 81 |
| [src/components/FuniOrgSelect/config/config.jsx](/src/components/FuniOrgSelect/config/config.jsx) | JavaScript JSX | 419 | 50 | 38 | 507 |
| [src/components/FuniOrgSelect/index.vue](/src/components/FuniOrgSelect/index.vue) | vue | 161 | 9 | 11 | 181 |
| [src/components/FuniPageRender/index.vue](/src/components/FuniPageRender/index.vue) | vue | 33 | 9 | 0 | 42 |
| [src/components/FuniPickerMobile/index.vue](/src/components/FuniPickerMobile/index.vue) | vue | 89 | 9 | 2 | 100 |
| [src/components/FuniProcessBottomBtn/index.vue](/src/components/FuniProcessBottomBtn/index.vue) | vue | 114 | 3 | 3 | 120 |
| [src/components/FuniRUOC/index.vue](/src/components/FuniRUOC/index.vue) | vue | 216 | 9 | 10 | 235 |
| [src/components/FuniRUOC/org.vue](/src/components/FuniRUOC/org.vue) | vue | 240 | 9 | 16 | 265 |
| [src/components/FuniRUOC/role.vue](/src/components/FuniRUOC/role.vue) | vue | 180 | 9 | 6 | 195 |
| [src/components/FuniRUOC/style/itemBlock.css](/src/components/FuniRUOC/style/itemBlock.css) | CSS | 31 | 0 | 4 | 35 |
| [src/components/FuniRUOC/user.vue](/src/components/FuniRUOC/user.vue) | vue | 338 | 0 | 19 | 357 |
| [src/components/FuniRUOCLowCode/dialog/index.vue](/src/components/FuniRUOCLowCode/dialog/index.vue) | vue | 160 | 9 | 9 | 178 |
| [src/components/FuniRUOCLowCode/dialog/org/hooks/oneself.js](/src/components/FuniRUOCLowCode/dialog/org/hooks/oneself.js) | JavaScript | 29 | 9 | 1 | 39 |
| [src/components/FuniRUOCLowCode/dialog/org/hooks/orgHooks.js](/src/components/FuniRUOCLowCode/dialog/org/hooks/orgHooks.js) | JavaScript | 56 | 12 | 5 | 73 |
| [src/components/FuniRUOCLowCode/dialog/org/oneself.vue](/src/components/FuniRUOCLowCode/dialog/org/oneself.vue) | vue | 72 | 9 | 5 | 86 |
| [src/components/FuniRUOCLowCode/dialog/org/org.vue](/src/components/FuniRUOCLowCode/dialog/org/org.vue) | vue | 166 | 9 | 12 | 187 |
| [src/components/FuniRUOCLowCode/dialog/user/hooks/oneself.js](/src/components/FuniRUOCLowCode/dialog/user/hooks/oneself.js) | JavaScript | 21 | 9 | 0 | 30 |
| [src/components/FuniRUOCLowCode/dialog/user/hooks/orgUser.js](/src/components/FuniRUOCLowCode/dialog/user/hooks/orgUser.js) | JavaScript | 74 | 12 | 8 | 94 |
| [src/components/FuniRUOCLowCode/dialog/user/hooks/roleUser.js](/src/components/FuniRUOCLowCode/dialog/user/hooks/roleUser.js) | JavaScript | 40 | 9 | 4 | 53 |
| [src/components/FuniRUOCLowCode/dialog/user/oneself.vue](/src/components/FuniRUOCLowCode/dialog/user/oneself.vue) | vue | 72 | 9 | 4 | 85 |
| [src/components/FuniRUOCLowCode/dialog/user/org_user.vue](/src/components/FuniRUOCLowCode/dialog/user/org_user.vue) | vue | 154 | 9 | 9 | 172 |
| [src/components/FuniRUOCLowCode/dialog/user/role_user.vue](/src/components/FuniRUOCLowCode/dialog/user/role_user.vue) | vue | 158 | 9 | 13 | 180 |
| [src/components/FuniRUOCLowCode/hooks/config.js](/src/components/FuniRUOCLowCode/hooks/config.js) | JavaScript | 51 | 9 | 8 | 68 |
| [src/components/FuniRUOCLowCode/index.vue](/src/components/FuniRUOCLowCode/index.vue) | vue | 223 | 10 | 12 | 245 |
| [src/components/FuniRUOCLowCode/select/index.vue](/src/components/FuniRUOCLowCode/select/index.vue) | vue | 131 | 9 | 5 | 145 |
| [src/components/FuniRUOCLowCode/style/banner.css](/src/components/FuniRUOCLowCode/style/banner.css) | CSS | 32 | 0 | 4 | 36 |
| [src/components/FuniRUOCLowCode/style/checkbox.css](/src/components/FuniRUOCLowCode/style/checkbox.css) | CSS | 15 | 0 | 3 | 18 |
| [src/components/FuniRUOCLowCode/style/itemBlock.css](/src/components/FuniRUOCLowCode/style/itemBlock.css) | CSS | 31 | 0 | 4 | 35 |
| [src/components/FuniRUOCLowCode/style/userList.css](/src/components/FuniRUOCLowCode/style/userList.css) | CSS | 24 | 0 | 4 | 28 |
| [src/components/FuniRadarChart/index.vue](/src/components/FuniRadarChart/index.vue) | vue | 350 | 0 | 5 | 355 |
| [src/components/FuniRegion/Tmap.vue](/src/components/FuniRegion/Tmap.vue) | vue | 236 | 9 | 23 | 268 |
| [src/components/FuniRegion/amap_utils.js](/src/components/FuniRegion/amap_utils.js) | JavaScript | 99 | 15 | 13 | 127 |
| [src/components/FuniRegion/index.vue](/src/components/FuniRegion/index.vue) | vue | 320 | 9 | 15 | 344 |
| [src/components/FuniRegion/useRequest.js](/src/components/FuniRegion/useRequest.js) | JavaScript | 12 | 15 | 3 | 30 |
| [src/components/FuniRegion/utils.js](/src/components/FuniRegion/utils.js) | JavaScript | 46 | 24 | 6 | 76 |
| [src/components/FuniScatterplotChart/index.vue](/src/components/FuniScatterplotChart/index.vue) | vue | 403 | 0 | 7 | 410 |
| [src/components/FuniSearch/FuniSearchItem.vue](/src/components/FuniSearch/FuniSearchItem.vue) | vue | 120 | 0 | 3 | 123 |
| [src/components/FuniSearch/index.vue](/src/components/FuniSearch/index.vue) | vue | 166 | 0 | 8 | 174 |
| [src/components/FuniSearchForm/form.vue](/src/components/FuniSearchForm/form.vue) | vue | 212 | 11 | 32 | 255 |
| [src/components/FuniSearchForm/index.vue](/src/components/FuniSearchForm/index.vue) | vue | 101 | 0 | 10 | 111 |
| [src/components/FuniSearchFormV2/cell-render.jsx](/src/components/FuniSearchFormV2/cell-render.jsx) | JavaScript JSX | 68 | 3 | 5 | 76 |
| [src/components/FuniSearchFormV2/cell/searchFormDateItem.vue](/src/components/FuniSearchFormV2/cell/searchFormDateItem.vue) | vue | 21 | 0 | 4 | 25 |
| [src/components/FuniSearchFormV2/cell/searchFormInputItem.vue](/src/components/FuniSearchFormV2/cell/searchFormInputItem.vue) | vue | 4 | 0 | 1 | 5 |
| [src/components/FuniSearchFormV2/cell/searchFormInputNumberItem.vue](/src/components/FuniSearchFormV2/cell/searchFormInputNumberItem.vue) | vue | 4 | 0 | 1 | 5 |
| [src/components/FuniSearchFormV2/cell/searchFormInputNumberRangeItem.vue](/src/components/FuniSearchFormV2/cell/searchFormInputNumberRangeItem.vue) | vue | 46 | 0 | 7 | 53 |
| [src/components/FuniSearchFormV2/cell/searchFormRegionItem.vue](/src/components/FuniSearchFormV2/cell/searchFormRegionItem.vue) | vue | 32 | 0 | 4 | 36 |
| [src/components/FuniSearchFormV2/cell/searchFormSelectItem.vue](/src/components/FuniSearchFormV2/cell/searchFormSelectItem.vue) | vue | 27 | 0 | 6 | 33 |
| [src/components/FuniSearchFormV2/index.vue](/src/components/FuniSearchFormV2/index.vue) | vue | 124 | 0 | 15 | 139 |
| [src/components/FuniSearchFormV2/searchFormItem.vue](/src/components/FuniSearchFormV2/searchFormItem.vue) | vue | 126 | 0 | 15 | 141 |
| [src/components/FuniSearchFormV3/cell-render.jsx](/src/components/FuniSearchFormV3/cell-render.jsx) | JavaScript JSX | 30 | 3 | 5 | 38 |
| [src/components/FuniSearchFormV3/cell/searchFormAutocompleteItem.vue](/src/components/FuniSearchFormV3/cell/searchFormAutocompleteItem.vue) | vue | 163 | 0 | 25 | 188 |
| [src/components/FuniSearchFormV3/cell/searchFormCascaderItem.vue](/src/components/FuniSearchFormV3/cell/searchFormCascaderItem.vue) | vue | 61 | 0 | 8 | 69 |
| [src/components/FuniSearchFormV3/cell/searchFormDateItem.vue](/src/components/FuniSearchFormV3/cell/searchFormDateItem.vue) | vue | 21 | 0 | 4 | 25 |
| [src/components/FuniSearchFormV3/cell/searchFormInputItem.vue](/src/components/FuniSearchFormV3/cell/searchFormInputItem.vue) | vue | 8 | 0 | 1 | 9 |
| [src/components/FuniSearchFormV3/cell/searchFormInputNumberItem.vue](/src/components/FuniSearchFormV3/cell/searchFormInputNumberItem.vue) | vue | 4 | 0 | 1 | 5 |
| [src/components/FuniSearchFormV3/cell/searchFormInputNumberRangeItem.vue](/src/components/FuniSearchFormV3/cell/searchFormInputNumberRangeItem.vue) | vue | 58 | 0 | 9 | 67 |
| [src/components/FuniSearchFormV3/cell/searchFormRegionItem.vue](/src/components/FuniSearchFormV3/cell/searchFormRegionItem.vue) | vue | 55 | 0 | 7 | 62 |
| [src/components/FuniSearchFormV3/cell/searchFormSelectItem.vue](/src/components/FuniSearchFormV3/cell/searchFormSelectItem.vue) | vue | 80 | 0 | 15 | 95 |
| [src/components/FuniSearchFormV3/form.vue](/src/components/FuniSearchFormV3/form.vue) | vue | 265 | 11 | 40 | 316 |
| [src/components/FuniSearchFormV3/index.vue](/src/components/FuniSearchFormV3/index.vue) | vue | 150 | 0 | 17 | 167 |
| [src/components/FuniSearchRegion/index.vue](/src/components/FuniSearchRegion/index.vue) | vue | 79 | 0 | 8 | 87 |
| [src/components/FuniSelect/index.vue](/src/components/FuniSelect/index.vue) | vue | 137 | 9 | 5 | 151 |
| [src/components/FuniShareAction/ShareDialog.vue](/src/components/FuniShareAction/ShareDialog.vue) | vue | 92 | 9 | 6 | 107 |
| [src/components/FuniShareAction/ShareSuccess.vue](/src/components/FuniShareAction/ShareSuccess.vue) | vue | 62 | 0 | 7 | 69 |
| [src/components/FuniShareAction/index.vue](/src/components/FuniShareAction/index.vue) | vue | 205 | 9 | 7 | 221 |
| [src/components/FuniSiteFooter/index.vue](/src/components/FuniSiteFooter/index.vue) | vue | 52 | 0 | 7 | 59 |
| [src/components/FuniSiteHeader/index.vue](/src/components/FuniSiteHeader/index.vue) | vue | 44 | 1 | 7 | 52 |
| [src/components/FuniSvg/index.vue](/src/components/FuniSvg/index.vue) | vue | 15 | 0 | 5 | 20 |
| [src/components/FuniTeleport/index.vue](/src/components/FuniTeleport/index.vue) | vue | 23 | 0 | 6 | 29 |
| [src/components/FuniTreeSelect/index.vue](/src/components/FuniTreeSelect/index.vue) | vue | 14 | 0 | 3 | 17 |
| [src/components/FuniVCurd/index.vue](/src/components/FuniVCurd/index.vue) | vue | 49 | 8 | 14 | 71 |
| [src/components/FuniVariableSetter/BindVariableDialog.scss](/src/components/FuniVariableSetter/BindVariableDialog.scss) | SCSS | 76 | 0 | 11 | 87 |
| [src/components/FuniVariableSetter/BindVariableDialog.vue](/src/components/FuniVariableSetter/BindVariableDialog.vue) | vue | 136 | 3 | 11 | 150 |
| [src/components/FuniVariableSetter/CodemirrorEditor/extentions/PlaceholderTag.js](/src/components/FuniVariableSetter/CodemirrorEditor/extentions/PlaceholderTag.js) | JavaScript | 22 | 0 | 2 | 24 |
| [src/components/FuniVariableSetter/CodemirrorEditor/extentions/PlaceholderWidget.js](/src/components/FuniVariableSetter/CodemirrorEditor/extentions/PlaceholderWidget.js) | JavaScript | 28 | 0 | 2 | 30 |
| [src/components/FuniVariableSetter/CodemirrorEditor/index.scss](/src/components/FuniVariableSetter/CodemirrorEditor/index.scss) | SCSS | 24 | 0 | 3 | 27 |
| [src/components/FuniVariableSetter/CodemirrorEditor/index.vue](/src/components/FuniVariableSetter/CodemirrorEditor/index.vue) | vue | 74 | 0 | 13 | 87 |
| [src/components/FuniVariableSetter/hooks/useFunctionVariables.js](/src/components/FuniVariableSetter/hooks/useFunctionVariables.js) | JavaScript | 714 | 0 | 1 | 715 |
| [src/components/FuniVariableSetter/index.vue](/src/components/FuniVariableSetter/index.vue) | vue | 108 | 0 | 14 | 122 |
| [src/components/FuniVariableSetter/utils.js](/src/components/FuniVariableSetter/utils.js) | JavaScript | 28 | 0 | 3 | 31 |
| [src/components/FuniWorkRecord/index.vue](/src/components/FuniWorkRecord/index.vue) | vue | 139 | 9 | 12 | 160 |
| [src/components/FuniWrap/index.vue](/src/components/FuniWrap/index.vue) | vue | 20 | 0 | 2 | 22 |
| [src/components/index.js](/src/components/index.js) | JavaScript | 17 | 0 | 3 | 20 |
| [src/config/app.config/bpaas.js](/src/config/app.config/bpaas.js) | JavaScript | 4 | 0 | 1 | 5 |
| [src/config/app.config/index.js](/src/config/app.config/index.js) | JavaScript | 5 | 0 | 1 | 6 |
| [src/config/app.config/paas.js](/src/config/app.config/paas.js) | JavaScript | 4 | 0 | 1 | 5 |
| [src/config/layout.config.js](/src/config/layout.config.js) | JavaScript | 11 | 12 | 2 | 25 |
| [src/config/router.config/bpaas.js](/src/config/router.config/bpaas.js) | JavaScript | 16 | 1 | 3 | 20 |
| [src/config/router.config/index.js](/src/config/router.config/index.js) | JavaScript | 11 | 0 | 4 | 15 |
| [src/config/router.config/paas.js](/src/config/router.config/paas.js) | JavaScript | 11 | 0 | 3 | 14 |
| [src/hooks/useTheme.js](/src/hooks/useTheme.js) | JavaScript | 54 | 25 | 0 | 79 |
| [src/layout/components/Aside/index.vue](/src/layout/components/Aside/index.vue) | vue | 101 | 0 | 14 | 115 |
| [src/layout/components/Aside/useRenderMenuItem.jsx](/src/layout/components/Aside/useRenderMenuItem.jsx) | JavaScript JSX | 39 | 0 | 6 | 45 |
| [src/layout/components/Breadcrumb.vue](/src/layout/components/Breadcrumb.vue) | vue | 72 | 9 | 5 | 86 |
| [src/layout/components/Collapse.vue](/src/layout/components/Collapse.vue) | vue | 24 | 0 | 4 | 28 |
| [src/layout/components/Header/NavMenu/index.vue](/src/layout/components/Header/NavMenu/index.vue) | vue | 46 | 0 | 8 | 54 |
| [src/layout/components/Header/NavMenu/useRenderMenuItem.jsx](/src/layout/components/Header/NavMenu/useRenderMenuItem.jsx) | JavaScript JSX | 39 | 0 | 6 | 45 |
| [src/layout/components/Header/ToolBarLeft/ClientSwitch.vue](/src/layout/components/Header/ToolBarLeft/ClientSwitch.vue) | vue | 127 | 0 | 20 | 147 |
| [src/layout/components/Header/ToolBarLeft/Logo.vue](/src/layout/components/Header/ToolBarLeft/Logo.vue) | vue | 22 | 1 | 4 | 27 |
| [src/layout/components/Header/ToolBarLeft/index.vue](/src/layout/components/Header/ToolBarLeft/index.vue) | vue | 40 | 9 | 3 | 52 |
| [src/layout/components/Header/ToolBarRight/Shortcuts.vue](/src/layout/components/Header/ToolBarRight/Shortcuts.vue) | vue | 35 | 12 | 7 | 54 |
| [src/layout/components/Header/ToolBarRight/UserCenter/FuniREC/index.vue](/src/layout/components/Header/ToolBarRight/UserCenter/FuniREC/index.vue) | vue | 193 | 0 | 22 | 215 |
| [src/layout/components/Header/ToolBarRight/UserCenter/UserInfoModal.vue](/src/layout/components/Header/ToolBarRight/UserCenter/UserInfoModal.vue) | vue | 92 | 9 | 8 | 109 |
| [src/layout/components/Header/ToolBarRight/UserCenter/UserInfoModalEdit.vue](/src/layout/components/Header/ToolBarRight/UserCenter/UserInfoModalEdit.vue) | vue | 232 | 8 | 13 | 253 |
| [src/layout/components/Header/ToolBarRight/UserCenter/apis/log.js](/src/layout/components/Header/ToolBarRight/UserCenter/apis/log.js) | JavaScript | 30 | 40 | 9 | 79 |
| [src/layout/components/Header/ToolBarRight/UserCenter/apis/log/bpaas.js](/src/layout/components/Header/ToolBarRight/UserCenter/apis/log/bpaas.js) | JavaScript | 30 | 40 | 9 | 79 |
| [src/layout/components/Header/ToolBarRight/UserCenter/apis/log/index.js](/src/layout/components/Header/ToolBarRight/UserCenter/apis/log/index.js) | JavaScript | 5 | 0 | 1 | 6 |
| [src/layout/components/Header/ToolBarRight/UserCenter/apis/log/paas.js](/src/layout/components/Header/ToolBarRight/UserCenter/apis/log/paas.js) | JavaScript | 30 | 40 | 9 | 79 |
| [src/layout/components/Header/ToolBarRight/UserCenter/index.vue](/src/layout/components/Header/ToolBarRight/UserCenter/index.vue) | vue | 275 | 10 | 33 | 318 |
| [src/layout/components/Header/ToolBarRight/UserCenter/store/logStore.js](/src/layout/components/Header/ToolBarRight/UserCenter/store/logStore.js) | JavaScript | 56 | 34 | 8 | 98 |
| [src/layout/components/Header/ToolBarRight/index.vue](/src/layout/components/Header/ToolBarRight/index.vue) | vue | 20 | 9 | 2 | 31 |
| [src/layout/components/Header/index.vue](/src/layout/components/Header/index.vue) | vue | 25 | 9 | 7 | 41 |
| [src/layout/components/Main/index.vue](/src/layout/components/Main/index.vue) | vue | 37 | 0 | 6 | 43 |
| [src/layout/components/Main/useKeepAliveCache.js](/src/layout/components/Main/useKeepAliveCache.js) | JavaScript | 30 | 0 | 7 | 37 |
| [src/layout/components/MultiTab/index.vue](/src/layout/components/MultiTab/index.vue) | vue | 169 | 8 | 21 | 198 |
| [src/layout/components/theme/components/ColorGroup/index.vue](/src/layout/components/theme/components/ColorGroup/index.vue) | vue | 159 | 9 | 6 | 174 |
| [src/layout/components/theme/components/FormStyle/index.vue](/src/layout/components/theme/components/FormStyle/index.vue) | vue | 53 | 9 | 2 | 64 |
| [src/layout/components/theme/components/HeadColor/index.vue](/src/layout/components/theme/components/HeadColor/index.vue) | vue | 57 | 9 | 3 | 69 |
| [src/layout/components/theme/components/MenuColor/index.vue](/src/layout/components/theme/components/MenuColor/index.vue) | vue | 55 | 9 | 3 | 67 |
| [src/layout/components/theme/components/MenuLayout/index.vue](/src/layout/components/theme/components/MenuLayout/index.vue) | vue | 53 | 9 | 3 | 65 |
| [src/layout/components/theme/components/TableStyle/index.vue](/src/layout/components/theme/components/TableStyle/index.vue) | vue | 53 | 9 | 2 | 64 |
| [src/layout/components/theme/components/Title/index.vue](/src/layout/components/theme/components/Title/index.vue) | vue | 11 | 9 | 1 | 21 |
| [src/layout/components/theme/components/style/comm.css](/src/layout/components/theme/components/style/comm.css) | CSS | 53 | 0 | 9 | 62 |
| [src/layout/components/theme/components/svg/formBorder.svg.vue](/src/layout/components/theme/components/svg/formBorder.svg.vue) | vue | 295 | 0 | 1 | 296 |
| [src/layout/components/theme/components/svg/formDefault.svg.vue](/src/layout/components/theme/components/svg/formDefault.svg.vue) | vue | 167 | 0 | 1 | 168 |
| [src/layout/components/theme/components/svg/headColor.svg.vue](/src/layout/components/theme/components/svg/headColor.svg.vue) | vue | 62 | 9 | 1 | 72 |
| [src/layout/components/theme/components/svg/menuColor.svg.vue](/src/layout/components/theme/components/svg/menuColor.svg.vue) | vue | 68 | 9 | 1 | 78 |
| [src/layout/components/theme/components/svg/menuDefault.svg.vue](/src/layout/components/theme/components/svg/menuDefault.svg.vue) | vue | 29 | 9 | 1 | 39 |
| [src/layout/components/theme/components/svg/menuTop.svg.vue](/src/layout/components/theme/components/svg/menuTop.svg.vue) | vue | 22 | 9 | 1 | 32 |
| [src/layout/components/theme/components/svg/tableBorder.svg.vue](/src/layout/components/theme/components/svg/tableBorder.svg.vue) | vue | 320 | 0 | 1 | 321 |
| [src/layout/components/theme/components/svg/tableDefault.svg.vue](/src/layout/components/theme/components/svg/tableDefault.svg.vue) | vue | 303 | 0 | 2 | 305 |
| [src/layout/components/theme/hooks/setTheme.js](/src/layout/components/theme/hooks/setTheme.js) | JavaScript | 221 | 51 | 28 | 300 |
| [src/layout/components/theme/index.css](/src/layout/components/theme/index.css) | CSS | 25 | 0 | 5 | 30 |
| [src/layout/components/theme/index.vue](/src/layout/components/theme/index.vue) | vue | 81 | 11 | 7 | 99 |
| [src/layout/index.vue](/src/layout/index.vue) | vue | 103 | 9 | 13 | 125 |
| [src/layout/styles/aside.scss](/src/layout/styles/aside.scss) | SCSS | 26 | 0 | 6 | 32 |
| [src/layout/styles/header.scss](/src/layout/styles/header.scss) | SCSS | 82 | 4 | 18 | 104 |
| [src/layout/styles/index.scss](/src/layout/styles/index.scss) | SCSS | 10 | 10 | 4 | 24 |
| [src/layout/styles/logo.scss](/src/layout/styles/logo.scss) | SCSS | 23 | 1 | 4 | 28 |
| [src/layout/styles/multitab.scss](/src/layout/styles/multitab.scss) | SCSS | 95 | 0 | 18 | 113 |
| [src/layout/styles/nav_menu.scss](/src/layout/styles/nav_menu.scss) | SCSS | 99 | 0 | 18 | 117 |
| [src/layout/styles/root.scss](/src/layout/styles/root.scss) | SCSS | 20 | 0 | 3 | 23 |
| [src/layout/styles/var.scss](/src/layout/styles/var.scss) | SCSS | 89 | 0 | 8 | 97 |
| [src/layout/useLayoutStore.js](/src/layout/useLayoutStore.js) | JavaScript | 20 | 11 | 4 | 35 |
| [src/main.js](/src/main.js) | JavaScript | 41 | 9 | 10 | 60 |
| [src/plugins/index.js](/src/plugins/index.js) | JavaScript | 11 | 0 | 1 | 12 |
| [src/plugins/loadMap.js](/src/plugins/loadMap.js) | JavaScript | 15 | 1 | 1 | 17 |
| [src/plugins/userCenterRegister.jsx](/src/plugins/userCenterRegister.jsx) | JavaScript JSX | 31 | 9 | 3 | 43 |
| [src/router/index.js](/src/router/index.js) | JavaScript | 8 | 9 | 3 | 20 |
| [src/router/routes/common.js](/src/router/routes/common.js) | JavaScript | 16 | 9 | 1 | 26 |
| [src/router/routes/components.js](/src/router/routes/components.js) | JavaScript | 44 | 0 | 1 | 45 |
| [src/router/routes/demo.js](/src/router/routes/demo.js) | JavaScript | 40 | 9 | 2 | 51 |
| [src/router/routes/index.js](/src/router/routes/index.js) | JavaScript | 2 | 9 | 2 | 13 |
| [src/stores/useAppStore/bpaas.js](/src/stores/useAppStore/bpaas.js) | JavaScript | 225 | 10 | 23 | 258 |
| [src/stores/useAppStore/index.js](/src/stores/useAppStore/index.js) | JavaScript | 4 | 0 | 1 | 5 |
| [src/stores/useAppStore/paas.js](/src/stores/useAppStore/paas.js) | JavaScript | 111 | 10 | 14 | 135 |
| [src/stores/usePermissionStore.js](/src/stores/usePermissionStore.js) | JavaScript | 66 | 108 | 11 | 185 |
| [src/styles/element/colors.scss](/src/styles/element/colors.scss) | SCSS | 115 | 11 | 10 | 136 |
| [src/styles/element/font.scss](/src/styles/element/font.scss) | SCSS | 21 | 2 | 3 | 26 |
| [src/styles/element/index.scss](/src/styles/element/index.scss) | SCSS | 37 | 1 | 8 | 46 |
| [src/styles/element/shadow.scss](/src/styles/element/shadow.scss) | SCSS | 19 | 1 | 2 | 22 |
| [src/styles/element/var.scss](/src/styles/element/var.scss) | SCSS | 3 | 0 | 1 | 4 |
| [src/styles/h5/color.css](/src/styles/h5/color.css) | CSS | 10 | 0 | 1 | 11 |
| [src/styles/index.scss](/src/styles/index.scss) | SCSS | 3 | 0 | 0 | 3 |
| [src/styles/mixins/functions.scss](/src/styles/mixins/functions.scss) | SCSS | 33 | 7 | 9 | 49 |
| [src/styles/mixins/utils.scss](/src/styles/mixins/utils.scss) | SCSS | 32 | 6 | 8 | 46 |
| [src/styles/theme.scss](/src/styles/theme.scss) | SCSS | 17 | 8 | 4 | 29 |
| [src/userWorker.js](/src/userWorker.js) | JavaScript | 17 | 0 | 7 | 24 |
| [src/utils/appBootstrap.js](/src/utils/appBootstrap.js) | JavaScript | 10 | 0 | 1 | 11 |
| [src/utils/directive/auth.js](/src/utils/directive/auth.js) | JavaScript | 13 | 15 | 5 | 33 |
| [src/utils/directive/draggable.js](/src/utils/directive/draggable.js) | JavaScript | 9 | 9 | 3 | 21 |
| [src/utils/directive/index.js](/src/utils/directive/index.js) | JavaScript | 13 | 0 | 1 | 14 |
| [src/utils/env.js](/src/utils/env.js) | JavaScript | 13 | 6 | 2 | 21 |
| [src/utils/file/bpaas.js](/src/utils/file/bpaas.js) | JavaScript | 7 | 0 | 1 | 8 |
| [src/utils/file/index.js](/src/utils/file/index.js) | JavaScript | 4 | 0 | 1 | 5 |
| [src/utils/file/paas.js](/src/utils/file/paas.js) | JavaScript | 7 | 0 | 1 | 8 |
| [src/utils/functions.js](/src/utils/functions.js) | JavaScript | 9 | 0 | 4 | 13 |
| [src/utils/hooks/useAppsLoadFunc.js](/src/utils/hooks/useAppsLoadFunc.js) | JavaScript | 13 | 9 | 1 | 23 |
| [src/utils/hooks/useExportTable.js](/src/utils/hooks/useExportTable.js) | JavaScript | 58 | 14 | 12 | 84 |
| [src/utils/hooks/useForwardRef.js](/src/utils/hooks/useForwardRef.js) | JavaScript | 9 | 0 | 2 | 11 |
| [src/utils/hooks/useMultiTab.js](/src/utils/hooks/useMultiTab.js) | JavaScript | 13 | 16 | 6 | 35 |
| [src/utils/hooks/useMutationObserver.js](/src/utils/hooks/useMutationObserver.js) | JavaScript | 13 | 13 | 4 | 30 |
| [src/utils/hooks/useWatermark.js](/src/utils/hooks/useWatermark.js) | JavaScript | 68 | 8 | 9 | 85 |
| [src/utils/httpUtil.js](/src/utils/httpUtil.js) | JavaScript | 28 | 0 | 9 | 37 |
| [src/utils/index.js](/src/utils/index.js) | JavaScript | 41 | 9 | 6 | 56 |
| [src/utils/smUtil.js](/src/utils/smUtil.js) | JavaScript | 46 | 4 | 5 | 55 |
| [src/utils/symbols.js](/src/utils/symbols.js) | JavaScript | 5 | 0 | 1 | 6 |
| [src/utils/tenant.js](/src/utils/tenant.js) | JavaScript | 18 | 0 | 3 | 21 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)